
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.BoileParam;
import com.javaweb.system.entity.Dept;
import com.javaweb.system.mapper.BoileParamMapper;
import com.javaweb.system.query.DeptQuery;
import com.javaweb.system.service.IBoileParamService;
import com.javaweb.system.service.IDeptService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 锅炉参数 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/boileparam")
public class BoileParamController extends BaseController {

    @Autowired
    private IBoileParamService boileParamService;

    @Autowired
    private BoileParamMapper boileParamMapper;


    /**
     * 添加锅炉参数
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "运行参数", logType = LogType.INSERT)
    @PostMapping("/edit")
    public JsonResult edit(@RequestBody BoileParam entity) {
        return boileParamService.edit(entity);
    }

    /**
     * 获取最新运行数据
     *
     * @return
     */
    @GetMapping("/getBoileParamData")
    public JsonResult getBoileParamData() {
        return JsonResult.success(boileParamMapper.getBoileParamData());
    }

}
