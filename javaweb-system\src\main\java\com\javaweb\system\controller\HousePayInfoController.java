package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.HousePayInfo;
import com.javaweb.system.query.HouseInfoQuery;
import com.javaweb.system.query.HousePayInfoQuery;
import com.javaweb.system.service.IHouseInfoService;
import com.javaweb.system.service.IHousePayInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 缴费信息
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/housepayinfo")
public class HousePayInfoController extends BaseController {

    @Autowired
    private IHousePayInfoService housePayInfoService;

    /**
     * 获取查询列表
     * @param housePayInfoQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HousePayInfoQuery housePayInfoQuery) {
        return housePayInfoService.getList(housePayInfoQuery);
    }

    @PostMapping("/getUserPayInfo")
    public JsonResult getUserPayInfo(@RequestBody HouseInfo entity) {
        return housePayInfoService.getUserPayInfo(entity);
    }

    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "缴费信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HousePayInfo entity){
        return housePayInfoService.edit(entity);
    }

    @Log(title = "缴费信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HousePayInfo entity){
        return housePayInfoService.edit(entity);
    }

    @Log(title = "缴费信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public JsonResult delete(@PathVariable("ids") Integer[] ids){
        return housePayInfoService.deleteByIds(ids);
    }


}

