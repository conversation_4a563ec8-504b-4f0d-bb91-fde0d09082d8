<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HousePayInfoMapper">


    <!-- 通过热表编号获取个数-->
    <select id="getCountByNo" resultType="Integer">
        SELECT count(id) as num  FROM t_houseinfo  where heatmeterno=#{heatmeterno}
             <if test="id !=null">
                and id !=#{id}
             </if>
    </select>


    <select id="getHousePayInfoByIdEx" resultType="com.javaweb.system.entity.HousePayInfo">
        SELECT
            mt.id as id,
            c.name AS name,
            c.useheatno As useheatno,
            uf.floorname AS floorname,
            ut.floorunitname AS floorunitname,
            b.*,
            mt.*
        FROM
            `${tablename}` mt
                LEFT JOIN t_houseinfo b ON mt.HouseInfo_Id = b.id
                LEFT JOIN t_useheatunit c ON mt.useheatunit_id = c.id
                LEFT JOIN t_useheatunitfloor  uf ON b.UseHeatUnitFloor_id = uf.id
                LEFT JOIN t_useheatunitfloorunit  ut ON b.UseHeatUnitFloorUnit_id = ut.id
        WHERE  b.romno=#{romno} and mt.useheatunit_id=#{useheatunitId}  order by mt.id desc

    </select>
    <insert id="insertPayTable">
        insert into `${tablename}`(UseHeatUnit_Id, HouseInfo_Id, HeatYearPay, PayDT, PayAmount, FullPayAmount, NotPayAmount,
                                   WarmStatus, optuser, optdt,ispay)
        values(#{housePayInfo.useheatunitId}, #{housePayInfo.houseinfoId}, #{housePayInfo.heatyearpay}, #{housePayInfo.paydt},
               #{housePayInfo.payamount}, #{housePayInfo.fullpayamount}, #{housePayInfo.notpayamount},
               #{housePayInfo.warmstatus}, #{housePayInfo.optuser}, #{housePayInfo.optdt},#{housePayInfo.ispay})
    </insert>

    <!--<insert id="insertPayTable">-->
           <!--insert into `${tablename}`(UseHeatUnit_Id,HouseInfo_Id,HeatYearPay,PayDT,PayAmount,FullPayAmount,NotPayAmount,-->
           <!--WarmStatus,optuser,optdt) values(#{housePayInfo.useheatunitId},#{housePayInfo.houseinfoId},#{housePayInfo.heatyearpay},-->
                                            <!--#{housePayInfo.paydt},#{housePayInfo.payamount}, #{housePayInfo.fullpayamount},-->
                                            <!--#{housePayInfo.notpayamount},#{housePayInfo.warmstatus},-->
                                            <!--#{housePayInfo.optuser},#{housePayInfo.optdt});-->
           <!--}-->

    <!--</insert>-->
</mapper>
