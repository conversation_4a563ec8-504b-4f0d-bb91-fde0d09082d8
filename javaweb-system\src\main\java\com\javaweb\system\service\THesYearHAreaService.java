package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HesYearHArea;

/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 15:23
 */
public interface THesYearHAreaService extends IService<HesYearHArea> {

    /**
     * 获取监列表
     *
     * @return
     */

    JsonResult getHareaList();

    /**
     * 获得列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(HesYearHArea entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

}

