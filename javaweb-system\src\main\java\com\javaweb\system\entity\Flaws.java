
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 职级表
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_web_grquexian")
public class Flaws extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 投诉地址
     */
    private String quexianAddress;

    /**
     *紧急程度
     */
    private String jinjiLevel;

    /**
     * 缺陷描述
     */
    private String quexianContent;


    /**
     * 处理方案
     */
    private String jiejueFangan;

}
