package com.javaweb.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 室温采集
 * </p>
 *
 * @Date: 2022/12/12 14:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hes_indoort")
public class THarvest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属小区
     */
    private int useheatunitid;
    /**
     * 小区名字
     */
    @TableField(exist=false)
    private String useheatunitName;

    /**
     * 所属换热站
     */
    private Integer hescode;

    /**
     * 换热站名字
     */
    @TableField(exist=false)
    private String name;
    /**
     * 楼宇号
     */
    private String floorname;

    /**
     * 单元号
     */
    private String floornum;

    /**
     * 户号
     */
    private String housenum;

    /**
     * 采集温度
     */
    private String installT;
    /**
     * 户外温度
     */
    private String outdoorT;

    /**
     *采集时间
     */
    private Date installDt;


    @TableField(exist=false)
    private String sdt;

    /**
     * 附件
     */
    private String floorpic;

    /**
     * 操作人员
     */
    private int uid;


    /**
     * 操作用户
     */
    private String username;

    /**
     * 经度
     */
    private String lon;

    /**
     * 温度
     */
    private String lat;

    /**
     *操作时间
     */
    private Date createDt;

    private String mm;
}

