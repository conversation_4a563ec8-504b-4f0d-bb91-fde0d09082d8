package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.RepairList;
import com.javaweb.system.entity.RepairSon;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.RepairListMapper;
import com.javaweb.system.mapper.RepairSonMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.RepairListQuery;
import com.javaweb.system.service.IRepairListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 维修列表 服务实现类
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Service
public class RepairListServiceIml extends ServiceImpl<RepairListMapper, RepairList> implements IRepairListService {

    @Autowired
    private RepairListMapper repairlistMapper;

    @Autowired
    private RepairSonMapper repairsonMapper;


    @Autowired
    private THesMapper thesMapper;
    /**
     * 获取维修列表
     *
     * @param repairlistQuery 查询条件
     * @return
     */
    @Override
    public JsonResult getList(RepairListQuery repairlistQuery) {

        // 查询条件
        QueryWrapper<RepairList> queryWrapper = new QueryWrapper<>();

        // 换热站ID
        if (!StringUtils.isEmpty(repairlistQuery.getHesId())) {
            queryWrapper.eq("hes_id", repairlistQuery.getHesId());
        }

        int nPage = 1;
        int nLimit = 10;

        if(null!=repairlistQuery.getPage()) {
            nPage = repairlistQuery.getPage();
        }
        if(null!=repairlistQuery.getLimit()) {
            nLimit = repairlistQuery.getLimit();
        }
        // 查询分页数据
        IPage<RepairList> page = new Page<>(nPage,nLimit);
        IPage<RepairList> pageData = repairlistMapper.selectPage(page, queryWrapper);

        pageData.convert(x -> {
            RepairList repairlist = Convert.convert(RepairList.class, x);

            // 维修人员
            if (StringUtils.isNotNull(x.getRepairId())) {
                RepairSon repairsonInfo = repairsonMapper.selectById(x.getRepairId());
                if (StringUtils.isNotNull(repairsonInfo)) {
                    repairlist.setRepairsonName(repairsonInfo.getName());
                }
            }
            // 换热站名称
            if (StringUtils.isNotNull(x.getHesId())) {
                THes hesInfo = thesMapper.selectById(x.getHesId());
                if (StringUtils.isNotNull(hesInfo)) {
                    repairlist.setHesname(hesInfo.getName());
                }
            }

            return repairlist;
        });
        return JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(RepairList entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }

        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    /**
     * 根据用户ID删除用户
     *
     * @param ids 记录ID
     * @return
     */
    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0

        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

}
