package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 维修人员表
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_repairinfo")
public class RepairSon extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 维修人员名称
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 地址
     */
    private String address;

    /**
     * 就职状态 状态：0未离职 1已离职
     */
    private Integer state;

}
