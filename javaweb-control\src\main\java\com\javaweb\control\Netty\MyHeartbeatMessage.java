package com.javaweb.control.Netty;

public class MyHeartbeatMessage {
    private char[] strBegin = new char[8];
    private int hesCode;
    private char[] strEnd = new char[8];
    private boolean isValidity;

    // getters and setters
    public char[] getStrBegin() {
        return strBegin;
    }

    public void setStrBegin(char[] strBegin) {
        this.strBegin = strBegin;
    }
    public int getHesCode() { return hesCode; }
    public void setHesCode(int hesCode) {
        this.hesCode = hesCode;
    }

    public char[] getStrEnd() { return strEnd; }
    public void setStrEnd(char[] strEnd) {
        this.strEnd = strEnd;
    }
    public void setIsValidity(boolean bState){ this.isValidity = bState; }
    public boolean getIsValidity(){ return this.isValidity; }
}