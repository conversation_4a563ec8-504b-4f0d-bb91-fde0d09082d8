package com.javaweb.api.Netty;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class MyHeartbeatMessageDecoder extends ByteToMessageDecoder {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {

        int nlen = in.readableBytes();
        byte[] bbuff = new byte[nlen];
        in.readBytes(bbuff);
        MyHeartbeatMessage myHeartbeatMessage = new MyHeartbeatMessage();
        if(nlen < 20){
            myHeartbeatMessage.setIsValidity(false);
            out.add(myHeartbeatMessage);
            return ;
        }

        byte[] strBegin = new byte[8];
        System.arraycopy(bbuff, 0, strBegin, 0, 8);

        byte[] strhescode = new byte[4];
        System.arraycopy(bbuff, 8, strhescode, 0, 4);

        byte[] strEnd = new byte[8];
        System.arraycopy(bbuff, 12, strEnd, 0, 8);

        String strTemp = new String(strBegin, StandardCharsets.UTF_8); // or other charset if not UTF-8
        String sBegin = "PANN0517";
        if(!strTemp.equals(sBegin)){
            myHeartbeatMessage.setIsValidity(false);
            out.add(myHeartbeatMessage);
            return ;
        }
        char[] cBegin = strTemp.toCharArray();
        myHeartbeatMessage.setStrBegin(cBegin);

        int hesCode = ByteBuffer.wrap(strhescode).order(ByteOrder.LITTLE_ENDIAN).getInt();
        myHeartbeatMessage.setHesCode(hesCode);

        strTemp = new String(strEnd, StandardCharsets.UTF_8); // or other charset if not UTF-8
        String sEnd = "DENG0304";
        if(!strTemp.equals(sEnd)){
            myHeartbeatMessage.setIsValidity(false);
            out.add(myHeartbeatMessage);
            return ;
        }
        char[] cEnd= strTemp.toCharArray();
        myHeartbeatMessage.setStrEnd(cEnd);

        myHeartbeatMessage.setIsValidity(true);
        out.add(myHeartbeatMessage);
    }
}
