
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.Customers;
import com.javaweb.system.entity.Flaws;
import com.javaweb.system.mapper.CustomersMapper;
import com.javaweb.system.query.CustomersQuery;
import com.javaweb.system.service.ICustomersService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 职级表 服务实现类
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@Service
public class CustomersServiceImpl extends BaseServiceImpl<CustomersMapper, Customers> implements ICustomersService {

    @Autowired
    private CustomersMapper customersMapper;

    /**
     * 获取职级列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(CustomersQuery customersQuery) {
        // 查询条件
        QueryWrapper<Customers> queryWrapper = new QueryWrapper<>();
        // 地址
        if (!StringUtils.isEmpty(customersQuery.getTousuAddress())) {
            queryWrapper.like("tousu_address", customersQuery.getTousuAddress());
        }

        queryWrapper.orderByDesc("id");

        int nPage = 1;
        int nLimit = 10;

        if(null!=customersQuery.getPage()) {
            nPage = customersQuery.getPage();
        }
        if(null!=customersQuery.getLimit()) {
            nLimit = customersQuery.getLimit();
        }
        // 查询分页数据
        IPage<Customers> page = new Page<>(nPage,nLimit);
        IPage<Customers> pageData = customersMapper.selectPage(page, queryWrapper);

        System.out.println(pageData);
        return JsonResult.success(pageData);



    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(Customers entity) {
        if (StringUtils.isNotNull(entity.getId()) && entity.getId() > 0) {
            entity.setUid(ShiroUtils.getUserId());
            entity.setUpdateTime(DateUtils.now());
        } else {
            entity.setUid(ShiroUtils.getUserId());
            entity.setUpdateTime(DateUtils.now());
        }
        return super.edit(entity);
    }


}
