package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


/**
 *
 *
 * <p>
 *  报警记录
 * </p>
 *
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_alarm")
public class Recode  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 监测站编号
     */
    private Integer hescode;

    /**
     * 监测站名称
     */
    @TableField(exist=false)
    private String hesname;

    /**
     * 报警字段
     */
    private String alarmField;

    /**
     * 报警值
     */
    private  String alarmValue;

    /**
     * 最大值
     */
    private  String alarmMaxval;


    /**
     * 最小值
     */
    private  String alarmMinval;

    /**
     * 是否报警
     */
    private  Integer isalarm;


    /**
     * 是否处理
     */
    private  Integer ishandle;


    /**
     * 报警类型
     */
    private  Integer alarmType;
    /**
     * 规则id
     */
    private  Integer ruleId;

    /**
     * 报警类型字典id
     */
    private  Integer dictId;

    /**
     * 报警等级
     */
    private  Integer alarmLevel;

    /**
     * 字段单位
     */
    private String unit;

    /**
     * 报警时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date alarmDt;

}
