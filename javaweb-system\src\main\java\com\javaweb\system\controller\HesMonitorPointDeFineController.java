package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.ExcelUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.dto.MonitorDto;
import com.javaweb.system.entity.HesMonitorPointDeFine;
import com.javaweb.system.mapper.HesMonitorPointDeFineMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.HesMonitorPointDeFineQuery;
import com.javaweb.system.service.THesMonitorPointDeFineService;
import com.javaweb.system.vo.monitor.MonitorInfoVo;
//import com.sun.deploy.net.URLEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *  测点配置
 * </p>
 *
 * @Date: 2022/12/16 17:30
 */
@RestController
@RequestMapping("/monitor")
public class HesMonitorPointDeFineController extends BaseController {

    @Autowired
    private THesMonitorPointDeFineService hesMonitorPointDeFineService;

    @Autowired
    private HesMonitorPointDeFineMapper hesMonitorPointDeFineMapper;
    @Autowired
    private THesMapper hesMapper;

    @GetMapping("/index")
    public JsonResult index(HesMonitorPointDeFineQuery hesMonitorPointDeFineQuery){
        return hesMonitorPointDeFineService.getList(hesMonitorPointDeFineQuery);

    }

    @Log(title = "测点配置",logType = LogType.DELETE)
    @DeleteMapping("delete/{Ids}")
    public JsonResult delete(@PathVariable("Ids") Integer[] Ids){
        return hesMonitorPointDeFineService.deleteByIds(Ids);
    }

    @GetMapping("creatTable/{hescode}")
    public JsonResult creatTable(@PathVariable int hescode) throws SQLException {
        return hesMonitorPointDeFineService.creatDataBaseTable(hescode);
    }


    @GetMapping("/setHesFieldSetting/{Ids}/{hescode}")
    public JsonResult setHesFieldSetting(@PathVariable("Ids") Integer[] Ids,@PathVariable("hescode") int hescode) {
        return hesMonitorPointDeFineService.setHesFieldSetting(Ids,hescode);
    }


    @Log(title = "测点配置", logType = LogType.STATUS)
    @PutMapping("/controlstatus")
    public JsonResult controlstatus(@RequestBody HesMonitorPointDeFine entity){
        return hesMonitorPointDeFineService.setStatus(entity);

    }

    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "测点配置", logType = LogType.STATUS)
    @PutMapping("/status")
    public JsonResult bjstate(@RequestBody HesMonitorPointDeFine entity) {
        return hesMonitorPointDeFineService.setStatus(entity);
    }

    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "测点配置", logType = LogType.STATUS)
    @PutMapping("/usedstatus")
    public JsonResult showstatus(@RequestBody HesMonitorPointDeFine entity) {
        return hesMonitorPointDeFineService.setStatus(entity);
    }

    /**
     * 导入Excel
     *
     * @param request 网络请求
     * @return
     */
    @Log(title = "测点配置", logType = LogType.IMPORT)
    @PostMapping("/importExcel/{name}")
    public JsonResult importExcel(HttpServletRequest request, @PathVariable("name") String name) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        return hesMonitorPointDeFineService.importExcel(request, name);
    }

    /**
     * 导出Excel
     *
     * @param hescode 站编号
     * @return
     */
    @PostMapping("/exportExcel/{hescode}")
    public JsonResult exportExcel(@PathVariable Integer hescode) {
        List<MonitorInfoVo> monitorVoList = hesMonitorPointDeFineService.exportExcel(hescode);
        ExcelUtils<MonitorInfoVo> excelUtils = new ExcelUtils<MonitorInfoVo>(MonitorInfoVo.class);
        return excelUtils.exportExcel(monitorVoList, "测点配置列表");
    }

    /**
     * 导出txt
     *
     * @param hescode 站编号
     * @return
     */
    @GetMapping("/exportTxt/{hescode}")
    public ResponseEntity<Resource> exportToTxt(@PathVariable Integer hescode) throws IOException {

        List<MonitorInfoVo> list = hesMonitorPointDeFineService.exportExcel(hescode);
        String hesname= hesMapper.getHesName(hescode);
        // 将所有 mpfielddefine 拼接成一个以逗号分隔的字符串
        String mpfielddefineStr = list.stream()
                .map(MonitorInfoVo::getMpfielddefine)
                .filter(Objects::nonNull) // 确保没有 null 值
                .collect(Collectors.joining(","));
        //删除数据
        hesMonitorPointDeFineMapper.delFieldDefine(hescode);
        //插入数据
        hesMonitorPointDeFineMapper.insertFieldDefineByhescode(hescode,mpfielddefineStr);
        // 创建TXT文件
        File file = File.createTempFile(hesname+"_hes_point_config", ".txt");
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file)))
        {

            writer.write(hesname+","+String.valueOf(hescode));
            writer.newLine();
             for (MonitorInfoVo data : list)
            {
                String mpdesc=data.getMpdesc();
                String mpfielddefine=data.getMpfielddefine();
                String line=mpdesc+","+mpfielddefine;

                writer.write(line);
                writer.newLine();
            }
        }

        // 设置HTTP响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", file.getName());
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        // 返回文件流
        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(new FileInputStream(file)));
    }


    @GetMapping("/getStationFieldSet/{hescode}")
    public JsonResult getStationFieldSet(@PathVariable int hescode)
    {
        return hesMonitorPointDeFineService.getHesFieldSetting(hescode);
    }

    @GetMapping("/getMonitorList")
    public JsonResult getMonitorList(){
        return hesMonitorPointDeFineService.getHesMonitorPointDeFineList();
    }


    //通过编号得到换热站配置字段
    @GetMapping("/getDictDataByCode/{hescode}")
    public JsonResult getDictDataByCode(@PathVariable Integer hescode)
    {
        return JsonResult.success(hesMonitorPointDeFineService.getDictDataByCode(hescode));
    }


    /*
    *
    * 根据其他站配置好的测点进行配置
    * */
    @GetMapping("/setHesFieldByOtherCode/{Ids}/{sourceHesCode}/{targetHesCode}")
    public JsonResult setHesFieldByOtherCode(@PathVariable("Ids") Integer[] Ids,
                                      @PathVariable("sourceHesCode") int sourceHesCode,
                                      @PathVariable("targetHesCode") int targetHesCode) {
        return hesMonitorPointDeFineService.setHesFieldByOtherCode(Ids,sourceHesCode,targetHesCode);
    }
    @GetMapping("/getDictDataAllList")
    public JsonResult getDictDataAllList() {
        List<MonitorDto>  lst=hesMonitorPointDeFineMapper.getDictDataAllList();
        return JsonResult.success(lst);
    }



}

