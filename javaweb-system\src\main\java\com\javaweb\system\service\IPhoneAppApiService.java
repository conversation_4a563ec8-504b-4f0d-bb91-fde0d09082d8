package com.javaweb.system.service;


import com.javaweb.common.utils.JsonResult;

public interface IPhoneAppApiService {
    /**
    * 获取换热站列表
    *
    * @return
    */
    JsonResult getHesList();

    /**
     * 获取小区列表
     *
     * @return
     */
    JsonResult getHesUnitList();

    /**
     * 获取采集温度列表
     * @param  nLevel  权限级别   name 名字
     * @return
     */
    JsonResult getIndoorDataList(Integer nLevel,String name);

    /**
    * 获取换热站最新数据
    *
    * @return
    */
    JsonResult getHesData(String hescode);
    /**
    * 获取换热站历史数据
    *
    * @return
    */
    JsonResult getHesHistoriesData(Integer hescode,String dt,String field);
    /**
    * 获取气象站数据
    *
    * @return
    */
    JsonResult getWsData();
    /**
     * 获取气象站历史数据
     *
     * @return
     */
    JsonResult getWsHistoriesData(String dt);


    /**
    * 获取气象站历史数据
    *
    * @return
    */
    JsonResult getWsHistoryData();
    /**
    * 登录验证
    *
    * @return
    */
    JsonResult uLogin(String phone,String password);
    /**
    * 注册验证
    *
    * @return
    */
    JsonResult regVer(String phone,String password);
    /**
    * 修改密码
    *
    * @return
    */
    JsonResult modifyPwd(Integer id,String password);


    JsonResult updateUserLocation(String phone, String lon,String lat);
    /**
    * 个人信息
    *
    * @return
    */
    JsonResult getPersonalInfo(String userId);
    /**
     * 白名单列表
     *
     * @return
     */
    JsonResult whitelistList(Integer id,Integer nlevel);

    /**
     * 白名单添加
     *
     * @return
     */
    JsonResult whiteUserAdd(String name,String phone);

    /**
     * 白名单编辑
     *
     * @return
     */
    JsonResult whiteUserEdit(Integer id,Integer nlevel);
    /**
     * 计算参数
     *
     * @return
     */
    JsonResult calcParam(String hescode);
    /**
     * 换热站供回水数据
     *
     * @return
     */
    JsonResult hesReflowData(String hescode,String dt);


    /**
     * 获取换热站运行模式算法
     *
     * @return
     */
    JsonResult getHesRunModeAlg(String hescode);

    /**
     * 设置换热站运行模式算法
     *
     * @return
     */
    JsonResult setHesRunModeAlg(String hescode,String type,Integer setval);


    /**
     * 换热站控制字段
     *
     * @return
     */
    JsonResult hesControlFields(String hescode);

    /**
     * 换热站指定字段值
     *
     * @return
     */
    JsonResult hesFieldData(String hescode,String field);

    /**
     * 换热站配置字段
     *
     * @return
     */
    JsonResult hesConfigFields(String hescode);
}
