package com.javaweb.control.Netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;

@Component
public class NettyServer implements CommandLineRunner {

    private final EventLoopGroup bossGroup;
    private final EventLoopGroup workerGroup;

    @Value("${controlServer.ip}")
    private String sIP;

    @Value("${controlServer.port}")
    private int nPort;

    @Autowired
    private ApplicationContext context;//上下文


    public NettyServer() {
        this.bossGroup = new NioEventLoopGroup();
        this.workerGroup = new NioEventLoopGroup();
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            // 启动TCP服务
            ServerBootstrap serverBootstrap = new ServerBootstrap();
            serverBootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            pipeline.addLast(new MyHeartbeatMessageDecoder());
                            pipeline.addLast(new MyControlMessageEncoder());
                            //pipeline.addLast(new TcpServerHandler());
                            // 为每个连接创建一个新的TcpServerHandler实例
                            TcpServerHandler tcpServerHandler = context.getBean(TcpServerHandler.class);
                            pipeline.addLast(tcpServerHandler);

                        }
                    });

            // Bind and start to accept incoming connections.
            ChannelFuture f = serverBootstrap.bind(sIP, nPort).sync();
            InetSocketAddress address = (InetSocketAddress) f.channel().localAddress();
            String ipAddressEx = address.getAddress().getHostAddress();
            System.out.println("bind ip:" + ipAddressEx + "  port: " + nPort + " is successfully!");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Failed to start Netty server", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
    }
}