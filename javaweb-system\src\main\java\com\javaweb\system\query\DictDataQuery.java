
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;

/**
 * 字典数据查询条件
 */
@Data
public class DictDataQuery extends BaseQuery {

    /**
     * 字典项名称
     */
    private String name;

    /**
     * 字典编码
     */
    private String code;

    /**
     * 字典ID
     */
    private Integer dictId;

}
