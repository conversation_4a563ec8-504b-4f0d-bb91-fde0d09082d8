package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <p>
 *  报警配置
 * </p>
 *
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_alarmconfig")
public class AlarmConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 换热站编号
     */
    private Integer hescode;

    /**
     * 换热站名称
     */
    private String hesname;

    /**
     *  告警规则
     */
    private String alarmRulesIds;

    /**
     * 运行规则
     */
    private Integer runRulesIds;


    /**
     * 是否启用
     */
    private Integer isUsed;
//
//    /*
//     * 运行模板
//     * */
//    @TableField(exist=false)
//    private RunRules runRuleTemplate;
//
//    /*
//     * 告警模板
//     * */
//    @TableField(exist=false)
//    private List<Rules> alarmRuleTemplates;


}
