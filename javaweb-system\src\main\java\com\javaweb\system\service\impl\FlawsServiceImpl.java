
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.config.UploadFileConfig;
import com.javaweb.common.utils.*;
import com.javaweb.system.entity.Flaws;
import com.javaweb.system.entity.Level;
import com.javaweb.system.mapper.FlawsMapper;
import com.javaweb.system.mapper.LevelMapper;
import com.javaweb.system.query.FlawsQuery;
import com.javaweb.system.query.LevelQuery;
import com.javaweb.system.service.IFlawsService;
import com.javaweb.system.utils.ShiroUtils;
import com.javaweb.system.vo.level.LevelInfoVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 职级表 服务实现类
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@Service
public class FlawsServiceImpl extends BaseServiceImpl<FlawsMapper, Flaws> implements IFlawsService {

    @Autowired
    private FlawsMapper flawsMapper;

    /**
     * 获取职级列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(FlawsQuery flawsQuery) {
        // 查询条件
        QueryWrapper<Flaws> queryWrapper = new QueryWrapper<>();
        // 紧急程度
        if (!StringUtils.isEmpty(flawsQuery.getJinjiLevel())) {
            queryWrapper.like("jinji_level", flawsQuery.getJinjiLevel());
        }

        queryWrapper.orderByDesc("id");

        int nPage = 1;
        int nLimit = 10;

        if(null!=flawsQuery.getPage()) {
            nPage = flawsQuery.getPage();
        }
        if(null!=flawsQuery.getLimit()) {
            nLimit = flawsQuery.getLimit();
        }
        // 查询分页数据
        IPage<Flaws> page = new Page<>(nPage,nLimit);
        IPage<Flaws> pageData = flawsMapper.selectPage(page, queryWrapper);

        System.out.println(pageData);
        return JsonResult.success(pageData);



    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(Flaws entity) {
        if (StringUtils.isNotNull(entity.getId()) && entity.getId() > 0) {
            entity.setUpdateUser(ShiroUtils.getUserId());
            entity.setUpdateTime(DateUtils.now());
        } else {
            entity.setCreateUser(ShiroUtils.getUserId());
            entity.setCreateTime(DateUtils.now());
        }
        return super.edit(entity);
    }


}
