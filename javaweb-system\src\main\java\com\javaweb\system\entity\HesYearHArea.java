package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 15:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hes_year_harea")
public class HesYearHArea implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 名称
     */
    private String hesname;

    /**
     * 分区
     */
    private Integer hescode;

    /**
     * 使用年
     */
    private Integer hyear;

    /**
     * 供热面积
     */
    private String heara;

    /**
     * 未供热面积
     */
    private Double freearea;

    /**
     * 供热月份
     */
    private Integer hmonth;

}

