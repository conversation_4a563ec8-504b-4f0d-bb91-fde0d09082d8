<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.calculate.mapper.StatisticsHourMapper">

        <!-- 获取个数 -->
        <select id="getNumByDt" resultType="Integer">
                SELECT count(id) FROM t_hes_statistics_hour where hescode=#{hescode} and dt=#{dt};
        </select>


</mapper>
