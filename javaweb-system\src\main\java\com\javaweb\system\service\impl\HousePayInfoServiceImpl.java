package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.HousePayInfo;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.HouseInfoMapper;
import com.javaweb.system.mapper.HousePayInfoMapper;
import com.javaweb.system.query.HousePayInfoQuery;
import com.javaweb.system.service.IHousePayInfoService;
import com.javaweb.system.utils.ShiroUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * <p>
 *  住户缴费信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HousePayInfoServiceImpl extends ServiceImpl<HousePayInfoMapper, HousePayInfo> implements IHousePayInfoService {

    @Autowired
    HousePayInfoMapper housePayInfoMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private HeatUnitMapper heatUnitMapper;


    @Autowired
    DataSource dataSource;

    /**
     * 查询小区缴费记录
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        HousePayInfoQuery housePayInfoQuery =(HousePayInfoQuery) query;

        //查询条件
        String where="";
        String where1="";
        String unitNo="";
        //小区名称
        if(!StringUtils.isNull(housePayInfoQuery.getUseheatunitId()))
        {
            //根据小区id 获得编号
            unitNo=heatUnitMapper.getHeatUnitNoById(housePayInfoQuery.getUseheatunitId());
            where += " where useheatunit_id=" + housePayInfoQuery.getUseheatunitId();
            where1 += " where mt.useheatunit_id=" + housePayInfoQuery.getUseheatunitId();
        }

//        if(StringUtils.isNotNull(housePayInfoQuery.getHeatyearpay()))
//        {
//            where1 += " and mt.heatyearpay=" + housePayInfoQuery.getHeatyearpay();
//        }
//        if(StringUtils.isNotEmpty(housePayInfoQuery.getRomno()))
//        {
//            where1 += " and b.romno = '" + housePayInfoQuery.getRomno()+"'";
//        }
//        if(StringUtils.isNotEmpty(housePayInfoQuery.getHousemaster()))
//        {
//            where1 += " and b.housemaster = '" + housePayInfoQuery.getHousemaster()+"'";
//        }

        Integer npage = housePayInfoQuery.getPage() != null ? housePayInfoQuery.getPage() : 1;
        Integer nlimit = housePayInfoQuery.getLimit() != null ? housePayInfoQuery.getLimit() : 10;
        IPage<HousePayInfo> page = new Page<>(npage, nlimit, 0);
        if(!unitNo.isEmpty())
        {
            try{
                // 创建分页对象
                String tableName="t_housepayinfo_"+unitNo;
                // 查询总行数
                String countSql = "SELECT COUNT(id) FROM " + tableName + where;
                Integer totalCount = jdbcTemplate.queryForObject(countSql, Integer.class);

                String sql="SELECT c.name AS name," +
                        "b.romno AS romno, " +
                        "b.floorno AS floorno,"+
                        "b.housemaster AS housemaster,"+
                        "b.housemaster AS housemaster,"+
                        "b.billablearea AS billablearea,"+
                        "b.builtarea AS billablearea,"+
                        "uf.floorname AS floorname," +
                        "ut.floorunitname AS floorunitname, " +
                        "c.unitno AS unitno, " +
                        "mt.*  FROM "+tableName+" mt " +
                        "LEFT JOIN t_houseinfo b  ON mt.HouseInfo_Id = b.id " +
                        "LEFT JOIN t_useheatunit c ON mt.useheatunit_id = c.id " +
                        "LEFT JOIN t_useheatunitfloor  uf ON b.useHeatUnitFloor_id = uf.id  " +
                        "LEFT JOIN t_useheatunitfloorunit  ut ON b.useHeatUnitFloorUnit_id = ut.id " + where1;

                sql += " ORDER BY mt.id DESC LIMIT " + (npage - 1) * nlimit + ", " + nlimit;

                List<HousePayInfo> records = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(HousePayInfo.class));

                // 返回分页结果
                page = new Page<>(npage, nlimit, totalCount);
                page.setRecords(records);

                return  JsonResult.success(page);
            }catch (Exception e)
            {
                System.out.println(e.getMessage());
                return  JsonResult.success(page);
            }
        }

        return  JsonResult.success(page);
    }



    /**
     * 查询缴费记录
     * @param
     * @return
     */
    @Override
    public JsonResult getUserPayInfo(HouseInfo houseinfo) {
        String unitNo="";
        //HousePayInfoQuery housePayInfoQuery =(HousePayInfoQuery) query;
        //小区名称
        if(StringUtils.isNull(houseinfo.getUseheatunitId())){
            return  JsonResult.error("请选择小区");
        }
        //根据小区id 获得编号
        unitNo=heatUnitMapper.getHeatUnitNoById(houseinfo.getUseheatunitId());
        String tableName="t_housepayinfo_"+unitNo;
        if(StringUtils.isNull(houseinfo.getRomno().trim())){
            return  JsonResult.error("请输入房号");
        }

        //1.根据搜索条件得到住户信息
        //2.根据住户id 信息得到住户的缴费信息，（若是缴费则返回对应的缴费记录信息）
//        HousePayInfo houseInfo=housePayInfoMapper.getHousePayInfoById(houseinfo.getUseheatunitId(),houseinfo.getRomno());
//
//        //缴费信息
//        HousePayInfo housePayInfo=housePayInfoMapper.getHousePayInfoByTableName(houseInfo.getId(),tableName);
//        houseInfo.setIspay(0);
//        if(housePayInfo !=null)
//        {
//            houseInfo.setIspay(1);
//            houseInfo.setPaydt(housePayInfo.getPaydt());
//            houseInfo.setPayamount(housePayInfo.getPayamount());
//            houseInfo.setFullpayamount(housePayInfo.getFullpayamount());
//            houseInfo.setNotpayamount(housePayInfo.getNotpayamount());
//        }
         List<HousePayInfo> houseInfo=housePayInfoMapper.getHousePayInfoByIdEx(houseinfo.getUseheatunitId(),houseinfo.getRomno().trim(),tableName);
        if(houseInfo==null)
        {
            return  JsonResult.error("没有查询到该用户信息！");
        }
        return  JsonResult.success(houseInfo);
    }


    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HousePayInfo entity) {
        double notpayamount=entity.getPayamount()-entity.getFullpayamount();
        entity.setNotpayamount(notpayamount);
        entity.setPaydt(DateUtils.now());
        String tableName="t_housepayinfo_"+entity.getUseheatno();
        entity.setOptuser(ShiroUtils.getUserName());
        entity.setOptdt(DateUtils.now());
        entity.setIspay(1);
        housePayInfoMapper.insertPayTable(entity,tableName);
        return JsonResult.success();
    }


    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }


}




