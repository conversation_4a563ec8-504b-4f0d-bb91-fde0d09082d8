package com.javaweb.system.controller;



import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.KnowledgeBase;
import com.javaweb.system.query.KnowledgeBaseQuery;
import com.javaweb.system.service.IKnowledgeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 知识库管理表 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/knowledgebase")
public class KnowledgeBaseController extends BaseController{

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;


    /**
     * 获取知识库管理列表
     *
     * @param knowledgeBaseQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(KnowledgeBaseQuery knowledgeBaseQuery) {
        return knowledgeBaseService.getList(knowledgeBaseQuery);
    }

    /**
     * 添加知识库管理
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "知识库管理", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody KnowledgeBase entity) {
        return knowledgeBaseService.edit(entity);
    }


    /**
     * 编辑知识库管理
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "知识库管理", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody KnowledgeBase entity) {
        return knowledgeBaseService.edit(entity);
    }

    /**
     * 删除知识库管理
     *
     * @param knowledgeBaseIds 职级ID
     * @return
     */
    @Log(title = "知识库管理", logType = LogType.DELETE)
    @DeleteMapping("/delete/{knowledgeBaseIds}")
    public JsonResult delete(@PathVariable("knowledgeBaseIds") Integer[] knowledgeBaseIds) {
        return knowledgeBaseService.deleteByIds(knowledgeBaseIds);
    }


}
