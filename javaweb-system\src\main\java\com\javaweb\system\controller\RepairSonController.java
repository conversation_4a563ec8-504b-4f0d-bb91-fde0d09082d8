package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Level;
import com.javaweb.system.entity.RepairSon;
import com.javaweb.system.query.RepairSonQuery;
import com.javaweb.system.service.IRepairSonService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 维修人员表 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/repairson")
public class RepairSonController extends BaseController {

    @Autowired
    private IRepairSonService repairsonService;

    /**
     * 获取维修人员列表
     *
     * @param repairsonQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(RepairSonQuery repairsonQuery) {
        return repairsonService.getList(repairsonQuery);
    }

    /**
     * 添加维修人员
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "维修人员", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody RepairSon entity) {
        return repairsonService.edit(entity);
    }


    /**
     * 编辑维修人员
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "维修人员", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody RepairSon entity) {
        return repairsonService.edit(entity);
    }

    /**
     * 删除维修人员
     *
     * @param repairsonIds 职级ID
     * @return
     */
    @Log(title = "维修人员", logType = LogType.DELETE)
    @DeleteMapping("/delete/{repairsonIds}")
    public JsonResult delete(@PathVariable("repairsonIds") Integer[] repairsonIds) {
        return repairsonService.deleteByIds(repairsonIds);
    }



    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "维修人员", logType = LogType.STATUS)
    @PutMapping("/state")
    public JsonResult status(@RequestBody RepairSon entity) {
        return repairsonService.setStatus(entity);
    }


    /**
     * 获取角色列表
     *
     * @return
     */
    @GetMapping("/getRepairSonList")
    public JsonResult getRepairSonList() {
        return repairsonService.getRepairSonList();
    }

}
