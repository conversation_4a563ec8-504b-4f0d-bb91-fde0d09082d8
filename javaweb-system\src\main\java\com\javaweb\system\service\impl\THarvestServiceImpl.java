package com.javaweb.system.service.impl;


import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.UploadFileConfig;
import com.javaweb.common.utils.*;
import com.javaweb.system.entity.THarvest;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.THarvestMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.THarvestQuery;
import com.javaweb.system.service.THarvestService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 *
 *
 * @Description :
 * <AUTHOR> 监测表实现
 * @Date: 2022/12/6 9:37
 */
@Service
public class THarvestServiceImpl extends ServiceImpl<THarvestMapper, THarvest> implements THarvestService{

    @Autowired
    THarvestMapper tHarvestMapper;

    @Autowired
    private THesMapper thesMapper;

    @Autowired
    private HeatUnitMapper heatUnitMapper;

    @Autowired
    DataSource dataSource;

    /**
     * 查询小区列表
     * @param tHarvestQuery 查询条件
     * @return
     */
    @Override
    public JsonResult getList(THarvestQuery tHarvestQuery) {
        //查询条件
        QueryWrapper<THarvest> queryWrapper = new QueryWrapper<>();
          // 小区名称
          if (!StringUtils.isEmpty(tHarvestQuery.getName())) {
            queryWrapper.like("hescode", tHarvestQuery.getName());
          }
        Integer npage=1;
        Integer nlimit=10;
        if(tHarvestQuery.getPage() !=null)
        {
            npage=tHarvestQuery.getPage();

        }
        if(tHarvestQuery.getLimit() !=null)
        {
            nlimit=tHarvestQuery.getLimit();
        }
        queryWrapper.orderByDesc("id");
        //查询分页数据
        IPage<THarvest> page = new Page<>(npage,nlimit);
        IPage<THarvest> pageData = tHarvestMapper.selectPage(page, queryWrapper);

        pageData.convert(x -> {
            THarvest repairlist = Convert.convert(THarvest.class, x);
            // 换热站名称
            if (StringUtils.isNotNull(x.getHescode())) {
                List<THes>  hesList= thesMapper.getHesByNo(x.getHescode());
                if (StringUtils.isNotNull(hesList)) {
                    repairlist.setUseheatunitName(hesList.get(0).getUseheatunitName());
                    repairlist.setName(hesList.get(0).getName());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    repairlist.setSdt(sdf.format(repairlist.getInstallDt()));
                }
            }

            return repairlist;
        });
        return JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(THarvest entity) {
        entity.setUsername(ShiroUtils.getUserName());
        entity.setUid(ShiroUtils.getUserId());
        entity.setCreateDt(DateUtils.now());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            entity.setInstallDt(dateFormat.parse(entity.getSdt()));
            boolean result = this.saveOrUpdate(entity);
            if (!result) {
                return JsonResult.error();
            }
            return JsonResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.error();
        }
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }
    /**
     * 导入Excel数据
     *
     * @param request 网络请求
     * @param name    目录名称
     * @return
     */
    @Override
    public JsonResult importExcel(HttpServletRequest request, String name) {
        // 上传文件
        UploadUtils uploadUtils = new UploadUtils();
        uploadUtils.setDirName("files");
        Map<String, Object> result = uploadUtils.uploadFile(request, name);
        List<String> imageList = (List<String>) result.get("image");
        List<THarvest> harvestList=new ArrayList<>();
        // 文件路径
        String filePath = UploadFileConfig.uploadFolder + imageList.get(imageList.size() - 1);
        // 读取文件
        List<Object> rows = ExcelUtil.readMoreThan1000RowBySheet(filePath, null);
        if (CollectionUtils.isEmpty(rows)) {
            return JsonResult.error("文件读取失败");
        }
        int totalNum = 0;
        try {
            for (int i = 1; i < rows.size(); i++) {
                // 排除第一行
                String info = rows.get(i).toString();
                if (info.length() <= 2) {
                    continue;
                }
                info = info.substring(1, info.length() - 1);
                String[] columns = info.split(",\\s+");
                if (columns.length != 7) {
                    return JsonResult.error(null, String.format("导入数据失败，文件列数错误！"));
                }
                // 验证前几列是否为空
                if (columns[0].equals("null") ||
                        columns[1].equals("null") ||
                        columns[2].equals("null") ||
                        columns[3].equals("null") ||
                        columns[4].equals("null") ||
                        columns[5].equals("null") ||
                        columns[6].equals("null")) {
                    return JsonResult.error(null, String.format("导入数据失败，校验文件格式错误！"));
                }
                // 插入数据
                THarvest harvest = new THarvest();
                //小区编号
                Integer unitId = heatUnitMapper.getHeatUnitIdByName(columns[0]);
                if(unitId==null)
                {
                    return JsonResult.error(null, String.format("导入数据失败，所属小区不存在！"));
                }

                //换热站编号
                Integer hescode = thesMapper.getHesCode(columns[1]);
                if(hescode==null)
                {
                    return JsonResult.error(null, String.format("导入数据失败，所属换热站不存在！"));
                }
                harvest.setUseheatunitid(unitId);
                harvest.setHescode(hescode);
                harvest.setFloorname(columns[2]);
                harvest.setFloornum(columns[3]);
                harvest.setHousenum(columns[4]);
                String javaDate = convertExcelDateToJavaDate(Double.valueOf(columns[5]));
                // 现在您可以将javaDate设置到harvest对象中
                harvest.setSdt(javaDate); // 假设sdt是Date类型
                harvest.setInstallT(columns[6]);
                harvest.setUsername(ShiroUtils.getUserName());
                harvest.setCreateDt(DateUtils.now());
                harvestList.add(harvest);
            }
            if (harvestList.size() > 0) {
                //批量插入
                if (insertDataBatch(harvestList)) {
                    String len = String.valueOf(harvestList.size());
                    return JsonResult.success(null, String.format("本次共导入数据【%s】条", len));
                } else {
                    return JsonResult.error(null, String.format("插入数据库失败"));
                }
            }
            return JsonResult.error(null,String.format("导入数据失败，校验文件格式错误!"));
        }catch (Exception e)
        {
            return JsonResult.error(null,String.format("导入失败!"));
        }
    }
    public static String convertExcelDateToJavaDate(double excelDate) {
        // 基础日期为1899年12月30日，考虑到Excel的特殊性
        LocalDateTime baseDate = LocalDateTime.of(1899, 12, 30, 0, 0);

        // 计算天数部分
        long days = (long) Math.floor(excelDate);
        // 计算小数部分表示的时间（一天有86400秒）
        int secondsInDay = (int) ((excelDate % 1) * 86400);

        // 创建目标日期时间
        LocalDateTime targetDateTime = baseDate.plusDays(days).plusSeconds(secondsInDay);

        // 设置时区为上海
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.of("Asia/Shanghai"));
        // 格式化为字符串
        return targetDateTime.atZone(ZoneId.of("Asia/Shanghai")).format(formatter);
    }


    private Boolean insertDataBatch(List<THarvest> harvestList)
    {
        if (harvestList == null || harvestList.isEmpty()) {
            return false;
        }
        String sql = "INSERT INTO t_hes_indoort(UseHeatUnitId, hescode,FloorName,FloorNum,HouseNum,install_T,install_dt,username,create_dt" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"; // 修改为实际的 SQL 语句
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(sql)) {

            // 关闭自动提交以提高性能
            connection.setAutoCommit(false);
            for (THarvest harvest : harvestList) {
                pStatement.setInt(1, harvest.getUseheatunitid());
                pStatement.setInt(2, harvest.getHescode());
                pStatement.setString(3, harvest.getFloorname());
                pStatement.setString(4, harvest.getFloornum());
                pStatement.setString(5, harvest.getHousenum());
                pStatement.setString(6, harvest.getInstallT());
                pStatement.setString(7,harvest.getSdt());
                pStatement.setString(8, harvest.getUsername());
                pStatement.setString(9, DateUtils.dateTimeNow());
                pStatement.addBatch();
            }
            // 执行批处理
            pStatement.executeBatch();
            // 提交事务
            connection.commit();
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
        return  true;
    }
}

