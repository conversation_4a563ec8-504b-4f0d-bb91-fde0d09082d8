package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.config.UploadFileConfig;
import com.javaweb.common.utils.*;
import com.javaweb.system.entity.Level;
import com.javaweb.system.entity.MapInfo;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.mapper.MapInfoMapper;
import com.javaweb.system.mapper.RulesMapper;
import com.javaweb.system.query.MapInfoQuery;
import com.javaweb.system.query.RulesQuery;
import com.javaweb.system.service.IMapInfoService;
import com.javaweb.system.service.IRulesService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Service
public class MapInfoServiceImpl extends ServiceImpl<MapInfoMapper, MapInfo> implements IMapInfoService {

        @Autowired
        private MapInfoMapper mapInfoMapper;

        @Override
        public JsonResult getList(MapInfoQuery mapInfoQuery) {

        // 查询条件
        QueryWrapper<MapInfo> queryWrapper = new QueryWrapper<>();


        int nPage = 1;
        int nLimit = 10;

        if(null!=mapInfoQuery.getPage()) {
            nPage = mapInfoQuery.getPage();
        }
        if(null!=mapInfoQuery.getLimit()) {
            nLimit = mapInfoQuery.getLimit();
        }
        // 查询分页数据
        IPage<MapInfo> page = new Page<>(nPage,nLimit);
        IPage<MapInfo> pageData = mapInfoMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }



    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(MapInfo entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    /**
     * 根据用户ID删除用户
     *
     * @param ids 记录ID
     * @return
     */
    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0

        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }


    /**
     * 导入Excel数据
     *
     * @param request 网络请求
     * @param name    目录名称
     * @return
     */
    @Override
    public JsonResult importExcel(HttpServletRequest request, String name) {
        // 上传文件
        UploadUtils uploadUtils = new UploadUtils();
        uploadUtils.setDirName("files");
        Map<String, Object> result = uploadUtils.uploadFile(request, name);
        List<String> imageList = (List<String>) result.get("image");

        // 文件路径
        String filePath = UploadFileConfig.uploadFolder + imageList.get(imageList.size() - 1);
        // 读取文件
        List<Object> rows = ExcelUtil.readMoreThan1000RowBySheet(filePath, null);
        if (CollectionUtils.isEmpty(rows)) {
            return JsonResult.error("文件读取失败");
        }
        int totalNum = 0;
        for (int i = 1; i < rows.size(); i++) {
            // 排除第一行
            String info = rows.get(i).toString();
            if (info.length() <= 2) {
                continue;
            }

            info = info.substring(1, info.length() - 1);
            String[] cloumns = info.split(",\\s+");
            if (cloumns.length != 11) {
                continue;
            }

            // 插入数据
            MapInfo mapInfo = new MapInfo();
            mapInfo.setDevType(cloumns[0]);//设备类型
            mapInfo.setName(cloumns[2]);//设备名称
            mapInfo.setDevName(cloumns[1]);//探测站名称
            mapInfo.setRouteName(cloumns[3]);//线别
            mapInfo.setDirection(cloumns[4]);//方向
            mapInfo.setKilometerMark(cloumns[5]);//公里标
            mapInfo.setMachineRoomNo(cloumns[6]);//机房编号
            mapInfo.setLon(cloumns[7]);
            mapInfo.setLat(cloumns[8]);
            mapInfo.setInstallSite(cloumns[9]);//安装地点
            mapInfo.setMarkType(Integer.valueOf(cloumns[10]));//标记类型
            int count = mapInfoMapper.insert(mapInfo);

            if (count == 1) {
                totalNum++;
            }
        }
        return JsonResult.success(null, String.format("本次共导入数据【%s】条", totalNum));
    }
    }
