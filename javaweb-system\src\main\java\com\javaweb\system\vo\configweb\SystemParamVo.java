package com.javaweb.system.vo.configweb;

import lombok.Data;

@Data
public class SystemParamVo {

    private Integer id;


    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统logo
     */
    private String systemLogo;

    /**
     * 系统版本
     */
    private String systemVersions;

    /**
     *版权信息
     */
    private String copyright;

    /**
     *所属公司
     */
    private String company;

    /**
     *负责人
     */
    private String linkman;

    /**
     *联系电话
     */
    private String mobile;


    /**
     *公司网站
     */
    private String internetAddr;

    /**
     *公司地址
     */
    private String companyAddr;


    /**
     *数据更新频率
     */
    private Integer updateFre;



    /**
     * 系统介绍
     */
    private String intro;
}
