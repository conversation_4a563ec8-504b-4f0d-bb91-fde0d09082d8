package com.javaweb.system.dto;

import lombok.Data;

/**
 * 测点配置表
 */

@Data
public class MonitorPointDefineDto {

    /**
     * 字段id
     */
    private Integer id;

    /**
     * 字段描述
     */
    private String mpdesc;

    private String  mpfielddefine;

    /**
     * 编号
     */
    private Integer hescode;


    /**
     * 最小量程
     */
    private String minvalue;

    /**
     * 最大量程
     */
   private String maxvalue;


    /**
     *是否使用
     */
    private Integer isused;


    /**
     * 换热站名称
     */
    private String wrAddress;

    /**
     * 字段别名
     */
    private String fieldAlisa;

    /**
     * 是否控制
     */
    private Integer iscontrol;

    /**
     * 是否报警
     */
    private Integer isbaojing;


    /**
     * 单位
     */
    private String unit;
}
