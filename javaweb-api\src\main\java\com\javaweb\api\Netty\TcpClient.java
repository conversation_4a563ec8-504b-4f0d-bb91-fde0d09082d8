package com.javaweb.api.Netty;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.util.concurrent.atomic.AtomicBoolean;

public class TcpClient {
    private Socket socket;
    private OutputStream outputStream;
    private final String serverAddress;
    private final int port;
    private AtomicBoolean isConnecting = new AtomicBoolean(false);

    public TcpClient(String serverAddress, int port) {
        this.serverAddress = serverAddress;
        this.port = port;
        // 不立即尝试连接
    }

    /**
     * 尝试建立连接，如果连接已存在则返回true，否则尝试重新连接并返回结果
     */
    private synchronized boolean ensureConnection() {
        if (socket != null && !socket.isClosed()) {
            return true;
        }

        if (isConnecting.compareAndSet(false, true)) {
            try {
                System.out.println("Attempting to establish connection to " + serverAddress + ":" + port);
                socket = new Socket();
                SocketAddress socketAddress = new InetSocketAddress(serverAddress, port);
                socket.connect(socketAddress, 5000); // 设置超时时间
                outputStream = socket.getOutputStream();
                System.out.println("Connection established successfully.");
                return true;
            } catch (IOException e) {
                System.err.println("Failed to establish connection: " + e.getMessage());
                disconnectQuietly(); // 确保关闭可能部分打开的资源
                return false;
            } finally {
                isConnecting.set(false);
            }
        }
        return false;
    }

    public void send(String message) {
        if (!ensureConnection()) {
            System.err.println("No active connection available. Message not sent: " + message);
            return;
        }

        try {
            byte[] buffer = message.getBytes("UTF-8");
            outputStream.write(buffer);
            outputStream.flush();
            System.out.println("Data sent successfully: " + message);
        } catch (IOException e) {
            System.err.println("Error while sending data: " + e.getMessage());
            disconnectQuietly(); // 发生错误时断开连接以便下次重试
        }
    }

    public void disconnect() {
        disconnectQuietly();
    }

    private void disconnectQuietly() {
        try {
            if (outputStream != null) {
                outputStream.close();
                System.out.println("OutputStream closed.");
            }
            if (socket != null) {
                socket.close();
                System.out.println("Socket closed.");
            }
        } catch (IOException e) {
            System.err.println("Error while closing resources: " + e.getMessage());
        }
    }
}