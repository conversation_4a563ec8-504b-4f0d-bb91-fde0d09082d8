package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <p>
 * 维修工单表
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_repair_record")
public class RepairList extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 监测站编号
     */
    private Integer hesId;

    /**
     * 监测站名称
     */
    @TableField(exist=false)
    private String hesname;

    /**
     * 故障分类
     */
    private String faultClass;

    /**
     * 故障描述
     */
    private String faultDesc;

    /**
     * 维修人员id
     */
    private Integer repairId;


    /**
     * 维修人员名称
     */
    @TableField(exist=false)
    private String repairsonName;

    /**
     * 维修状态 状态：0待处理 1处理中 2 已完成
     */
    private Integer repairState;

    /**
     * 维修结果
     */
    private String repairRst;


    /**
     * 维修内容
     */
    private String repairContent;


}

