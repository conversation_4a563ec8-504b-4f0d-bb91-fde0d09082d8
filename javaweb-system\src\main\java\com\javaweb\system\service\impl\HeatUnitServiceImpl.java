package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.mapper.DatabaseOptMapper;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.query.UseHeatUnitQuery;
import com.javaweb.system.service.IHeatUnitService;
import com.javaweb.system.utils.DataBaseOptUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 *小区信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HeatUnitServiceImpl extends ServiceImpl<HeatUnitMapper, HeatUnit> implements IHeatUnitService {

    @Autowired
    HeatUnitMapper heatUnitMapper;

    @Autowired
    DatabaseOptMapper databaseOptMapper;

    /**
     * 查询小区列表
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        UseHeatUnitQuery useHeatUnitQuery =(UseHeatUnitQuery) query;
        //查询条件
        QueryWrapper<HeatUnit> queryWrapper = new QueryWrapper<>();
        //小区名称
        if(!StringUtils.isEmpty(useHeatUnitQuery.getName())){
            queryWrapper.like("name",useHeatUnitQuery.getName());
        }
         queryWrapper.orderByDesc("id");
         Integer npage=1;
         Integer nlimit=10;
         if(useHeatUnitQuery.getPage() !=null)
         {
             npage=useHeatUnitQuery.getPage();

        }
         if(useHeatUnitQuery.getLimit() !=null)
         {
          nlimit=useHeatUnitQuery.getLimit();
         }
        //查询分页数据
        IPage<HeatUnit> page = new Page<>(npage,nlimit);
        IPage<HeatUnit> page1 = heatUnitMapper.selectPage(page,queryWrapper);
        return  JsonResult.success(page1);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HeatUnit entity) {
        Integer num=0;
        num= heatUnitMapper.getCountByNo(entity);
        if(num>0)
        {
            return JsonResult.error("小区已存在");
        }
        boolean result = this.saveOrUpdate(entity);
        if(StringUtils.isNull(entity.getId()))
        {
            createIndoorTableField(entity.getUseheatno());
        }

        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    @Override
    public JsonResult creatDataBaseTable(String unitNo) {
        Map<String, Boolean> tableCreationResults = new HashMap<>();

        try {
            // 尝试创建室内温度采集表
            boolean indoorTableCreated = createIndoorTableField(unitNo);
            tableCreationResults.put("indoorTable", indoorTableCreated);

            // 尝试创建缴费表
            boolean payTableCreated = createPayTableField(unitNo);
            tableCreationResults.put("payTable", payTableCreated);

            // 尝试创建阀门表
            boolean valvesTableCreated = createValvesTableField(unitNo);
            tableCreationResults.put("valvesTable", valvesTableCreated);

            // 检查结果并给出相应的消息
            StringBuilder messageBuilder = new StringBuilder();
            for (Map.Entry<String, Boolean> entry : tableCreationResults.entrySet())
            {
                if (entry.getValue()) {
                    messageBuilder.append(entry.getKey()).append(" 表生成成功; ");
                } else {
                    messageBuilder.append(entry.getKey()).append(" 表生成失败; ");
                }
            }

            if (tableCreationResults.values().stream().allMatch(result -> !result)) {
                return JsonResult.error("所有数据表都已存在: " + messageBuilder.toString());
            } else if (tableCreationResults.values().stream().anyMatch(result -> !result)) {
                return JsonResult.error(messageBuilder.toString());
            } else {
                return JsonResult.success("创建数据表成功!");
            }
        } catch (Exception e) {
            // 如果发生异常，则返回失败消息
            return JsonResult.error("生成数据表失败: " + e.getMessage());
        }
    }

    //创建室内温度采集表
    public boolean createIndoorTableField(String unitNo)
    {
        try{
            String tableName="t_indoorT_data_"+unitNo;
            if(!databaseOptMapper.tableExists(tableName))
            {
                //数据表字段
                List<String> lst = new ArrayList<>();
                lst.add("id");
                lst.add("collectDt");
                lst.add("heatMeterNo");
                lst.add("F_S_T1");
                lst.add("F_B_T1");
                lst.add("F_S_C_F");
                lst.add("F_S_C_H");
                lst.add("F_S_H");
                lst.add("F_S_F");
                //创建数据表
                String sql= DataBaseOptUtils.createTableSQLandPartition(lst,tableName);
                databaseOptMapper.createTable(sql);
                return true;
            }else
            {
                return true;
            }
        }catch (Exception e)
        {
            return false;
        }
    }
    //创建阀门采集表
    public boolean createValvesTableField(String unitNo)
    {
        try{
            String tableName="t_valves_data_"+unitNo;
            if(!databaseOptMapper.tableExists(tableName))
            {
                //数据表字段
                List<String> lst = new ArrayList<>();
                lst.add("id");
                lst.add("collectDt");
                lst.add("valvesNo");
                lst.add("F_S_V");
                lst.add("F_S_V_STATE");
                //创建数据表
                String sql= DataBaseOptUtils.createTableSQLandPartition(lst,tableName);
                databaseOptMapper.createTable(sql);
                return true;
            }else
            {
                return true;
            }
        }catch (Exception e)
        {
            return false;
        }
    }
    //创建缴费表
    public boolean createPayTableField(String unitNo)
    {
        try{
            String tableName="t_housepayinfo_"+unitNo;
            if(!databaseOptMapper.tableExists(tableName)) {
                //数据表字段
                List<String> lst = new ArrayList<>();
                lst.add("id");
                lst.add("UseHeatUnit_Id");
                lst.add("HouseInfo_Id");
                lst.add("HeatYearPay");
                lst.add("ispay");
                lst.add("PayDT");
                lst.add("PayAmount");
                lst.add("FullPayAmount");
                lst.add("NotPayAmount");
                lst.add("WarmStatus");
                lst.add("optuser");
                lst.add("optdt");
                String sql= DataBaseOptUtils.createTableSQL(lst,tableName);
                databaseOptMapper.createTable(sql);
                return true;
            }else
            {
                return true;
            }
        }catch (Exception e)
        {
            return false;
        }
    }


    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHeatunitList() {
        QueryWrapper<HeatUnit> queryWrapper = new QueryWrapper<>();

        List<HeatUnit> list = list(queryWrapper);
        return JsonResult.success(list);
    }
}




