
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.system.entity.UserRole;

/**
 * <p>
 * 人员角色表 服务类
 * </p>
 *
 * 
 * @since 2020-10-30
 */
public interface IUserRoleService extends IBaseService<UserRole> {

    /**
     * 插入用户角色关系数据
     *
     * @param userId  用户ID
     * @param roleIds 角色ID
     */
    void insertUserRole(Integer userId, Integer[] roleIds);

    /**
     * 根据用户ID删除用户角色关系数据
     *
     * @param userId 用户ID
     */
    void deleteUserRole(Integer userId);

}
