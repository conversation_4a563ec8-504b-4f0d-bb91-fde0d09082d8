package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THarvest;
import com.javaweb.system.query.THarvestQuery;
import com.javaweb.system.service.THarvestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/tharvest")
public class THarvestController extends BaseController {

    @Autowired
    private THarvestService tTHarvestService;

    /**
     * 获取查询列表
     * @param
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(THarvestQuery tHarvestuery) {
        return tTHarvestService.getList(tHarvestuery);
    }


    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "室温采集", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody THarvest entity){

        return tTHarvestService.edit(entity);
    }

    @Log(title = "室温采集", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody THarvest entity){

        return tTHarvestService.edit(entity);
    }

    @Log(title = "室温采集", logType = LogType.DELETE)
    @DeleteMapping("/delete/{THarvestIds}")
    public JsonResult delete(@PathVariable("THarvestIds") Integer[]THarvestIds){

        return tTHarvestService.deleteByIds(THarvestIds);
    }
    /**
     * 导入Excel
     *
     * @param request 网络请求
     * @return
     */
    @Log(title = "室温采集", logType = LogType.IMPORT)
    @PostMapping("/importExcel/{name}")
    public JsonResult importExcel(HttpServletRequest request, @PathVariable("name") String name) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        return tTHarvestService.importExcel(request, name);
    }

}


