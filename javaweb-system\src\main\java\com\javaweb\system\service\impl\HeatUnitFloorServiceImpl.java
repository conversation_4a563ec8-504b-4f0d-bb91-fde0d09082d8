package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.entity.HeatUnitFloorUnit;
import com.javaweb.system.mapper.HeatUnitFloorMapper;
import com.javaweb.system.query.HeatUnitFloorQuery;
import com.javaweb.system.service.IHeatUnitFloorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 楼宇信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HeatUnitFloorServiceImpl extends ServiceImpl<HeatUnitFloorMapper, HeatUnitFloor> implements IHeatUnitFloorService {

    @Autowired
    HeatUnitFloorMapper heatUnitFloorMapper;

    /**
     * 查询小区列表
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        HeatUnitFloorQuery heatUnitFloorQuery =(HeatUnitFloorQuery) query;
        //查询条件
        QueryWrapper<HeatUnitFloor> queryWrapper = new QueryWrapper<>();
        //楼宇名称
        if(!StringUtils.isEmpty(heatUnitFloorQuery.getFloorname())){
            queryWrapper.like("floorname",heatUnitFloorQuery.getFloorname());
        }
        queryWrapper.orderByDesc("id");
         Integer npage=1;
         Integer nlimit=10;
         if(heatUnitFloorQuery.getPage() !=null)
         {
             npage=heatUnitFloorQuery.getPage();

        }
         if(heatUnitFloorQuery.getLimit() !=null)
         {
          nlimit=heatUnitFloorQuery.getLimit();
         }
        //查询分页数据
        IPage<HeatUnitFloor> page = new Page<>(npage,nlimit);
        IPage<HeatUnitFloor> pageData = heatUnitFloorMapper.selectPage(page,queryWrapper);
        List<HouseInfoDto> houseInfoDtolst=heatUnitFloorMapper.getHeatUnitFloorByLinkId();

        pageData.convert(x -> {
            HeatUnitFloor heatUnitFloorList = Convert.convert(HeatUnitFloor.class, x);

            houseInfoDtolst.forEach(houseInfo->{
                if(heatUnitFloorList.getId().equals(houseInfo.getId()))
                {
                    heatUnitFloorList.setName(houseInfo.getName());
                }
            });
            return heatUnitFloorList;
        });

        return  JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HeatUnitFloor entity) {
        //通过验证楼宇信息是否存在
        Integer num=0;
        num= heatUnitFloorMapper.getCountByNo(entity);
        if(num>0)
        {
            return JsonResult.error("楼宇已存在");
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHeatUnitFloorList() {
        QueryWrapper<HeatUnitFloor> queryWrapper = new QueryWrapper<>();

        List<HeatUnitFloor> list = list(queryWrapper);
        return JsonResult.success(list);
    }
}




