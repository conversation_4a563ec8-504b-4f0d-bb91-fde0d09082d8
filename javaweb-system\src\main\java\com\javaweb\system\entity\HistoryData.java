package com.javaweb.system.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 历史数据
 * </p>
 *
 * @since 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_hesmonitorpointdefine")
public class HistoryData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *是否使用
     */
    private Integer isused;

    /**
     * 监测站编号
     */
    private Integer hescode;


    /**
     * 描述
     */
    private String mpdesc;

    /**
     * 字段名
     */
    private String mpfielddefine;


    /**
     * 是否控制
     */
    private Integer iscontrol;

}
