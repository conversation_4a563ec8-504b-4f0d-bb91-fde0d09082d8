package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.mapper.HeatUnitFloorMapper;
import com.javaweb.system.query.HeatUnitFloorQuery;
import com.javaweb.system.service.IHeatUnitFloorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/heatunitfloor")
public class HeatUnitFloorController extends BaseController {

    @Autowired
    private IHeatUnitFloorService heatUnitFloorService;


    @Autowired
    private HeatUnitFloorMapper heatUnitFloorMapper;

    /**
     * 获取查询列表
     * @param HeatUnitFloorQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HeatUnitFloorQuery HeatUnitFloorQuery) {
        return heatUnitFloorService.getList(HeatUnitFloorQuery);
    }


    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "楼宇信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HeatUnitFloor entity){

        return heatUnitFloorService.edit(entity);
    }

    @Log(title = "楼宇信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HeatUnitFloor entity){

        return heatUnitFloorService.edit(entity);
    }

    @Log(title = "楼宇信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{heatUnitFloorIds}")
    public JsonResult delete(@PathVariable("heatUnitFloorIds") Integer[]HeatUnitFloorIds){

        return heatUnitFloorService.deleteByIds(HeatUnitFloorIds);
    }

    @GetMapping("/getHeatUnitFloorList")
    public JsonResult getHeatUnitFloorList(){
        return heatUnitFloorService.getHeatUnitFloorList();
    }


    //获得基本信息  id 名称
    @GetMapping("/getHeatUnitFloorBaseinfo/{heatUnitId}")
    public JsonResult getHeatUnitFloorBaseinfo(@PathVariable Integer heatUnitId)
    {
        return JsonResult.success(heatUnitFloorMapper.getHeatUnitFloorBaseinfo(heatUnitId));
    }



}

