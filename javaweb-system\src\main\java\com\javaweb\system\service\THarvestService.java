package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THarvest;
import com.javaweb.system.query.THarvestQuery;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 14:44
 */
public interface THarvestService extends IService<THarvest> {

    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(THarvestQuery tHarvestQuery);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(THarvest entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

    /**
     * 导入
     *
     * @return
     */
    JsonResult importExcel(HttpServletRequest request, String name);
}

