package com.javaweb.system.service.impl;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.WeatherDto;
import com.javaweb.system.entity.Weather;
import com.javaweb.system.mapper.WeatherMapper;
import com.javaweb.system.query.WeatherQuery;
import com.javaweb.system.service.IWeatherService;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * <p>
 * 气象站 服务实现类
 * </p>
 *
 *
 * @since 2022-12-12
 */

@Service
public class WeatherServiceImpl extends ServiceImpl<WeatherMapper, Weather> implements IWeatherService {


    @Autowired
    private WeatherMapper weatherMapper;

    @Autowired
    DataSource dataSource;
    /**
     * 获取气象站列表
     *
     * @param weatherQuery 查询条件
     * @return
     */

    @Override
    public JsonResult getList(WeatherQuery weatherQuery) {
        // 查询条件
        QueryWrapper<Weather> queryWrapper = new QueryWrapper<>();
        // 查询分页数据

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = new Date();
        String currentTime = dateFormat.format(date1);
        System.out.println(currentTime);

        // 气象站数据
        if (!StringUtils.isEmpty(weatherQuery.getCollectdt())) {
            currentTime=weatherQuery.getCollectdt();
            System.out.println(currentTime);
        }
        queryWrapper.ge("collectdt",currentTime);
        IPage<Weather> page = new Page<>(weatherQuery.getPage(), weatherQuery.getLimit());
        IPage<Weather> pageData = weatherMapper.selectPage(page, queryWrapper);
        return JsonResult.success(pageData);
    }
    /**
     * 获取当天气象站列表
     *
     * @return
     */
    @Override
    public JsonResult getWeatherAll() {
        List<Weather>  weatherList= weatherMapper.getWeatherBytimes();
        System.out.println(weatherList);
        return JsonResult.success(weatherList);
    }


    @Override
    public JsonResult getWeatherDate(String dt)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String sql=null;
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        try {
            connection = dataSource.getConnection();
            if (!StringUtils.isEmpty(dt))
            {
                sql="select * from t_weatherstationdata  where collectdt>='"+dt+" 00:00:00' and collectdt<='"+dt+" 23:59:59' ";
            }else
            {
                sql = "select * from t_weatherstationdata order by id desc limit 0,100";
            }
            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                String collectdt =rSet.getString("collectdt");
                String t =rSet.getString("T");
                String h =rSet.getString("H");
                String r =rSet.getString("R");
                String s =rSet.getString("S");
                resultMap.put("dt",collectdt);
                resultMap.put("t",t);
                resultMap.put("h",h);
                resultMap.put("r",r);
                resultMap.put("s",s);
                lst.add(resultMap);
            }
            //System.out.println(lst);
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            if (connection!=null) try {connection.close();}catch (Exception ignore) {}
        }
        return JsonResult.success(lst);
    }


    @Override
    public JsonResult getWeatherHourAvgData(String dt)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String sql=null;
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        try {
            connection = dataSource.getConnection();
            sql="SELECT   HOUR(CollectDT) AS hour, CAST(AVG( T ) as decimal(10,2)) as T from t_weatherstationdata where CollectDT>='"+dt+" 00:00:00' and CollectDT<='"+dt+" 23:59:59' GROUP BY hour";
            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                String hour =rSet.getString("hour");
                String t =rSet.getString("T");
                resultMap.put("dt",hour+"时");
                resultMap.put("t",t);
                lst.add(resultMap);
            }
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            if (connection!=null) try {connection.close();}catch (Exception ignore) {}
        }
        return JsonResult.success(lst);
    }


    /*
    * 获取日最高温度，最低温度，平均温度
    * */
    @Override
    public JsonResult getWeatherDayMinMax(String dt)
    {

        String  startdt=dt+" 00:00:00";
        String  enddt=dt+" 23:59:59";
        WeatherDto weatherDto=weatherMapper.getWeatherDayMinMaxBydt(startdt,enddt);
        return JsonResult.success(weatherDto);
    }


    @Override
    public JsonResult getWeatherDayAvgData(String startdt,String enddt)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String sql=null;
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        try {
            connection = dataSource.getConnection();
            sql="SELECT  DATE(CollectDT) AS date, CAST(AVG(T) as decimal(10,2)) as T from t_weatherstationdata where CollectDT>='"+startdt+" 00:00:00' and CollectDT<='"+enddt+" 23:59:59' GROUP BY date";
            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                String date =rSet.getString("date");
                String t =rSet.getString("T");
                resultMap.put("dt",date);
                resultMap.put("t",t);
                lst.add(resultMap);
            }
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            if (connection!=null) try {connection.close();}catch (Exception ignore) {}
        }
        return JsonResult.success(lst);
    }
}
