
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Flaws;
import com.javaweb.system.query.FlawsQuery;
import com.javaweb.system.service.IFlawsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@RestController
@RequestMapping("/flaws")
public class FlawsController extends BaseController {

    @Autowired
    private IFlawsService flawsService;

    /**
     * 获取列表
     *
     * @param flawsQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(FlawsQuery flawsQuery) {
        return flawsService.getList(flawsQuery);
    }

    /**
     * 添加
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "供热缺陷", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Flaws entity) {
        return flawsService.edit(entity);
    }

    /**
     * 编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "供热缺陷", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Flaws entity) {
        return flawsService.edit(entity);
    }

    /**
     * 删除
     *
     * @param flawsIds 职级ID
     * @return
     */
    @Log(title = "供热缺陷", logType = LogType.DELETE)
    @DeleteMapping("/delete/{flawsIds}")
    public JsonResult delete(@PathVariable("flawsIds") Integer[] flawsIds) {
        return flawsService.deleteByIds(flawsIds);
    }



}
