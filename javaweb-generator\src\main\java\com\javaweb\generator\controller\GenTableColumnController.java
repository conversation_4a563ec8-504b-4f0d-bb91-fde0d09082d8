
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.generator.controller;


import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 代码生成业务表字段 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-06
 */
@RestController
@RequestMapping("/gentablecolumn")
public class GenTableColumnController {

}
