package com.javaweb.system.workthread;

import com.aliyuncs.utils.StringUtils;
import com.google.gson.Gson;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.THesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RedisDataOptServer {
    @Autowired
    RedisUtils redisUtils;

    @Autowired
    THesMapper tHesMapper;

    /*
    * 保存所有的换热站数据
    * */
    public void redisSaveHesList()
    {

        //换热站信息表
        String hashname="FD:Heslist";
        redisUtils.del(hashname);
        Map<String, Object> map= new HashMap<>();
        // 获取所有换热站信息
        List<THes> heslist=tHesMapper.getHesAllList();
        // 创建Gson对象
        Gson gson = new Gson();
        heslist.forEach(hes -> {
            String json = gson.toJson(hes);
            map.put(String.valueOf(hes.getHescode()),json);
        });
        redisUtils.hmset(hashname,map);
    }

    /*
     * 获取保存所有的换热站数据
     * */
    public List<THes>  getRedisSaveHesList()
    {
        //换热站信息表
        List<THes>  heslist = new ArrayList<>();
        String hashname="FD:Heslist";
        Map<String, Object> map= new HashMap<>();
        Map<Object, Object> lst=redisUtils.hmget(hashname);
        Gson gson = new Gson();
        //遍历Map
        for (Map.Entry<Object, Object> entry : lst.entrySet())
        {
            Object value = entry.getValue();
            //System.out.println(value);
            // 现在将JSON字符串转换回对象
            THes hes = gson.fromJson(value.toString(), THes.class);
            heslist.add(hes);
        }
        return  heslist;
    }

    /*
     * redis中 修改换热站信息某一条数据
     * */
    public boolean updateRedisHesById(THes hes)
    {
        String key=String.valueOf(hes.getId());
        String hashname = "FD:Heslist";
        // 创建Gson对象
        Gson gson = new Gson();
        String json = gson.toJson(hes);
        //设置单个的信息
        return redisUtils.hset(hashname,key,json);
    }

    /*
     * redis中 删除换热站信息某些数据
     * */
    public void delRedisHesById(Integer[] ids)
    {
        String hashname = "FD:Heslist";
        for (Integer id : ids) {
            redisUtils.hdel(hashname,id);
        }
    }


    /*
     * 保存所有的换热站数据
     * */
    public void redisSaveAlarmModelList()
    {
        //换热站信息表
        String hashname="FD:Heslist";
        Map<String, Object> map= new HashMap<>();
        // 获取所有换热站信息
        List<THes> heslist=tHesMapper.getHesAllList();
        // 创建Gson对象
        Gson gson = new Gson();
        heslist.forEach(hes -> {
            String json = gson.toJson(hes);
            map.put(String.valueOf(hes.getHescode()),json);
        });
        redisUtils.hmset(hashname,map);
    }

    /*获取redis中指定换热站的的数据*/
    public String getRedisHesDataByCode(Integer Code)
    {
        String hashname = "FD:HesDatalist";
        Object obj=redisUtils.hget(hashname,String.valueOf(Code));
        if (obj != null) {
            return obj.toString();
        }
        return "";
    }

    /*删除redis中指定换热站的的数据*/
    public void delRedisHesDataByCode(Integer Code)
    {
        String hashname = "FD:HesDatalist";
        redisUtils.hdel(hashname,String.valueOf(Code));
    }

}
