package com.javaweb.system.controller;

import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THarvest;
import com.javaweb.system.mapper.THarvestMapper;
import com.javaweb.system.service.IPhoneAppApiService;
import com.javaweb.system.service.THarvestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 数据传输 前端控制器
 * </p>
 *
 *
 * @since 2024-1-2
 */
@RestController
@RequestMapping("/phoneApi")
public class PhoneAppApi {

    @Autowired
    private IPhoneAppApiService phoneAppApiService;

    @Autowired
    private THarvestMapper harvestMapper;

    /**
     * 登录验证
     *
     * @return
     */
    @GetMapping("/uLogin/{phone}/{password}")
    public JsonResult uLogin(@PathVariable("phone") String phone,@PathVariable("password") String password)
    {
        System.out.println("访问一次");
        return phoneAppApiService.uLogin(phone,password);
    }


    /**
     * 获取换热站列表
     *
     * @return
     */
    @GetMapping("/getHesList")
    public JsonResult getHesList()
    {
        return phoneAppApiService.getHesList();
    }

    /**
     * 获取换热站最新数据
     *
     * @return
     */
    @GetMapping("/getHesData/{hescode}")
    public JsonResult getHesData(@PathVariable("hescode")String hescode)
    {
        return phoneAppApiService.getHesData(hescode);
    }

    /**
     * 获取换热站历史数据
     *
     * @return
     */
    @GetMapping("/getHesHistoriesData/{hescode}/{dt}/{field}")
    public JsonResult getHesHistoriesData(@PathVariable("hescode") Integer hescode,
                                          @PathVariable("dt") String dt,
                                          @PathVariable("field") String field)
    {
        return phoneAppApiService.getHesHistoriesData(hescode,dt,field);
    }



    /**
     * 获取气象站数据
     *
     * @return
     */
    @GetMapping("/getWsData")
    public JsonResult getWsData()
    {
        return phoneAppApiService.getWsData();
    }

    /**
     * 获取气象站最后100条
     *
     * @return
     */
    @GetMapping("/getWsHistoryData")
    public JsonResult getWsgetWsHistoryDataData()
    {
        return phoneAppApiService.getWsHistoryData();
    }

    /**
     * 获取气象站历史数据
     *
     * @return
     */
    @GetMapping("/getWsHistoriesData/{dt}")
    public JsonResult getWsHistoriesData(@PathVariable("dt") String dt)
    {
        return phoneAppApiService.getWsHistoriesData(dt);
    }


    /**
     * 获取小区列表
     *
     * @return
     */
    @GetMapping("/getHesUnitList")
    public JsonResult getHesUnitList()
    {
        return phoneAppApiService.getHesUnitList();
    }

    /**
     * 获取采集数据列表
     *
     * @return
     */
    @GetMapping("/getIndoorDataList/{nlevel}/{username}")
    public JsonResult getIndoorDataList(@PathVariable("nlevel")   Integer nlevel,
                                        @PathVariable("username") String username)
    {
        return phoneAppApiService.getIndoorDataList(nlevel,username);
    }


    /**
     * 室温数据上报
     *
     * @return
     */
    @PostMapping("/addIndoorData")
    public JsonResult addIndoorData(@RequestParam("hescode") String hesCode,
                                    @RequestParam("useheatunitid") Integer unitId,
                                    @RequestParam("floornum") String floornum,
                                    @RequestParam("floorname") String floorname,
                                    @RequestParam("housenum") String housenum,
                                    @RequestParam("installt") String installt,
                                    @RequestParam("username") String username,
                                    @RequestParam("lon") String lon,
                                    @RequestParam("lat") String lat,
                                    @RequestParam("mm") String mm){
        // 根据接收到的参数创建实体对象
        THarvest entity = new THarvest();
        entity.setHescode(Integer.valueOf(hesCode));
        entity.setUseheatunitid(unitId);
        entity.setFloornum(floornum);
        entity.setFloorname(floorname);
        entity.setHousenum(housenum);
        entity.setUsername(username);
        entity.setLon(lon);
        entity.setLat(lat);
        entity.setInstallT(installt);
        entity.setInstallDt(DateUtils.now());
        entity.setCreateDt(DateUtils.now());
        entity.setMm(mm);
        // 使用服务层逻辑处理实体对象
        harvestMapper.insert(entity);
        return JsonResult.success(0);
    }

    /**
     * 更新用户位置信息
     *
     * @return
     */
    @PostMapping("/updateUserLocation")
    public JsonResult updateUserLocation(@RequestParam("phone") String phone,
                                         @RequestParam("lon") String lon,
                                         @RequestParam("lat") String lat)
    {

        return phoneAppApiService.updateUserLocation(phone,lon,lat);
    }

    /**
     * 注册验证
     *
     * @return
     */
    @GetMapping("/regVer/{phone}/{password}")
    public JsonResult regVer(@PathVariable("phone") String phone,@PathVariable("password") String password)
    {
        return phoneAppApiService.regVer(phone,password);
    }


    /**
     * 修改密码
     *
     * @return
     */
    @GetMapping("/modifyPwd/{id}/{password}")
    public JsonResult modifyPwd(@PathVariable("id") Integer id,@PathVariable("password") String password)
    {
        return phoneAppApiService.modifyPwd(id,password);
    }


    /**
     * 个人信息
     *
     * @return
     */
    @GetMapping("/getPersonalInfo/{param}")
    public JsonResult getPersonalInfo(@PathVariable("param") String param)
    {
        return phoneAppApiService.getPersonalInfo(param);
    }

    /**
     * 白名单列表
     *
     * @return
     */
    @GetMapping("/whitelistList/{id}/{nlevel}")
    public JsonResult whitelistList(@PathVariable("id") Integer id,@PathVariable("nlevel") Integer nlevel)
    {
        return phoneAppApiService.whitelistList(id,nlevel);
    }


    /**
     * 白名单添加
     *
     * @return
     */
    @GetMapping("/whiteUserAdd/{name}/{phone}")
    public JsonResult whiteUserAdd(@PathVariable("name") String name,@PathVariable("phone") String phone)
    {
        return phoneAppApiService.whiteUserAdd(name,phone);
    }


    /**
     * 白名单列表编辑
     *
     * @return
     */
    @GetMapping("/whiteUserEdit/{id}/{nlevel}")
    public JsonResult whiteUserEdit(@PathVariable("id") Integer id,@PathVariable("nlevel") Integer nlevel)
    {
        return phoneAppApiService.whiteUserEdit(id,nlevel);
    }

    /**
     * 计算参数
     *
     * @return
     */
    @GetMapping("/calcParam/{hescode}")
    public JsonResult calcParam(@PathVariable("hescode") String hescode)
    {
        return phoneAppApiService.calcParam(hescode);
    }


    /**
     * 换热站供回水数据
     *
     * @return
     */
    @GetMapping("/hesReflowData/{hescode}/{dt}")
    public JsonResult hesReflowData(@PathVariable("hescode") String hescode,@PathVariable("dt") String dt)
    {
        return phoneAppApiService.hesReflowData(hescode,dt);
    }

    /**
     * 换热站运行模式算法
     *
     * @return
     */
    @GetMapping("/hesRunModeAlg/{hescode}")
    public JsonResult hesRunModeAlg(@PathVariable("hescode") String hescode)
    {
        return phoneAppApiService.getHesRunModeAlg(hescode);
    }


    /**
     * 换热站运行模式算法
     *
     * @return
     */
    @GetMapping("/setHesRunModeAlg/{hescode}/{type}/{setval}")
    public JsonResult setHesRunModeAlg(@PathVariable("hescode") String hescode,
                                       @PathVariable("type") String type,
                                       @PathVariable("setval") Integer setval)
    {
        return phoneAppApiService.setHesRunModeAlg(hescode,type,setval);
    }


    /**
     * 换热站配置字段
     *
     * @return
     */
    @GetMapping("/hesConfigFields/{hescode}")
    public JsonResult hesConfigFields(@PathVariable("hescode") String hescode)
    {
        return phoneAppApiService.hesConfigFields(hescode);
    }

    /**
     * 换热站控制字段
     *
     * @return
     */
    @GetMapping("/hesControlFields/{hescode}")
    public JsonResult hesControlFields(@PathVariable("hescode") String hescode)
    {
        return phoneAppApiService.hesControlFields(hescode);
    }


    /**
     * 换热站字段值
     *
     * @return
     */
    @GetMapping("/hesFieldData/{hescode}/{field}")
    public JsonResult hesFieldData(@PathVariable("hescode") String hescode,@PathVariable("field") String field)
    {
        return phoneAppApiService.hesFieldData(hescode,field);
    }

}
