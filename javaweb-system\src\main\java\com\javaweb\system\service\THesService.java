package com.javaweb.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THes;
import com.javaweb.system.query.HesQuery;
import com.javaweb.system.query.RecodeQuery;

import java.util.List;
import java.util.Map;


/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 9:28
 */
public interface THesService extends IService<THes> {


    /**
     * 获取监测站列表
     *
     * @return
     */
     JsonResult getList(BaseQuery query);

    /**
     * 获取监测站列表
     *
     * @return
     */
    JsonResult getHesList();


    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(THes entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

    /**
     * 设置状态
     *
     * @return
     */
    JsonResult setStatus(THes entity);

     /**
     * 获取监测站信息列表
     *
     * @return
     */
    JsonResult getHesinfoList(HesQuery hesQuery);

    /**
     * 获取监测站类型个数
     *
     * @return
     */
    JsonResult getHesNumData();

    /**
     * 获取监测站能耗曲线
     *
     * @return
     */
    JsonResult getHesEnergyQuxianData(String start_dt, String end_dt,int hescode);


     JsonResult getCarbonAllowanceData(String startDate, String endDate, List<Integer> hescodes);
    /**
     * 获取监测站日能耗数据
     *
     * @return
     */

    JsonResult getHesDayEnergyData(String dt);


    JsonResult getHesStation();


    /**
     * 换热站能耗统计按照时间段统计每天
     *
     * @return
     */
     JsonResult getHesEnergytjData(String startdt,String enddt);

    /**
     * 统计按照日期统计每小时能耗
     *
     * @return
     */

    JsonResult getHesEnergytjHourData(String startdt);
}

