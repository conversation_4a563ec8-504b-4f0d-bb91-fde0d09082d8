package com.javaweb.control.Netty;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

public class MyControlMessageEncoder extends MessageToByteEncoder<ControlCmdPack> {

    @Override
    protected void encode(ChannelHandlerContext ctx, ControlCmdPack msg, ByteBuf out) throws Exception {
        out.writeIntLE(msg.head.headFlag);
        out.writeIntLE(msg.head.hesCode);
        out.writeIntLE(msg.control_type);
        out.writeIntLE(msg.control_rate);
        out.writeIntLE(msg.control_value);
        for (byte b : msg.control_field) {
            out.writeByte(b);
        }
    }
}