<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HeatUnitMapper">
    <!-- 通过名字获取个数-->
    <select id="getCountByNo" resultType="Integer">
        SELECT count(id) as num  FROM t_useheatunit where name=#{name}
        <if test="id !=null">
            and id !=#{id}
        </if>
    </select>

    <!-- 基础信息-->
    <select id="getHeatUnitBaseinfo" resultType="com.javaweb.system.entity.HeatUnit">
        SELECT id,name  FROM t_useheatunit order by id asc;
    </select>

    <!-- 小区编号-->
    <select id="getHeatUnitNoById" resultType="String">
        SELECT   useheatno FROM t_useheatunit  where id=#{id} order by id asc;
    </select>
    <!-- 小区id-->
    <select id="getHeatUnitIdByName" resultType="Integer">
        SELECT   id FROM t_useheatunit  where name=#{name};
    </select>

</mapper>
