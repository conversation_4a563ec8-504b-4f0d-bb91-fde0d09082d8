package com.javaweb.system.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 输入安全验证器
 * 用于验证和过滤用户输入，防止注入攻击
 */
@Component
public class InputSecurityValidator {

    private static final Logger log = LoggerFactory.getLogger(InputSecurityValidator.class);

    /**
     * 危险字符模式 - 用于检测可能的命令注入
     */
    private static final Pattern DANGEROUS_CHARS_PATTERN = Pattern.compile(
        "[|;&$`\\n\\r<>\"'\\\\]"
    );

    /**
     * SQL注入关键词模式
     */
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i).*(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror).*"
    );

    /**
     * 命令注入关键词模式
     */
    private static final Pattern COMMAND_INJECTION_PATTERN = Pattern.compile(
        "(?i).*(cmd|powershell|bash|sh|exec|system|runtime|process|eval|expression).*"
    );

    /**
     * XSS攻击模式
     */
    private static final Pattern XSS_PATTERN = Pattern.compile(
        "(?i).*(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=).*"
    );

    /**
     * 用户名安全验证模式 - 只允许字母、数字、下划线、中文
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_\\u4e00-\\u9fa5]{2,50}$"
    );

    /**
     * 验证码模式 - 只允许字母和数字
     */
    private static final Pattern CAPTCHA_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9]{3,10}$"
    );

    /**
     * 验证用户名安全性
     *
     * @param username 用户名
     * @return 验证结果
     */
    public ValidationResult validateUsername(String username) {
        if (StringUtils.isEmpty(username)) {
            return ValidationResult.error("用户名不能为空");
        }

        // 长度检查
        if (username.length() < 2 || username.length() > 50) {
            return ValidationResult.error("用户名长度必须在2-50个字符之间");
        }

        // 格式检查
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            log.warn("用户名包含非法字符: {}", username);
            return ValidationResult.error("用户名只能包含字母、数字、下划线和中文字符");
        }

        // 危险字符检查
        if (containsDangerousChars(username)) {
            log.warn("用户名包含危险字符: {}", username);
            return ValidationResult.error("用户名包含非法字符");
        }

        // SQL注入检查
        if (containsSqlInjection(username)) {
            log.warn("用户名疑似SQL注入攻击: {}", username);
            return ValidationResult.error("用户名格式不正确");
        }

        return ValidationResult.success();
    }

    /**
     * 验证密码安全性（已加密的密码）
     *
     * @param encryptedPassword 加密后的密码
     * @return 验证结果
     */
    public ValidationResult validateEncryptedPassword(String encryptedPassword) {
        if (StringUtils.isEmpty(encryptedPassword)) {
            return ValidationResult.error("密码不能为空");
        }

        // 检查是否包含危险字符（针对可能的绕过攻击）
        if (containsDangerousChars(encryptedPassword)) {
            log.warn("加密密码包含危险字符");
            return ValidationResult.error("密码格式不正确");
        }

        return ValidationResult.success();
    }

    /**
     * 验证验证码安全性
     *
     * @param captcha 验证码
     * @return 验证结果
     */
    public ValidationResult validateCaptcha(String captcha) {
        if (StringUtils.isEmpty(captcha)) {
            return ValidationResult.error("验证码不能为空");
        }

        // 格式检查
        if (!CAPTCHA_PATTERN.matcher(captcha).matches()) {
            log.warn("验证码格式不正确: {}", captcha);
            return ValidationResult.error("验证码格式不正确");
        }

        // 危险字符检查
        if (containsDangerousChars(captcha)) {
            log.warn("验证码包含危险字符: {}", captcha);
            return ValidationResult.error("验证码格式不正确");
        }

        return ValidationResult.success();
    }

    /**
     * 验证验证码Key的安全性
     *
     * @param captchaKey 验证码Key
     * @return 验证结果
     */
    public ValidationResult validateCaptchaKey(String captchaKey) {
        if (StringUtils.isEmpty(captchaKey)) {
            return ValidationResult.error("验证码Key不能为空");
        }

        // UUID格式检查（36个字符，包含4个连字符）
        if (captchaKey.length() != 36) {
            log.warn("验证码Key长度不正确: {}", captchaKey);
            return ValidationResult.error("验证码Key格式不正确");
        }

        // 危险字符检查
        if (containsDangerousChars(captchaKey)) {
            log.warn("验证码Key包含危险字符: {}", captchaKey);
            return ValidationResult.error("验证码Key格式不正确");
        }

        return ValidationResult.success();
    }

    /**
     * 检查是否包含危险字符
     *
     * @param input 输入字符串
     * @return true-包含危险字符，false-不包含
     */
    private boolean containsDangerousChars(String input) {
        return DANGEROUS_CHARS_PATTERN.matcher(input).find();
    }

    /**
     * 检查是否包含SQL注入关键词
     *
     * @param input 输入字符串
     * @return true-包含SQL注入关键词，false-不包含
     */
    private boolean containsSqlInjection(String input) {
        return SQL_INJECTION_PATTERN.matcher(input).find();
    }

    /**
     * 检查是否包含命令注入关键词
     *
     * @param input 输入字符串
     * @return true-包含命令注入关键词，false-不包含
     */
    private boolean containsCommandInjection(String input) {
        return COMMAND_INJECTION_PATTERN.matcher(input).find();
    }

    /**
     * 检查是否包含XSS攻击代码
     *
     * @param input 输入字符串
     * @return true-包含XSS攻击代码，false-不包含
     */
    private boolean containsXss(String input) {
        return XSS_PATTERN.matcher(input).find();
    }

    /**
     * 清理和转义特殊字符
     *
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public String sanitizeInput(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        return input.replaceAll("[|;&$`\\n\\r<>\"'\\\\]", "")
                   .trim();
    }

    /**
     * HTML转义
     *
     * @param input 输入字符串
     * @return 转义后的字符串
     */
    public String escapeHtml(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }

        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;")
                   .replace("/", "&#x2F;");
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
