
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.system.entity.ItemCate;

import java.util.List;

/**
 * <p>
 * 栏目管理表 服务类
 * </p>
 *
 * 
 * @since 2020-11-07
 */
public interface IItemCateService extends IBaseService<ItemCate> {

    /**
     * 获取栏目名称
     *
     * @param cateId    栏目ID
     * @param delimiter 分隔符
     * @return
     */
    String getCateName(Integer cateId, String delimiter);

    /**
     * 获取栏目列表
     *
     * @return
     */
    List<ItemCate> getCateList();

}
