package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.AlarmConfig;
import com.javaweb.system.query.AlarmConfigQuery;

public interface IAlarmConfigService extends IService<AlarmConfig> {

    /**
     * 根据查询条件获取数据列表
     *
     * @param rulesQuery 查询条件
     * @return
     */
    JsonResult getList(AlarmConfigQuery rulesQuery);


    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(AlarmConfig entity);


    /**
     * 根据ID删除记录
     *
     * @param ids 记录ID
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);
}
