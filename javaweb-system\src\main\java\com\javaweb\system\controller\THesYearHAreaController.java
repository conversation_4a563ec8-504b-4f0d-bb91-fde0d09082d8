package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HesYearHArea;
import com.javaweb.system.query.HesYearHAreaQuery;
import com.javaweb.system.service.THesYearHAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 16:40
 */
@RestController
@RequestMapping("/harea")
public class THesYearHAreaController extends BaseController {

    @Autowired
   private THesYearHAreaService tHesYearHAreaService;


    @GetMapping("/index")
    public JsonResult index(HesYearHAreaQuery hesYearHAreaQuery) {
        return tHesYearHAreaService.getList(hesYearHAreaQuery);
    }


    @Log(title = "供热面积", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HesYearHArea entity){
        return tHesYearHAreaService.edit(entity);
    }


    @Log(title = "供热面积" ,logType =LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HesYearHArea entity){
        return tHesYearHAreaService.edit(entity);
    }


    @Log(title = "供热面积",logType = LogType.DELETE)
    @DeleteMapping("/delete/{hareaIds}")
    public JsonResult delete(@PathVariable("hareaIds") Integer[] hareaIds){
        return tHesYearHAreaService.deleteByIds(hareaIds);
    }

    @GetMapping("/getHareaList")
    public JsonResult getHareaList(){
        return tHesYearHAreaService.getHareaList();
    }

}

