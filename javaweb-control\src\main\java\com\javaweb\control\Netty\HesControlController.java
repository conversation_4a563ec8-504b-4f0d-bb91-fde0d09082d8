package com.javaweb.control.Netty;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/control")
public class HesControlController {

    @Autowired
    private TcpServerHandler tcpServerHandler;


    @GetMapping(path = "/hesControl/{param}")
    public boolean hesControl(@PathVariable String param) throws Exception {
        Map<String, String> parameters = new HashMap<>();
        String[] pairs = param.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            String key = URLDecoder.decode(pair.substring(0, idx), StandardCharsets.UTF_8.name());
            String value = URLDecoder.decode(pair.substring(idx + 1), StandardCharsets.UTF_8.name());
            parameters.put(key, value);
        }

        LocalTime currentTime = LocalTime.now();
        System.out.println(currentTime + " send command ==> hescode: " + parameters.get("hescode")
                + " type: " + parameters.get("type")
                + " field: " + parameters.get("field")
                + " value: " + parameters.get("value")
                + " rate: " + parameters.get("rate"));

        // 写入日志
        ControlCmdPack cmpPack = new ControlCmdPack();
        cmpPack.head.headFlag = 20110517;
        cmpPack.head.hesCode = Integer.valueOf(parameters.get("hescode"));
        cmpPack.control_type = Integer.valueOf(parameters.get("type"));
        cmpPack.control_rate = Integer.valueOf(parameters.get("rate"));
        cmpPack.control_value = Integer.valueOf(parameters.get("value"));
        String strField = parameters.get("field");
        byte[] byteArray = strField.getBytes(StandardCharsets.UTF_8);
        for (int i = 0; i < byteArray.length; i++) {
            cmpPack.control_field[i] = byteArray[i];
        }
        // 下发控制指令到设备
        tcpServerHandler.channelWriteData(cmpPack);
        return true;
    }
}