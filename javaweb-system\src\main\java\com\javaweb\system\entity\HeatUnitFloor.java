package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *小区信息
 * </p>
 *
 * @Date: 2022/12/12 14:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_useheatunitfloor")
public class HeatUnitFloor implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 楼宇名称
     */
    private String floorname;

    /**
     * 小区id
     */
    private Integer useheatunitid;

    /**
     * 小区
     */
    @TableField(exist=false)
    private String name;

    /**
     * 单元数
     */
    private Integer floornum;

    /**
     * 住户数
     */
    private Integer housenum;

    /**
     * 楼宇图
     */
    private String floorpic;

    /**
     * 备注
     */
    private String remarks;


}

