
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.dto.RolePermissionDto;
import com.javaweb.system.entity.Role;

/**
 * <p>
 * 系统角色表 服务类
 * </p>
 *
 * 
 * @since 2020-10-31
 */
public interface IRoleService extends IBaseService<Role> {

    /**
     * 获取角色列表
     *
     * @return
     */
    JsonResult getRoleList();

    /**
     * 获取角色菜单列表
     *
     * @param roleId 角色ID
     * @return
     */
    JsonResult getPermissionList(Integer roleId);

    /**
     * 分配角色菜单数据
     *
     * @param rolePermissionDto 角色权限集合
     * @return
     */
    JsonResult savePermission(RolePermissionDto rolePermissionDto);

}
