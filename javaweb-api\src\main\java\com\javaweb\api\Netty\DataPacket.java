package com.javaweb.api.Netty;

import com.javaweb.common.utils.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Calendar;
import java.util.List;

public class DataPacket {
    private int length;
    private List<DataItem> data;
    private String stationCode; // 站编号
    private String collectDt; // 采集时间
    private Integer runModel; // 运行模式
    private String checksum; // 校验码

    public DataPacket(List<DataItem> data, String stationCode, String collectDt, Integer runModel) {
        this.data = data;
        this.stationCode = stationCode;
        this.collectDt = collectDt;
        this.runModel = runModel;
        this.length = data.size();
        this.checksum = calculateChecksum(); // 计算校验码
    }

    private String calculateChecksum() {
        String  md5Key=null;
        Calendar calendar=Calendar.getInstance();
        int nhour=calendar.get(Calendar.HOUR_OF_DAY);
        int nMinutes=calendar.get(Calendar.MINUTE);
        int nCalc=nhour*nMinutes*getLength();
        String key="tbkj2015"+String.valueOf(nCalc)+getStationCode();
        //MD5加密
        md5Key = DigestUtils.md5Hex(key);
        return md5Key;
    }

    public int getLength() { return length; }
    public List<DataItem> getData() { return data; }
    public String getStationCode() { return stationCode; }
    public String getCollectDt() { return collectDt; }
    public Integer getRunModel() { return runModel; }
    public String getChecksum() { return checksum; }
}