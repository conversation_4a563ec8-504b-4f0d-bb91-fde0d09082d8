package com.javaweb.system.controller;



import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.RepairList;
import com.javaweb.system.query.RepairListQuery;
import com.javaweb.system.service.IRepairListService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * <p>
 * 维修工单表 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/repairlist")
public class RepairListController extends BaseController{

    @Autowired
    private IRepairListService repairlistService;


    /**
     * 获取维修工单列表
     *
     * @param repairlistQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(RepairListQuery repairlistQuery) {
        return repairlistService.getList(repairlistQuery);
    }

    /**
     * 添加维修工单
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "维修工单", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody RepairList entity) {
        return repairlistService.edit(entity);
    }


    /**
     * 编辑维修工单
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "维修工单", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody RepairList entity) {
        return repairlistService.edit(entity);
    }

    /**
     * 删除维修工单
     *
     * @param repairlistIds 职级ID
     * @return
     */
    @Log(title = "维修工单", logType = LogType.DELETE)
    @DeleteMapping("/delete/{repairlistIds}")
    public JsonResult delete(@PathVariable("repairlistIds") Integer[] repairlistIds) {
        return repairlistService.deleteByIds(repairlistIds);
    }


}
