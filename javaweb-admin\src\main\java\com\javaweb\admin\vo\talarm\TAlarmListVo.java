
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.admin.vo.talarm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 列表Vo
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
public class TAlarmListVo {

    /**
    * ID
    */
    private Integer id;

    /**
     * 
     */
    private Integer hesId;

    /**
     * 
     */
    private String alarmField;

    /**
     * 
     */
    private String alarmValue;

    /**
     * 
     */
    private String alarmMaxval;

    /**
     * 
     */
    private String alarmMinval;

    /**
     * 
     */
    private Integer isalarm;

    /**
     * 
     */
    private Integer ishandle;

    /**
     * 
     */
    private Integer dictId;

    /**
     * 
     */
    private Integer alarmType;

    /**
     * 
     */
    private Integer alarmLevel;

    /**
     * 
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmDt;

    /**
     * 
     */
    private String unit;

}