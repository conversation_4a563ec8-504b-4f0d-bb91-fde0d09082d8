package com.javaweb.api.Netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

@Component
public class NettyServer implements CommandLineRunner {

    private final EventLoopGroup udpGroup;
    private final EventLoopGroup udpGroup2; // 新增UDP服务使用的EventLoopGroup

    private int nPort;

    @Autowired
    private UdpServerhandler udpServerhandler;

    @Autowired
    private UdpServerStatushandler udpServerStatushandler;

    @Autowired
    private ApplicationContext context;//上下文


    public NettyServer() {
        this.udpGroup = new NioEventLoopGroup();
        this.udpGroup2 = new NioEventLoopGroup(); // 初始化新的EventLoopGroup
    }

    @Override
    public void run(String... args) throws Exception {
        try {

            // 启动第一个UDP服务
            Bootstrap b1 = new Bootstrap();
            b1.group(udpGroup)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .handler(udpServerhandler);

            // 绑定端口
            Channel ch1 = b1.bind(8882).sync().channel();
            System.out.println("UDP server started on port 8882");

            // 启动第二个UDP服务
            Bootstrap b2 = new Bootstrap();
            b2.group(udpGroup2)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .handler(udpServerStatushandler); // 使用新的处理器

            // 绑定端口
            Channel ch2 = b2.bind(8881).sync().channel();
            System.out.println("UDP server started on port 8881");

            // 保持服务器运行
            ch1.closeFuture().sync();
            ch2.closeFuture().sync();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Failed to start Netty server", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        if (udpGroup != null) {
            udpGroup.shutdownGracefully();
        }
        if (udpGroup2 != null) {
            udpGroup2.shutdownGracefully();
        }
    }
}