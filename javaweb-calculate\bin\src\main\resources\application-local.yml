# 端口配置
server:
  port: 9033
  servlet:
    # 项目的前缀名
    context-path: /api

# 自定义配置
javaweb:
  # 图片域名
  image-url: http://localhost:9033/api/
  # 是否演示模式：true是,false否
  app-debug: false

spring:
  # 配置数据源
  datasource:
    # 使用阿里的Druid连接池
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 填写你数据库的url、登录名、密码和数据库名
    url: ***************************************************************************************************************************************
    username: root
    password: fd2016@)!^


    druid:
      # 连接池的配置信息
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 5
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      webStatFilter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      statViewServlet:
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        #  禁用HTML页面上的“Reset All”功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Redis数据源
  redis:
    # 缓存库默认索引0
    database: 0
    # Redis服务器地址
    host: **************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: fdrl2016@)!^
    # 连接超时时间（毫秒）
    timeout: 6000
    # 默认的数据过期时间，主要用于shiro权限管理
    expire: 2592000
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 1       # 连接池中的最小空闲连接

  servlet:
    multipart:
      # 过滤springmvc的文件上传
      enabled: false
      # 单个文件最大值
      max-file-size: 50MB
      # 上传文件总的最大值
      max-request-size: 100MB

  #邮件配置
  mail:
    # 设置邮箱主机
    host: smtp.qq.com
    # 开启邮箱POP3/SMTP服务，获取客户端授权码(注意并不是邮箱密码，而是授权码)
    password:
    # 邮箱的用户名
    username:
    properties:
      mail:
        smtp:
          # 设置是否需要认证，如果为true,那么用户名和密码就必须的。如果设置false，可以不设置用户名和密码，当然也得看你的对接的平台是否支持无密码进行访问的。
          auth: true
          starttls:
            # STARTTLS[1]  是对纯文本通信协议的扩展。它提供一种方式将纯文本连接升级为加密连接（TLS或SSL），而不是另外使用一个端口作加密通信。
            enable: true
            require: true

  # 阿里短信
  alisms:
    accessKeyId:
    accessKeySecret:
    regionId: cn-hangzhou
    signName: 短信签名
    templateCode:

file:
  #上传的服务器上的映射文件夹
  accessPath: /uploads/
  #静态资源对外暴露的访问路径
  staticAccessPath: /**
  #静态资源实际存储路径
  uploadFolder: /home/<USER>/web/uploads/