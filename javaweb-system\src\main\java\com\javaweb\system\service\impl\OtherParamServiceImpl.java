package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.OtherParam;
import com.javaweb.system.mapper.OtherParamMapper;
import com.javaweb.system.query.OtherParamQuery;
import com.javaweb.system.service.IOtherParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 实时监测 服务实现类
 * </p>
 *
 *
 * @since 2022-12-12
 */

@Service
public class OtherParamServiceImpl extends ServiceImpl<OtherParamMapper, OtherParam> implements IOtherParamService
{
    @Autowired
    private OtherParamMapper otherParamMapper;

    /**
     * 获取所有数据列表
     *
     * @return
     */
    @Override
    public JsonResult getCurrentDataAll(OtherParamQuery currentdataQuery) {
        QueryWrapper<OtherParam>  queryWrapper = new QueryWrapper<>();
        IPage<OtherParam> page = new Page<>(currentdataQuery.getPage(), currentdataQuery.getLimit());
        IPage<OtherParam> pageData = otherParamMapper.selectPage(page, queryWrapper);
        return JsonResult.success(pageData);
    }


    @Override
    public JsonResult edit(OtherParam entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }




    /**
     * 获取系统参数信息
     *
     * @return
     */
    @Override
    public JsonResult getOtherParamInfo() {

        // 获取参数信息
        OtherParam otherParam = otherParamMapper.selectById(1);
        return JsonResult.success(otherParam);

    }

}





