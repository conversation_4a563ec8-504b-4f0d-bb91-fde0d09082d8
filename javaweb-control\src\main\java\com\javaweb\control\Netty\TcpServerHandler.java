package com.javaweb.control.Netty;

import com.javaweb.common.utils.JsonResult;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.time.Duration;
import java.time.LocalTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

//@RestController
@Component
@Scope("prototype")//用于定义 Bean 的作用域 : 每次请求该 Bean 时都会创建一个新的实例。适用于那些需要保持独立状态的组件。
public class TcpServerHandler extends SimpleChannelInboundHandler<MyHeartbeatMessage> {

    public class HesState {
            public int hesCode;
            public LocalTime lastUpdateTime;
        }

    // 创建一个 Map 来保存所有的 ChannelHandlerContext
    private static final Map<HesState, ChannelHandlerContext> channels = new ConcurrentHashMap<>();

    @Autowired
    private  DataSource dataSource;
    // 创建一个 ScheduledExecutorService 实例
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        super.handlerAdded(ctx);
        // 启动一个定时任务，每30秒执行一次
        scheduler.scheduleAtFixedRate(this::checkConnectionTimeouts, 0, 30, TimeUnit.SECONDS);
    }


    private void checkConnectionTimeouts() {
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 遍历 channels 映射
        channels.entrySet().removeIf(entry -> {
            HesState hesState = entry.getKey();
            // 计算最后更新时间与当前时间的差值
            Duration duration = Duration.between(hesState.lastUpdateTime, currentTime);
            // 如果差值超过30秒，则移除该连接
            //System.out.println(hesState.hesCode+"==start===" + hesState.lastUpdateTime +"  end==="+currentTime +" 差值 "+duration.getSeconds());
            if (duration.getSeconds() > 30) {
                // 在这里关闭 ChannelHandlerContext
                entry.getValue().close();
                updateHesRunState(hesState, 0);
                System.out.println("Removing inactive connection: " + hesState.hesCode);
                return true;
            }
            return false;
        });
    }
    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        // 当 Channel 关闭连接时，需要从 Map 中移除对应的 ChannelHandlerContext
        for (Map.Entry<HesState, ChannelHandlerContext> entry : channels.entrySet())
        {
            if (entry.getValue().equals(ctx))
            {
                HesState hesState = entry.getKey();
                channels.remove(hesState);
                LocalTime currentTime = LocalTime.now();
                updateHesRunState(hesState, 0);
                System.out.println(currentTime+" hescode=" +String.valueOf(hesState.hesCode) +"  disconnected ×××××××");
                break;
            }
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 当 Channel 关闭连接时，需要从 Map 中移除对应的 ChannelHandlerContext
        for (Map.Entry<HesState, ChannelHandlerContext> entry : channels.entrySet())
        {
            if (entry.getValue().equals(ctx))
            {
                HesState hesState = entry.getKey();
                channels.remove(hesState);
                LocalTime currentTime = LocalTime.now();
                updateHesRunState(hesState, 0);
                System.out.println(currentTime+" channelInactive  hescode=" +String.valueOf(hesState.hesCode) +"  disconnected ×××××××");
                break;
            }
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, MyHeartbeatMessage msg) throws Exception {
        // 检查消息是否是心跳包 MyHeartbeatMessage
        LocalTime currentTime = LocalTime.now();
        if (msg.getIsValidity())
        {
            // 如果是心跳包，使用 StationId 作为标识将 ChannelHandlerContext 添加到 Map 中
            Integer hesCode = msg.getHesCode();
            // 判断 channels 是否存在连接 如果不存在则加入
            if(!hesIsExist(hesCode))
            {
                HesState hesState= new HesState();
                hesState.hesCode = hesCode;
                hesState.lastUpdateTime = currentTime;
                channels.put(hesState, ctx);
                updateHesRunState(hesState, 1);
                System.out.println(currentTime+" hesCode= " + hesCode.toString()+ " insert to channels..");
            }
            System.out.println(currentTime+" hesCode= " + hesCode.toString()+ " Hearbeat Data √√√√√√");
        } else {
            // 处理其他类型的消息
            System.out.println(currentTime+" Received message:  is null from " + ctx.channel().remoteAddress());
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }

    public boolean hesIsExist(Integer nHesCode){
        for (Map.Entry<HesState, ChannelHandlerContext> entry : channels.entrySet())
        {
            HesState hesState = entry.getKey();
            if(hesState.hesCode == nHesCode)
            {
                LocalTime currentTime = LocalTime.now();
                hesState.lastUpdateTime = currentTime;
                return  true;
            }
        }
        return false;
    }

    public JsonResult channelWriteData(ControlCmdPack controlCmdPack){
        // 根据 msg 格式解析命令进行下发
        Integer hesCode = controlCmdPack.head.hesCode;
        // 1. 根据stationId 获取tcp通道
        //ChannelHandlerContext specialCtx = channels.get(hesCode);
        ChannelHandlerContext specialCtx = getChannelHandlerContextByHesCode(hesCode);
        if(specialCtx != null)
        {
            specialCtx.writeAndFlush(controlCmdPack);
            System.out.println("send command to station , hescode="+hesCode);
        }
        return JsonResult.success("OK");
    }

    public ChannelHandlerContext getChannelHandlerContextByHesCode(int hesCode) {
        for (Map.Entry<HesState, ChannelHandlerContext> entry : channels.entrySet()) {
            if (entry.getKey().hesCode == hesCode) {
                return entry.getValue();
            }
        }
        return null;
    }


    private void updateHesRunState(HesState hesState, int isrun) {
        try (Connection connection = dataSource.getConnection()) {
            String sql = "update  t_hes set isrun=? where hescode=?";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                // 设置参数
                pStatement.setInt(1, isrun);
                pStatement.setInt(2, hesState.hesCode);
                pStatement.executeUpdate();
            }
        } catch (Exception e) {
            System.err.println("update hescode="+String.valueOf(hesState.hesCode)+" error: " + e.getMessage());
        }
    }
}