<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.UserMapper">


    <!-- 获取用户登录次数，登录时长-->
    <select id="getUserInfo" resultType="com.javaweb.system.entity.User">
        select * from tb_user where username =#{userName} order by id asc;
    </select>


    <!--更新用户状态,次数-->
    <update id="updateUserState" >
        UPDATE tb_user
        SET  status=#{status},login_num=#{loginNum}
        WHERE username =#{userName};
    </update>

    <!--更新登录错误次数以及时间-->
    <update id="updateLoginCount" parameterType="String">
        UPDATE tb_user
        SET  login_num=login_num+1,login_time = SYSDATE()
        WHERE username =#{userName};
    </update>

    <!-- 获取用户登录次数，登录时长-->
    <select id="getUserInfo" resultType="com.javaweb.system.entity.User">
        select * from tb_user where username =#{userName} order by id asc;
    </select>

    <update id="updateLoginErrNum" parameterType="String">
        UPDATE tb_user SET  login_num=0 WHERE username =#{userName};
    </update>

    <!-- 获取用户信息 -->
    <select id="getUserByroleIds" resultType="com.javaweb.system.entity.User">

        select id ,realname,role_id from tb_user
        where  role_id in
        <foreach collection="roldIds" item="item" index="index" open="(" separator="," close=")" >
            (#{item})
        </foreach>
        order by id asc
    </select>
</mapper>
