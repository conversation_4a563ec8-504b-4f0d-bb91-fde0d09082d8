package com.javaweb.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.service.IRecvDataService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


/**
 * <p>
 * 数据传输 前端控制器
 * </p>
 *
 *
 * @since 2020-10-31
 */
@RestController
@RequestMapping("/recv")
public class RecvDataController {

    @Autowired
    private IRecvDataService recvdataService;

    /**
     * 获取验证码
     *
     * @param request 网络请求
     * @return
     */
    @PostMapping(value="/recvdata",produces = "application/json;charset=UTF-8")
    public JsonResult userLogin(@RequestBody JSONObject data, HttpServletRequest request)
    {

        return recvdataService.getRecvdata(data, request);
    }


    /**
     * 接收换热站数据
     *
     * @param request 网络请求
     * @return
     */
    @PostMapping("/hesdata")
    public JsonResult getRecvHesdata(@RequestBody String data, HttpServletRequest request)
    {

        recvdataService.getRecvHesdata(data, request);
        return JsonResult.success("OK");
    }


    /**
     * 接收气象站数据
     *
     * @param request 网络请求
     * @return
     */
    @GetMapping("/wsdata")
    public JsonResult getRecvWsdata(@RequestParam String data, HttpServletRequest request)
    {

        return recvdataService.getRecvWsdata(data, request);
    }

    /**
     * 接收室内采集数据
     *
     * @param request 网络请求
     * @return
     */
    @GetMapping("/indoortdata")
    public JsonResult getRecvIndoorTdata(@RequestParam String data, HttpServletRequest request)
    {

        return recvdataService.getRecvIndoorTdata(data, request);
    }
}
