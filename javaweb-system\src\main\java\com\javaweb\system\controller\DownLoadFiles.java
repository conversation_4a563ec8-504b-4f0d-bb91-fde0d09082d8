package com.javaweb.system.controller;

import org.springframework.core.io.InputStreamResource;
import com.javaweb.common.common.BaseController;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.*;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
public class DownLoadFiles extends BaseController {
//    @RequestMapping(path = "/download", method = RequestMethod.GET)
//    public ResponseEntity<InputStreamResource> download(String param) throws IOException {
//        // 根据param参数获取要下载的文件
//        //....
//
//         //示例如下
//        String currentDirectoryFile = System.getProperty("user.dir")+"/uploads/level.xlsx";
//        // 创建一个输入流 uploads
//        File file = new File(currentDirectoryFile);
//        String fileName = file.getName();
//        FileInputStream fileInputStream = new FileInputStream(file);
//        InputStreamResource resource = new InputStreamResource(fileInputStream);
//        HttpHeaders headers = new HttpHeaders();
//        headers.add(HttpHeaders.CONTENT_DISPOSITION,"attachment; filename="+fileName);
//        return  ResponseEntity.ok()
//                .headers(headers)
//                .contentLength(file.length())
//                .contentType(MediaType.APPLICATION_OCTET_STREAM)
//                .body(resource);
//    }

    @RequestMapping("/uploads/{filename:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String filename) throws MalformedURLException {
        Path filePath = Paths.get(System.getProperty("user.dir")+"/uploads/", filename);
        Resource resource = new UrlResource(filePath.toUri());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
}
