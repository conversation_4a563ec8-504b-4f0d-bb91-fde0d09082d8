
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.system.entity.RoleMenu;

/**
 * <p>
 * 角色菜单关联表 服务类
 * </p>
 *
 * 
 * @since 2020-10-30
 */
public interface IRoleMenuService extends IBaseService<RoleMenu> {

    /**
     * 根据角色ID删除角色菜单关系数据
     *
     * @param roleId
     */
    boolean deleteRoleMenus(Integer roleId);

}
