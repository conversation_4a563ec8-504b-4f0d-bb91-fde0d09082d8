package com.javaweb.system.service.impl;

import ch.qos.logback.core.joran.spi.ElementSelector;
import com.alibaba.druid.sql.visitor.functions.If;
import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.HesDto;
import com.javaweb.system.service.IPhoneAppApiService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import javax.sql.DataSource;

/**
 * <p>
 * 手机app接口类
 * </p>
 *
 *
 * @since 2023-1-2
 */

@Service
public class PhoneAppApiServiceImpl implements IPhoneAppApiService {

    @Autowired
    DataSource dataSoure;
    /**
     * 获取换热站列表
     *
     * @return
     */
    @Override
    public JsonResult getHesList() {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT id, name, hescode, runMode, equipmentNum, isrun,isHeating FROM t_hes WHERE isUsed = 1 order by name desc")) {

            List<HesDto> hesList = new ArrayList<>();
            ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next())
                {
                    HesDto hesDto = new HesDto();
                    hesDto.setId(rSet.getInt("id"));
                    hesDto.setName(rSet.getString("name"));
                    hesDto.setHescode(rSet.getInt("hescode"));
                    hesDto.setRunMode(rSet.getInt("runMode"));
                    hesDto.setEquipmentNum(rSet.getInt("equipmentNum"));
                    hesDto.setIsrun(rSet.getInt("isrun"));
                    hesDto.setIsheating(rSet.getInt("isHeating"));
                    hesList.add(hesDto);
                }
            }
            if (hesList.size() > 0) {
                for (int i = 0; i < hesList.size(); i++) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    String sqlpre = "";
                    int num = hesList.get(i).getEquipmentNum();

                    if (num == 2 || num == 21) {
                        sqlpre = ", LOW_S_S_T1, LOW_S_B_T1";
                    } else if (num == 3) {
                        sqlpre = ", LOW_S_S_T1, LOW_S_B_T1, MID_S_S_T1, MID_S_B_T1";
                    } else if (num == 4) {
                        sqlpre = ", LOW_S_S_T1, LOW_S_B_T1, MID_S_S_T1, MID_S_B_T1, FOUR_S_S_T1, FOUR_S_B_T1";
                    }

                    Calendar calendar = Calendar.getInstance();
                    int year = calendar.get(Calendar.YEAR);

                    String sql = "SELECT collectdt, F_S_F, F_S_T1, F_B_T1, S_S_T1, S_B_T1 " + sqlpre +
                            " FROM t_hes_data_" + hesList.get(i).getHescode() +
                            " ORDER BY id DESC LIMIT 1";

                    try (PreparedStatement pStatement2 = connection.prepareStatement(sql);
                         ResultSet rSet2 = pStatement2.executeQuery()) {

                        if (rSet2.next()) {
                            resultMap.put("id", hesList.get(i).getId());
                            resultMap.put("name", hesList.get(i).getName());
                            resultMap.put("hescode", hesList.get(i).getHescode());
                            resultMap.put("runmode", hesList.get(i).getRunMode());
                            resultMap.put("equipmentNum", 1); // 初始值
                            resultMap.put("isrun", hesList.get(i).getIsrun());
                            resultMap.put("isheating", hesList.get(i).getIsheating());
                            resultMap.put("dataExist", "OK");
                            resultMap.put("F_S_T1", rSet2.getString("F_S_T1"));
                            resultMap.put("F_B_T1", rSet2.getString("F_B_T1"));
                            resultMap.put("S_S_T1", rSet2.getString("S_S_T1"));
                            resultMap.put("S_B_T1", rSet2.getString("S_B_T1"));
                            resultMap.put("dt", rSet2.getString("collectdt"));

                            if (num == 2 || num == 21) {
                                resultMap.put("equipmentNum", 2);
                                resultMap.put("LOW_S_S_T1", rSet2.getString("LOW_S_S_T1"));
                                resultMap.put("LOW_S_B_T1", rSet2.getString("LOW_S_B_T1"));
                            } else if (num == 3) {
                                resultMap.put("equipmentNum", 3);
                                resultMap.put("MID_S_S_T1", rSet2.getString("MID_S_S_T1"));
                                resultMap.put("MID_S_B_T1", rSet2.getString("MID_S_B_T1"));
                            } else if (num == 4) {
                                resultMap.put("equipmentNum", 4);
                                resultMap.put("FOUR_S_S_T1", rSet2.getString("FOUR_S_S_T1"));
                                resultMap.put("FOUR_S_B_T1", rSet2.getString("FOUR_S_B_T1"));
                            }
                        } else {
                            resultMap.put("id", hesList.get(i).getId());
                            resultMap.put("name", hesList.get(i).getName());
                            resultMap.put("hescode", hesList.get(i).getHescode());
                            resultMap.put("runmode", hesList.get(i).getRunMode());
                            resultMap.put("equipmentNum", hesList.get(i).getEquipmentNum());
                            resultMap.put("isrun", hesList.get(i).getIsrun());
                            resultMap.put("isheating", hesList.get(i).getIsheating());
                            resultMap.put("F_S_T1", 0.0);
                            resultMap.put("F_B_T1", 0.0);
                            resultMap.put("S_S_T1", 0.0);
                            resultMap.put("S_B_T1", 0.0);
                            resultMap.put("dt", "2024-01-01 00:00:00");
                            resultMap.put("dataExist", "NO");
                        }
                    }
                    lst.add(resultMap);
                }
                return JsonResult.success(lst);
            } else {
                return JsonResult.error("没有找到数据");
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 获取小区列表
     *
     * @return
     */
    @Override
    public JsonResult getHesUnitList() {
        String sql = "SELECT id, name, unittype, useheatno FROM t_useheatunit order by name desc";
        List<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(sql)) {
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) { // 使用 while 循环来迭代所有结果
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("id", rSet.getString("id"));
                    resultMap.put("name", rSet.getString("name"));
                    resultMap.put("unittype", rSet.getString("unittype"));
                    resultMap.put("useheatno", rSet.getString("useheatno"));
                    lst.add(resultMap);
                }
                if (lst.isEmpty()) {
                    return JsonResult.error("没有找到数据");
                }
            }
            return JsonResult.success(lst);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    @Override
    public JsonResult getIndoorDataList(Integer nLevel, String name) {
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        String sql = "SELECT t_useheatunit.name, t_hes_indoort.* " +
                "FROM t_hes_indoort " +
                "JOIN t_useheatunit ON t_hes_indoort.useheatunitid = t_useheatunit.id ";
        if (nLevel == 1) {
            sql += "WHERE t_hes_indoort.username = ? ";
        }
        sql += "ORDER BY t_hes_indoort.id DESC limit 10;";

        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(sql)) {
            // 设置参数
            if (nLevel == 1) {
                pStatement.setString(1, name);
            }
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) { // 使用while循环来处理所有行
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("id", rSet.getString("id"));
                    resultMap.put("name", rSet.getString("name"));
                    resultMap.put("floorname", rSet.getString("floorname"));
                    resultMap.put("floornum", rSet.getString("floornum"));
                    resultMap.put("housenum", rSet.getString("housenum"));
                    resultMap.put("installT", rSet.getString("install_T"));
                    resultMap.put("installDt", rSet.getString("install_dt"));
                    lst.add(resultMap);
                }
                if (lst.isEmpty()) {
                    return JsonResult.error("没有找到数据");
                }
                return JsonResult.success(lst);
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }
    /**
     * 获取换热站最新数据
     *
     * @return
     */
    @Override
    public JsonResult getHesData(String hescode) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT MPFieldDefine, MPDesc, unit FROM T_HesMonitorPointDefine WHERE IsUsed = 1 AND HesCode = ?")) {

            // 设置参数
            pStatement.setString(1, hescode);

            List<String> lst = new ArrayList<>();
            List<String> desclst = new ArrayList<>();
            List<String> unitlst = new ArrayList<>();
            ArrayList<LinkedHashMap<Object, Object>> rstlst = new ArrayList<>();

            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    lst.add(rSet.getString("MPFieldDefine"));
                    desclst.add(rSet.getString("MPDesc"));
                    unitlst.add(rSet.getString("unit"));
                }
            }

            if (lst.size() > 0) {
                StringBuilder sqlpre = new StringBuilder();
                for (String field : lst) {
                    if (sqlpre.length() > 0) {
                        sqlpre.append(", ");
                    }
                    sqlpre.append(field);
                }

                String sql = "SELECT collectdt, " + sqlpre.toString() +
                        " FROM t_hes_data_" + hescode + " ORDER BY id DESC LIMIT 1";

                try (PreparedStatement pStatement2 = connection.prepareStatement(sql);
                     ResultSet rSet2 = pStatement2.executeQuery()) {

                    if (rSet2.next()) {
                        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                        resultMap.put("name", "dt");
                        resultMap.put("value", rSet2.getString("collectdt"));
                        resultMap.put("desc", "采集时间");
                        rstlst.add(resultMap);

                        for (int i = 0; i < lst.size(); i++) {
                            LinkedHashMap<Object, Object> resultMap1 = new LinkedHashMap<>();
                            String rawValue=rSet2.getString(lst.get(i));
                            if(StringUtils.isEmpty(rawValue))
                            {
                                rawValue="0";
                            }
                            String val = String.format("%.2f", Double.valueOf(rawValue));
                            resultMap1.put("name", lst.get(i));
                            resultMap1.put("value", val);
                            resultMap1.put("desc", desclst.get(i));
                            resultMap1.put("unit", unitlst.get(i));
                            rstlst.add(resultMap1);
                        }
                    } else {
                        return JsonResult.error("没有找到数据");
                    }
                }
            }

            return JsonResult.success(rstlst);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }


    /**
     * 获取换热站历史数据
     *
     * @return
     */
    @Override
    public JsonResult getHesHistoriesData(Integer hescode,String dt,String field) {
        Connection connection = null;
        Statement pStatement = null;
        ResultSet rSet = null;
        List<String> lst = new ArrayList<>();
        List<Integer> timeList = new ArrayList<>();
        List<Double> valList = new ArrayList<>();
        // 构建内部JSON对象
        JSONObject innerJson = new JSONObject();
        try {
            connection = dataSoure.getConnection();

            String sql = "select HOUR(CollectDT) AS dt, CAST(AVG( " + field + " ) as decimal(10,2)) as  " + field +
                    " from t_hes_data_" + hescode +
                    " where collectdt BETWEEN '" + dt + " 00:00:00' AND '" + dt + " 23:59:59'" +
                    "  GROUP BY dt ";
            pStatement = connection.createStatement();
            rSet = pStatement.executeQuery(sql);
            while (rSet.next()) {
                timeList.add(rSet.getInt("dt"));
                valList.add(rSet.getDouble(field));
            }
            innerJson.put("time", timeList);
            innerJson.put("Field", valList);
            rSet.close();
        } catch (SQLException ex) {
            return JsonResult.error(ex);
        }finally {
            // 关闭资源
            if (rSet != null) {
                try {
                    rSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (pStatement != null) {
                try {
                    pStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return JsonResult.success(innerJson);
    }


        /**
         * 获取气象站数据
         *
         * @return
         */
    @Override
    public JsonResult getWsData() {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT T, H, R, S, CollectDT FROM T_WeatherStationData ORDER BY id DESC LIMIT 1")) {

            LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();

            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.next()) {
                    resultMap.put("T", rSet.getString("T"));
                    resultMap.put("H", rSet.getString("H"));
                    resultMap.put("R", rSet.getString("R"));
                    resultMap.put("S", rSet.getString("S"));
                    resultMap.put("DT", rSet.getString("CollectDT"));
                } else {
                    return JsonResult.error("没有找到数据");
                }
            }

            return JsonResult.success(resultMap);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 获取气象站历史数据
     *
     * @return
     */
    @Override
    public JsonResult getWsHistoriesData(String dt) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT T, H, R, S, CollectDT FROM T_WeatherStationData " +
                             "WHERE CollectDT BETWEEN ? AND ? " +
                             "ORDER BY id asc")) {

            // 设置参数
            pStatement.setString(1, dt + " 00:00:00");
            pStatement.setString(2, dt + " 23:59:59");

            List<String> timeList = new ArrayList<>();
            List<Double> tList = new ArrayList<>();
            List<Double> hList = new ArrayList<>();
            List<Double> rList = new ArrayList<>();
            List<Double> sList = new ArrayList<>();

            JSONObject innerJson = new JSONObject();

            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    timeList.add(rSet.getString("CollectDT"));
                    tList.add(rSet.getDouble("T"));
                    hList.add(rSet.getDouble("H"));
                    rList.add(rSet.getDouble("R"));
                    sList.add(rSet.getDouble("S"));
                }
            }

            innerJson.put("dt", timeList);
            innerJson.put("T", tList);
            innerJson.put("H", hList);
            innerJson.put("R", rList);
            innerJson.put("S", sList);

            return JsonResult.success(innerJson);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }


    /**
     * 获取气象站历史数据
     *
     * @return
     */

    @Override
    public JsonResult getWsHistoryData() {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT T, H, R, S, CollectDT FROM T_WeatherStationData ORDER BY id DESC LIMIT 100")) {

            ArrayList<LinkedHashMap<Object, Object>> rstlst = new ArrayList<>();

            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("T", rSet.getString("T"));
                    resultMap.put("H", rSet.getString("H"));
                    resultMap.put("R", rSet.getString("R"));
                    resultMap.put("S", rSet.getString("S"));
                    resultMap.put("dt", rSet.getString("CollectDT"));
                    rstlst.add(resultMap);
                }
            }

            return JsonResult.success(rstlst);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }
    /**
     * 登录验证
     *
     * @return
     */
    @Override
    public JsonResult uLogin(String phone, String password) {
        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
        try (Connection connection = dataSoure.getConnection();
             Statement statement = connection.createStatement()) {

            password = getMd5(password);
            String sql = "SELECT * FROM t_userapp WHERE phone = ? AND pwd = ?";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                pStatement.setString(1, phone);
                pStatement.setString(2, password);
                try (ResultSet rSet = pStatement.executeQuery()) {
                    if (rSet.next()) {
                        resultMap.put("id", rSet.getString("id"));
                        resultMap.put("name", rSet.getString("name"));
                        resultMap.put("pwd", rSet.getString("pwd"));
                        resultMap.put("phone", rSet.getString("phone"));
                        resultMap.put("headpic", rSet.getString("headpic"));
                        resultMap.put("userwhite_id", rSet.getString("userwhite_id"));
                        resultMap.put("nlevel", rSet.getString("nlevel"));
                    } else {
                        return JsonResult.error("账号或密码错误！");
                    }
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
        return JsonResult.success(resultMap);
    }
    /**
     * 注册验证
     *
     * @return
     */

    @Override
    public JsonResult regVer(String phone, String password) {
        try (Connection connection = dataSoure.getConnection();
             Statement statement = connection.createStatement()) {

            // 对密码进行MD5加密
            password = getMd5(password);

            // 查询t_userwhite表
            String sql = "SELECT * FROM t_userwhite WHERE phone = ?";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                pStatement.setString(1, phone);
                try (ResultSet rSet = pStatement.executeQuery()) {
                    if (rSet.next()) {
                        String id = rSet.getString("id");
                        String name = rSet.getString("name");
                        String nlevel = rSet.getString("nlevel");

                        // 获取当前时间
                        LocalDateTime now = LocalDateTime.now();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        String dt = now.format(formatter);

                        // 查询t_userapp表
                        sql = "SELECT * FROM t_userapp WHERE phone = ?";
                        try (PreparedStatement pStatement2 = connection.prepareStatement(sql)) {
                            pStatement2.setString(1, phone);
                            try (ResultSet rSet2 = pStatement2.executeQuery()) {
                                if (rSet2.next()) {
                                    return JsonResult.error("手机号/工号已存在，不可重复注册！");
                                } else {
                                    // 插入新用户
                                    sql = "INSERT INTO t_userapp (name, pwd, phone, nlevel, dt, userwhite_id) VALUES (?, ?, ?, ?, ?, ?)";
                                    try (PreparedStatement insertStatement = connection.prepareStatement(sql)) {
                                        insertStatement.setString(1, name);
                                        insertStatement.setString(2, password);
                                        insertStatement.setString(3, phone);
                                        insertStatement.setString(4, nlevel);
                                        insertStatement.setString(5, dt);
                                        insertStatement.setString(6, id);
                                        int rowsAffected = insertStatement.executeUpdate();
                                        if (rowsAffected > 0) {
                                            return JsonResult.success("注册成功");
                                        } else {
                                            return JsonResult.error("注册失败");
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        return JsonResult.error("需要审核，请联系管理员进行处理");
                    }
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("注册失败: " + ex.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @return
     */
    @Override
    public JsonResult modifyPwd(Integer id, String password) {
        try (Connection connection = dataSoure.getConnection()) {
            // 对新密码进行MD5加密
            String newpassword = getMd5(password);

            // 使用PreparedStatement来执行更新操作
            String sql = "UPDATE t_userapp SET pwd = ? WHERE id = ?";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                pStatement.setString(1, newpassword);
                pStatement.setInt(2, id);

                int rst = pStatement.executeUpdate();
                if (rst > 0) {
                    return JsonResult.success("修改密码成功");
                } else {
                    return JsonResult.error("修改密码失败");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 修改密码
     *
     * @return
     */
    @Override
    public JsonResult updateUserLocation(String phone, String lon,String lat) {
        try (Connection connection = dataSoure.getConnection()) {
            // 使用PreparedStatement来执行更新操作
            String sql = "UPDATE t_userapp SET lon = ?, lat = ?  WHERE  phone = ? ";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                pStatement.setString(1, lon);
                pStatement.setString(2, lat);
                pStatement.setString(3, phone);

                int rst = pStatement.executeUpdate();
                if (rst > 0) {
                    return JsonResult.success("更新位置成功");
                } else {
                    return JsonResult.error("更新位置失败");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 个人信息
     *
     * @return
     */
    @Override
    public JsonResult getPersonalInfo(String userId) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement("SELECT * FROM t_user WHERE id = ?")) {
            // 设置参数
            pStatement.setString(1, userId);

            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("Name", rSet.getString("Name"));
                    resultMap.put("pwd", rSet.getString("pwd"));
                    resultMap.put("Level", rSet.getString("Level"));
                    resultMap.put("ntype", rSet.getString("ntype"));

                    return JsonResult.success(resultMap);
                } else {
                    return JsonResult.error("用户不存在");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 白名单列表
     *
     * @return
     */
    @Override
    public JsonResult whitelistList(Integer id, Integer nlevel) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement("SELECT * FROM T_UserWhite WHERE nlevel <= ? AND id != ?")) {

            // 设置参数
            pStatement.setInt(1, nlevel);
            pStatement.setInt(2, id);

            ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.isBeforeFirst()) {
                    while (rSet.next()) {
                        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                        resultMap.put("id", rSet.getString("id"));
                        resultMap.put("name", rSet.getString("name"));
                        resultMap.put("phone", rSet.getString("phone"));
                        resultMap.put("nlevel", rSet.getString("nlevel"));
                        lst.add(resultMap);
                    }
                    return JsonResult.success(lst);
                } else {
                    return JsonResult.error("没有符合条件的白名单用户");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 白名单添加
     *
     * @return
     */
    @Override
    public JsonResult whiteUserAdd(String name, String phone) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement checkStatement = connection.prepareStatement("SELECT * FROM t_userwhite WHERE phone = ?");
             PreparedStatement insertStatement = connection.prepareStatement("INSERT INTO t_userwhite (name, phone, nlevel) VALUES (?, ?, 1)")) {

            // 检查用户是否已存在
            checkStatement.setString(1, phone);
            try (ResultSet rSet = checkStatement.executeQuery()) {
                if (rSet.isBeforeFirst() && rSet.next()) {
                    return JsonResult.error("用户已存在");
                }
            }

            // 插入新用户
            insertStatement.setString(1, name);
            insertStatement.setString(2, phone);

            int rst = insertStatement.executeUpdate();
            if (rst > 0) {
                return JsonResult.success("添加成功");
            } else {
                return JsonResult.error("添加失败");
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("添加失败: " + ex.getMessage());
        }
    }

    /**
     * 白名单编辑
     *
     * @return
     */
    @Override
    public JsonResult whiteUserEdit(Integer id, Integer nlevel) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement updateWhiteStatement = connection.prepareStatement("UPDATE T_UserWhite SET nlevel = ? WHERE id = ?");
             PreparedStatement selectAppStatement = connection.prepareStatement("SELECT * FROM T_UserApp WHERE UserWhite_id = ?");
             PreparedStatement updateAppStatement = connection.prepareStatement("UPDATE T_UserApp SET nlevel = ? WHERE UserWhite_id = ?")) {

            // 更新T_UserWhite表
            updateWhiteStatement.setInt(1, nlevel);
            updateWhiteStatement.setInt(2, id);
            int ret = updateWhiteStatement.executeUpdate();

            if (ret > 0) {
                // 查询T_UserApp表
                selectAppStatement.setInt(1, id);
                try (ResultSet rSet = selectAppStatement.executeQuery()) {
                    if (rSet.isBeforeFirst() && rSet.next()) {
                        // 更新T_UserApp表
                        updateAppStatement.setInt(1, nlevel);
                        updateAppStatement.setInt(2, id);
                        updateAppStatement.executeUpdate();
                    }
                }
                return JsonResult.success("设置成功");
            } else {
                return JsonResult.error("设置失败");
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 计算参数
     *
     * @return
     */
    @Override
    public JsonResult calcParam(String hescode)
    {
        Connection connection = null;
        Statement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        String calc_model="1";
        String field_index="1";
        String area="0.5";
        String tw = "5.0";
        String tn = "18.0";
        String T = "";
        String H = "";
        String R = "";
        String S = "";
        String dt = "";
        try
        {
            connection = dataSoure.getConnection();
            String sql = "select calc_mode,sst_field_index from t_hes where hescode ="+hescode;
            pStatement = connection.createStatement();
            rSet = pStatement.executeQuery(sql);
            if (rSet.next())
            {
                calc_model=rSet.getString("calc_mode");
                field_index=rSet.getString("sst_field_index");
            }
            rSet.close();
            sql="select heara from t_hes_year_Harea  where hescode ="+hescode+" order by hyear desc LIMIT 1";
            rSet = pStatement.executeQuery(sql);
            if (rSet.next())
            {
                area=rSet.getString("heara");
            }
            rSet.close();
            sql="select tw from t_hs";
            rSet = pStatement.executeQuery(sql);
            if (rSet.next())
            {
                if(rSet.getString("tw").equals("NO"))
                {
                    rSet.close();
                    //获取当前气温
                    sql = "select * from T_WeatherStationData order by id desc LIMIT 1";
                    rSet = pStatement.executeQuery(sql);
                    if(rSet.next())
                    {
                        tw = rSet.getString("T");
                        T = rSet.getString("T");
                        H = rSet.getString("H");
                        R = rSet.getString("R");
                        S = rSet.getString("S");
                        dt= rSet.getString("CollectDT");
                    }
                }
                else
                {
                    tw = rSet.getString("tw");
                }
            }
            if (Float.parseFloat(tw) > 10)
            {
                tw= "10";
            }
            rSet.close();
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
            String str_ymdhms = now.format(formatter);
            sql = "select tn1 from T_Hes_Rules where hescode="+hescode+" and dt='"+str_ymdhms+"' LIMIT 1";
            rSet = pStatement.executeQuery(sql);
            String tn2 = "0";
            if(rSet.next())
            {
                tn =rSet.getString("tn1");
            }
            else
            {
                tn = "20";
            }
            String sst = "60";
            sql = "select sst from T_Hes_CalcParam3  where hescode ="+hescode+ " order by id desc";
            rSet.close();
            rSet = pStatement.executeQuery(sql);
            if(rSet.next())
            {
                sst =rSet.getString("sst");
            }
            Rst+= "{'rst':'1','tw':'"+tw+
                    "','T':'"+T+"','H':'"+H+
                    "','R':'"+R+"','S':'"+S+
                    "','dt':'"+dt+"','tn':'"+tn+
                    "','area':'"+area+"','sst':'"+sst+
                    "','calc_model':'"+calc_model+
                    "','field_index':'"+field_index+"','data':[";
            sql = "select run_h,control_t from t_hes_rule where hescode="+hescode+" order by run_h asc";
            rSet.close();
            rSet = pStatement.executeQuery(sql);
            if(rSet.isBeforeFirst())
            {
                String strrst = "";
                while (rSet.next())
                {
                    strrst +="{'run_h':'"+rSet.getString("run_h")+
                            "','sst':'"+rSet.getString("control_t")+"'},";

                }
                Rst += strrst+"]}";
                Rst += "]}";
                Rst =Rst.replace(",]", "]");
                Rst =Rst.replace("\'", "\"");
            }else{
                return JsonResult.error();
            }
            pStatement.close();
            rSet.close();
        }catch (SQLException ex) {
            return JsonResult.error();
        }
        return JsonResult.success(Rst);
    }
    /**
     * 换热站供回水数据
     *
     * @return
     */
    @Override
    public JsonResult hesReflowData(String hescode, String dt) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT F_S_T1, F_B_T1, S_S_T1, S_B_T1, TIME_FORMAT(CollectDT, '%H:%i:%s') AS dt " +
                             "FROM t_hes_data_" + hescode + " " +
                             "WHERE CollectDT BETWEEN ? AND ? " +
                             "ORDER BY CollectDT ASC")) {

            // 设置参数
            pStatement.setString(1, dt + " 00:00:00");
            pStatement.setString(2, dt + " 23:59:59");

            List<String> timeList = new ArrayList<>();
            List<Double> fstList = new ArrayList<>();
            List<Double> fbtList = new ArrayList<>();
            List<Double> sstList = new ArrayList<>();
            List<Double> sbtList = new ArrayList<>();

            JSONObject innerJson = new JSONObject();

            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    timeList.add(rSet.getString("dt"));
                    fstList.add(rSet.getDouble("F_S_T1"));
                    fbtList.add(rSet.getDouble("F_B_T1"));
                    sstList.add(rSet.getDouble("S_S_T1"));
                    sbtList.add(rSet.getDouble("S_B_T1"));
                }
            }

            innerJson.put("time", timeList);
            innerJson.put("F_S_T1", fstList);
            innerJson.put("F_B_T1", fbtList);
            innerJson.put("S_S_T1", sstList);
            innerJson.put("S_B_T1", sbtList);

            return JsonResult.success(innerJson);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 换热站运行模式算法
     *
     * @return
     */
    @Override
    public JsonResult setHesRunModeAlg(String hescode, String type, Integer setval) {
        try (Connection connection = dataSoure.getConnection()) {
            String sql;
            try (PreparedStatement pStatement = connection.prepareStatement(type.equals("calcmode")
                    ? "UPDATE t_hes SET calc_mode = ? WHERE hescode = ?"
                    : "UPDATE t_hes SET runMode = ? WHERE hescode = ?")) {

                pStatement.setInt(1, setval);
                pStatement.setString(2, hescode);

                int ret = pStatement.executeUpdate();
                if (ret > 0) {
                    return JsonResult.success();
                } else {
                    return JsonResult.error("更新失败");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }


    /**
     * 换热站运行模式算法
     *
     * @return
     */
    @Override
    public JsonResult getHesRunModeAlg(String hescode) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement("SELECT calc_mode, runMode FROM t_hes WHERE hescode = ?")) {

            // 设置参数
            pStatement.setString(1, hescode);

            LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.next()) {
                    resultMap.put("calcMode", rSet.getString("calc_mode"));
                    resultMap.put("runMode", rSet.getString("runMode"));
                }
            }

            return JsonResult.success(resultMap);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }
    /**
     * 换热站控制字段
     *
     * @return
     */
    @Override
    public JsonResult hesControlFields(String hescode) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement("SELECT MPFieldDefine, MPDesc FROM T_HesMonitorPointDefine WHERE IsControl = 1 AND HesCode = ?")) {

            // 设置参数
            pStatement.setString(1, hescode);

            ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("MPDesc", rSet.getString("MPDesc"));
                    resultMap.put("MPFieldDefine", rSet.getString("MPFieldDefine"));
                    lst.add(resultMap);
                }
            }

            return JsonResult.success(lst);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 换热站指定字段值
     *
     * @return
     */
    public JsonResult hesFieldData(String hescode, String field) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT " + field + " FROM t_hes_data_" + hescode + " ORDER BY id DESC LIMIT 1")) {
            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("value", rSet.getDouble(field));
                    return JsonResult.success(resultMap);
                } else {
                    return JsonResult.error("没有找到数据");
                }
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }

    /**
     * 换热站配置字段
     *
     * @return
     */
    @Override
    public JsonResult hesConfigFields(String hescode) {
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement("SELECT MPFieldDefine, MPDesc FROM T_HesMonitorPointDefine WHERE IsUsed=1 AND HesCode = ?")) {

            // 设置参数
            pStatement.setString(1, hescode);

            ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("MPDesc", rSet.getString("MPDesc"));
                    resultMap.put("MPFieldDefine", rSet.getString("MPFieldDefine"));
                    lst.add(resultMap);
                }
            }

            return JsonResult.success(lst);
        } catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("数据库操作失败: " + ex.getMessage());
        }
    }
    /**
     * md5加密
     *
     * @return
     */
    public static String getMd5(String pwd) {
        try {
            // 创建一个MD5消息摘要实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算MD5哈希值
            byte[] messageDigest = md.digest(pwd.getBytes());

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            // 转换为小写
            String tmp = hexString.toString().toLowerCase();

            // 获取从第9个字符开始的16个字符
            return tmp.substring(8, 24);

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

}
