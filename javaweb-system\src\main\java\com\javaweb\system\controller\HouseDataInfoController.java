package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HouseDataInfo;
import com.javaweb.system.query.HouseDataInfoQuery;
import com.javaweb.system.service.IHouseDataInfoService;
import com.javaweb.system.service.IHouseDataInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  室内数据
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/indoordata")
public class HouseDataInfoController extends BaseController {

    @Autowired
    private IHouseDataInfoService houseDataInfoService;

    /**
     * 获取查询列表
     * @param houseDataInfoQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HouseDataInfoQuery houseDataInfoQuery) {
        return houseDataInfoService.getList(houseDataInfoQuery);
    }


    /**
     * 获取当天数据列表
     * @param useheatunitId romno
     * @return
     */
    @GetMapping("/getIndoorTDayData/{useheatunitId}/{heatno}")
    public JsonResult getIndoorTDayData(@PathVariable("useheatunitId") Integer useheatunitId,@PathVariable("heatno") String heatno) {
        return houseDataInfoService.getIndoorTDayData(useheatunitId,heatno);
    }

}

