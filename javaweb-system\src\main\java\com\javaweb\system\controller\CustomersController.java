
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Customers;
import com.javaweb.system.query.CustomersQuery;
import com.javaweb.system.service.ICustomersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@RestController
@RequestMapping("/customers")
public class CustomersController extends BaseController {

    @Autowired
    private ICustomersService customersService;

    /**
     * 获取列表
     *
     * @param customersQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(CustomersQuery customersQuery) {
        return customersService.getList(customersQuery);
    }

    /**
     * 添加
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "用户投诉", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Customers entity) {
        return customersService.edit(entity);
    }

    /**
     * 编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "用户投诉", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Customers entity) {
        return customersService.edit(entity);
    }

    /**
     * 删除
     *
     * @param customersIds
     * @return
     */
    @Log(title = "用户投诉", logType = LogType.DELETE)
    @DeleteMapping("/delete/{customersIds}")
    public JsonResult delete(@PathVariable("customersIds") Integer[] customersIds) {
        return customersService.deleteByIds(customersIds);
    }



}
