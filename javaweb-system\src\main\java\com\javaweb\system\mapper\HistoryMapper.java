package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.HistoryData;

import java.util.List;


/**
 * <p>
 * 历史数据 Mapper 接口
 * </p>
 *
 *
 * @since 2020-12-27
 */
public interface HistoryMapper extends BaseMapper<HistoryData> {

    /**
     * 根据表名获取历史数据
     *
     * @param  tablename,dt
     * @return
     */
    List<HistoryData> getHistoryStation(String tablename);


    /**
     * 根据站编号获取配置测点数据
     *
     * @param  hescode
     * @return
     */
    List<HistoryData> getHesCedian(Integer hescode);
}
