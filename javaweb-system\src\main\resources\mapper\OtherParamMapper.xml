<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.OtherParamMapper">
   <update id="updateAlarmDate">
       update tb_other_param set last_alarm_date=CURRENT_TIMESTAMP where id=1;
   </update>
    <select id="getOtherParamInfo" resultType="com.javaweb.system.entity.OtherParam">
        select * from  tb_other_param where id=1;
    </select>

</mapper>
