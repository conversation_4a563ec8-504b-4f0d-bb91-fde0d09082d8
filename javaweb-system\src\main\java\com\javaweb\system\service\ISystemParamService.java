package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.SystemParam;
import com.javaweb.system.query.SystemParamQuery;

public interface ISystemParamService extends IService<SystemParam> {


    /**
     * 获取所有监测站实时数据
     *
     * @return
     */
    JsonResult getCurrentDataAll(SystemParamQuery currentdataQuery);



    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(SystemParam entity);


    /**
     * 获取系统参数信息
     *
     * @return
     */
    JsonResult getSystemParamInfo();




}
