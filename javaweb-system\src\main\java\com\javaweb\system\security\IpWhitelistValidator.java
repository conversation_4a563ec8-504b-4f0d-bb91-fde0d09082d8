package com.javaweb.system.security;

import com.javaweb.common.utils.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * IP白名单验证器
 * 用于验证客户端IP地址是否在允许的白名单范围内
 */
@Component
public class IpWhitelistValidator {

    private static final Logger log = LoggerFactory.getLogger(IpWhitelistValidator.class);

    /**
     * 登录接口IP白名单，从配置文件读取
     */
    @Value("${security.login.ip-whitelist:127.0.0.1,::1,localhost}")
    private String loginIpWhitelist;

    /**
     * 是否启用IP白名单验证
     */
    @Value("${security.login.ip-whitelist-enabled:true}")
    private boolean ipWhitelistEnabled;

    /**
     * IP地址正则表达式
     */
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );

    /**
     * IPv6地址正则表达式
     */
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$"
    );

    /**
     * 验证IP地址是否在白名单中
     *
     * @param request HTTP请求对象
     * @return true-在白名单中，false-不在白名单中
     */
    public boolean isIpAllowed(HttpServletRequest request) {
        if (!ipWhitelistEnabled) {
            log.debug("IP白名单验证已禁用");
            return true;
        }

        String clientIp = IpUtils.getIpAddr(request);
        if (clientIp == null || clientIp.isEmpty()) {
            log.warn("无法获取客户端IP地址");
            return false;
        }

        // 标准化IP地址
        clientIp = normalizeIp(clientIp);
        
        List<String> allowedIps = getWhitelistIps();
        
        for (String allowedIp : allowedIps) {
            if (isIpMatch(clientIp, allowedIp.trim())) {
                log.debug("IP地址 {} 在白名单中", clientIp);
                return true;
            }
        }

        log.warn("IP地址 {} 不在白名单中，拒绝访问", clientIp);
        return false;
    }

    /**
     * 获取白名单IP列表
     *
     * @return IP白名单列表
     */
    private List<String> getWhitelistIps() {
        return Arrays.asList(loginIpWhitelist.split(","));
    }

    /**
     * 标准化IP地址
     *
     * @param ip 原始IP地址
     * @return 标准化后的IP地址
     */
    private String normalizeIp(String ip) {
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "127.0.0.1";
        }
        if ("localhost".equalsIgnoreCase(ip)) {
            return "127.0.0.1";
        }
        return ip;
    }

    /**
     * 检查IP地址是否匹配
     *
     * @param clientIp  客户端IP
     * @param allowedIp 允许的IP（支持CIDR格式）
     * @return true-匹配，false-不匹配
     */
    private boolean isIpMatch(String clientIp, String allowedIp) {
        // 精确匹配
        if (clientIp.equals(allowedIp)) {
            return true;
        }

        // localhost匹配
        if ("localhost".equalsIgnoreCase(allowedIp) && "127.0.0.1".equals(clientIp)) {
            return true;
        }

        // CIDR格式匹配
        if (allowedIp.contains("/")) {
            return isIpInCidr(clientIp, allowedIp);
        }

        // 通配符匹配
        if (allowedIp.contains("*")) {
            return isIpMatchWildcard(clientIp, allowedIp);
        }

        return false;
    }

    /**
     * 检查IP是否在CIDR范围内
     *
     * @param ip   IP地址
     * @param cidr CIDR格式的网络地址
     * @return true-在范围内，false-不在范围内
     */
    private boolean isIpInCidr(String ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);

            if (!isValidIp(ip) || !isValidIp(networkIp)) {
                return false;
            }

            long ipLong = ipToLong(ip);
            long networkLong = ipToLong(networkIp);
            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;

            return (ipLong & mask) == (networkLong & mask);
        } catch (Exception e) {
            log.error("CIDR匹配失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通配符匹配
     *
     * @param ip      IP地址
     * @param pattern 包含通配符的模式
     * @return true-匹配，false-不匹配
     */
    private boolean isIpMatchWildcard(String ip, String pattern) {
        String regex = pattern.replace(".", "\\.")
                             .replace("*", "\\d+");
        return ip.matches(regex);
    }

    /**
     * 验证IP地址格式是否正确
     *
     * @param ip IP地址
     * @return true-格式正确，false-格式错误
     */
    private boolean isValidIp(String ip) {
        return IP_PATTERN.matcher(ip).matches() || IPV6_PATTERN.matcher(ip).matches();
    }

    /**
     * 将IP地址转换为长整型
     *
     * @param ip IP地址
     * @return 长整型表示的IP
     */
    private long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = result * 256 + Integer.parseInt(parts[i]);
        }
        return result;
    }

    /**
     * 记录IP访问日志
     *
     * @param request HTTP请求
     * @param allowed 是否允许访问
     */
    public void logIpAccess(HttpServletRequest request, boolean allowed) {
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String uri = request.getRequestURI();
        
        if (allowed) {
            log.info("IP访问允许 - IP: {}, URI: {}, UserAgent: {}", clientIp, uri, userAgent);
        } else {
            log.warn("IP访问拒绝 - IP: {}, URI: {}, UserAgent: {}", clientIp, uri, userAgent);
        }
    }
}
