package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *小区信息
 * </p>
 *
 * @Date: 2022/12/12 14:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_useheatunit")
public class HeatUnit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 小区名称
     */
    private String name;

    /**
     * 小区类型
     */
    private String unittype;

    /**
     * 小区编号
     */
    private String useheatno;

    /**
     * 地址
     */
    private String addr;

    /**
     * 建筑面积
     */
    private Double builtarea;

    /**
     * 建筑年份
     */
    private Integer builtyear;

    /**
     *楼层总计
     */
    private Integer floortotal;

    /**
     * 家庭总计
     */
    private Integer housetotal;

    /**
     * 使用热量
     */
    private String useheatpic;

    /**
     *单元管理
     */
    private String unitmanage;

    /**
     *单元管理联系人
     */
    private String unitmanagecontact;

    /**
     * 联系电话
     */
    private String unitmanagetel;

    /**
     * 热量总计表
     */
    private Integer heatmetertotal;

    /**
     * 已用热量表总计
     */
    private Integer usedheatmetertotal;

    /**
     *备注
     */
    private String mm;

}

