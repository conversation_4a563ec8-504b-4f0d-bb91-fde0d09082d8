<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.THesYearHAreaMapper">

    <!-- 获取换热站面积 -->
    <select id="getHareaByDate" resultType="string">
        select  heara from T_Hes_Year_HArea where hescode =#{hescode} and hyear=#{hyear}   and hmonth &lt;=#{hmonth} order by id desc limit 0,1 ;
    </select>


    <select id="getHareaByYear" resultType="string">
        select  heara from T_Hes_Year_HArea where hescode =#{hescode} and hyear=#{hyear}  order by id desc limit 0,1 ;
    </select>


    <!--    <insert id="insertone" parameterType="com.javaweb.system.entity.HesYearHArea" >-->
<!--        insert into t_hes_year_harea-->
<!--            value-->
<!--            (0,#{id},#{hesname},#{hescode},#{hyear},#{heara},#{freearea})-->




<!--    </insert>-->

<!--    <select id="selectList  " resultType="com.javaweb.system.entity.HesYearHArea">-->
<!--        select-->
<!--            hesname-->
<!--        from-->
<!--            t_hes_year_harea-->



<!--    </select>-->

<!--    <update id="update" parameterType="com.javaweb.system.entity.HesYearHArea">-->
<!--        update-->
<!--            t_hes_year_harea-->
<!--        set-->
<!--            (#{hesname},#{hescode},#{hyear},#{heara},#{freearea})-->

<!--    </update>-->



<!--    <delete id="deleteid" parameterType="com.javaweb.system.entity.HesYearHArea">-->

<!--        delete-->
<!--        from t_hes_year_harea-->
<!--        where (#{id})-->



<!--    </delete>-->


</mapper>