<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HesRuleMapper">
        <!-- 获取运行配置 -->
        <select id="getHesRuleByHescode" resultType="com.javaweb.system.entity.HesRule">
                select run_h,offset_t from t_hes_rule where hescode=#{hescode} order by run_h asc
        </select>

</mapper>
