
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Dict;
import com.javaweb.system.query.DictQuery;
import com.javaweb.system.service.IDictService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 字典类型表 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-01
 */
@RestController
@RequestMapping("/dict")
public class DictController extends BaseController {

    @Autowired
    private IDictService dictService;

    /**
     * 获取字典列表
     *
     * @param dictQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(DictQuery dictQuery) {
        return dictService.getList(dictQuery);
    }

    /**
     * 添加字典
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "字典分组", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Dict entity) {
        return dictService.edit(entity);
    }

    /**
     * 编辑字典
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "字典分组", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Dict entity) {
        return dictService.edit(entity);
    }

    /**
     * 删除字典
     *
     * @param dicTypeId 字典ID
     * @return
     */
    @Log(title = "字典分组", logType = LogType.DELETE)
    @DeleteMapping("/delete/{dicTypeId}")
    public JsonResult delete(@PathVariable("dicTypeId") Integer dicTypeId) {
        return dictService.deleteById(dicTypeId);
    }

}
