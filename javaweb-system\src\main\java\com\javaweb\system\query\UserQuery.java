
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;

/**
 * 用户查询条件
 */
@Data
public class UserQuery extends BaseQuery {

    /**
     * 用户账号
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 性别：1男 2女 3保密
     */
    private Integer gender;

}
