package com.javaweb.api.service.impl;

import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.HashMap;
import java.util.Map;

/*
* 数学解析表达式
* */
@Service
public class MathExpressionEvaluator {

    private ScriptEngine engine;
    private Map<String, Object> variables;

    public MathExpressionEvaluator() {
        engine = new ScriptEngineManager().getEngineByName("JavaScript");
        variables = new HashMap<>();
    }

    public void setVariable(String name, Object value) {
        variables.put(name, value);
    }

    public Object evaluate(String expression) throws ScriptException {
        // 替换表达式中的变量
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            expression = expression.replace(entry.getKey(), entry.getValue().toString());
        }
        // 执行表达式
        return engine.eval(expression);
    }

}
