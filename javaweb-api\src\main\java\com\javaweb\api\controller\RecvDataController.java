package com.javaweb.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.javaweb.api.service.IRecvDataService;
import com.javaweb.common.utils.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/recv")
public class RecvDataController {

    @Autowired
    private IRecvDataService recvdataService;

    /**
     * 获取验证码
     *
     * @param request 网络请求
     * @return
     */
    @PostMapping(value="/recvdata",produces = "application/json;charset=UTF-8")
    public JsonResult userLogin(@RequestBody JSONObject data, HttpServletRequest request)
    {
        return recvdataService.getRecvdata(data, request);
    }


    /**
     * 接收换热站数据
     *
     * @param request 网络请求
     * @return
     */
    @PostMapping("/hesdata")
    public JsonResult getRecvHesdata(@RequestBody String data, HttpServletRequest request)
    {
        //System.out.println("======接收到换热站数据======");
        recvdataService.getRecvHesdata(data, request);
        return JsonResult.success("OK");
    }


    /**
     * 接收气象站数据
     *
     * @param request 网络请求
     * @return
     */
    @PostMapping("/wsdata")
    public JsonResult getRecvWsdata(@RequestBody String data, HttpServletRequest request)
    {

        return recvdataService.getRecvWsdata(data, request);
    }

    /**
     * 接收室内采集数据
     *
     * @param request 网络请求
     * @return
     */
    @GetMapping("/indoortdata")
    public JsonResult getRecvIndoorTdata(@RequestParam String data, HttpServletRequest request)
    {
        return recvdataService.getRecvIndoorTdata(data, request);
    }

    @GetMapping("/getHesParam/{hescode}")
    public String getHesParam(@PathVariable("hescode") int hescode)
    {
        return recvdataService.getHesParam(hescode);
    }


    @GetMapping("/getHesVersion")
    public String  getHesVersion(){
        return recvdataService.getHesVersion();
    }

    @GetMapping("/getHesState/{hescode}")
    public String getHesState(@PathVariable("hescode") int hescode)
    {
        return recvdataService.getHesState(hescode);
    }

}
