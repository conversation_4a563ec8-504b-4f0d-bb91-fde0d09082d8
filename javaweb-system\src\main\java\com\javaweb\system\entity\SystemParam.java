package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_system_param")
public class SystemParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统logo
     */
    private String systemLogo;

    /**
     * 系统版本
     */
    private String systemVersions;


    /**
     * 前端一体机程序版本
     */
    private String stationVersions;

    /**
     *版权信息
     */
    private String copyright;

    /**
     *所属公司
     */
    private String company;

    /**
     *负责人
     */
    private String linkman;

    /**
     *联系电话
     */
    private String mobile;


    /**
     *公司网站
     */
    private String internetAddr;

    /**
     *公司地址
     */
    private String companyAddr;


    /**
     *数据更新频率
     */
    private Integer updateFre;



    /**
     * 系统介绍
     */
    private String intro;

}



