package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HesMpDictionary;
import com.javaweb.system.mapper.HesMpDictionaryMapper;
import com.javaweb.system.query.HesMpDictionaryQuery;
import com.javaweb.system.service.THesMpDictionaryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  测点信息 前端控制器
 * </p>
 *
 * @Date: 2022/12/16 17:31
 */
@RestController
@RequestMapping("dictionary")
public class HesMpDictionaryController extends BaseController {

    @Autowired
    private THesMpDictionaryService tHesMpDictionaryService;

    @Autowired
    private HesMpDictionaryMapper hesMpDictionaryMapper;
    /**
     * 获取数据
     * @param hesMpDictionaryQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HesMpDictionaryQuery hesMpDictionaryQuery)
    {
       return    tHesMpDictionaryService.getList(hesMpDictionaryQuery);
    }

    @Log(title = "测点信息",logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HesMpDictionary entity){

        return  tHesMpDictionaryService.edit(entity);
    }

    @Log(title = "测点信息",logType = LogType.UPDATE)
    @PutMapping ("/edit")
    public JsonResult edit(@RequestBody HesMpDictionary entity){
        return tHesMpDictionaryService.edit(entity);
    }


    @Log(title = "测点信息",logType = LogType.DELETE)
    @DeleteMapping("/delete/{hesMpDictionaryIds}")
    public JsonResult delete(@PathVariable("hesMpDictionaryIds") Integer[] hesMpDictionaryIds){
        return tHesMpDictionaryService.deleteByIds(hesMpDictionaryIds);
    }


    @Log(title = "测点信息", logType = LogType.STATUS)
    @PutMapping("/controlstatus")
    public JsonResult controlstatus(@RequestBody HesMpDictionary entity){
        return tHesMpDictionaryService.setStatus(entity);

    }

    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "测点信息", logType = LogType.STATUS)
    @PutMapping("/status")
    public JsonResult state(@RequestBody HesMpDictionary entity) {
        return tHesMpDictionaryService.setStatus(entity);
    }


    /**
     * 根据测点表的显示状态更新配置表的显示状态
     *
     */
    @GetMapping("/updateMonitorStatius")
    public JsonResult updateMonitorStatius()
    {
        hesMpDictionaryMapper.updateMonitorStatius();
        return JsonResult.success();
    }

    @GetMapping("/getDictionaryList")
    public JsonResult getDictionaryList(){
        return tHesMpDictionaryService.getHesMpDictionaryList();
    }
}

