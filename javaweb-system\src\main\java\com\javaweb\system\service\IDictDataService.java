
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.DictData;

/**
 * <p>
 * 字典项管理表 服务类
 * </p>
 *
 * 
 * @since 2020-11-01
 */
public interface IDictDataService extends IBaseService<DictData> {



    /**
     * 分組获取字典项列表
     *
     * @return
     */

    JsonResult getDictdataInfoList();

}
