package com.javaweb.system.utils;

import com.javaweb.common.utils.StringUtils;

import java.util.Calendar;
import java.util.List;

public class DataBaseOptUtils {

    /*
     * 创建采集数据表以及分区
     * 参数：
     * list       字段
     * tablename  表名
     * 2024/9/11 xqt
     * */
    public static String createTableSQLandPartition(List<String> list, String tablename) {
        StringBuffer sb = new StringBuffer();
        sb.append("CREATE TABLE `" + tablename + "` (\n");

        for (int i = 0; i < list.size(); i++) {
            String columnName = list.get(i);
            // 当前条数据
            if (columnName.equals("id")) {
                sb.append("id INT UNSIGNED AUTO_INCREMENT").append(",");
            } else if (columnName.equals("collectDt")) {
                sb.append("collectDt datetime ").append(",");
            }else {
                // 判断数据类型
                String fieldType = "varchar";
                sb.append("`" + list.get(i) + "`").append(" ");
                // 追加列
                sb.append("varchar(50)").append(",");
            }
            if (list.size()-1==i) {
                sb.append("PRIMARY KEY (`id`,`collectDt`)");
                sb.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8");
            }
        }
        sb.append(" PARTITION BY RANGE(YEAR(collectDt)) (");
        Calendar calendar = Calendar.getInstance();

        for (int i = 0; i < 11; i++)
        {
            int year = calendar.get(Calendar.YEAR);
            year=year+i;
            String name=tablename+"_"+String.valueOf(year);
            year=year+1;
            if (i==10)
            {
                sb.append(" PARTITION  "+ name+" VALUES LESS THAN ("+year+"))");
            }else
            {
                sb.append(" PARTITION  "+ name+" VALUES LESS THAN ("+year+"),");
            }
        }
        return sb.toString();
    }

    /*
     * 创建数据表sql语句
     * 参数：
     * list       字段
     * tablename  表名
     * 2024/9/18 xqt
     * */
    public static String createTableSQL(List<String> list,String tablename) {
        StringBuffer sb = new StringBuffer();
        sb.append("CREATE TABLE `" + tablename + "` (\n");

        for (int i = 0; i < list.size(); i++) {
            // 当前条数据
            String field = list.get(i);
            if (StringUtils.equals(field, "id")) {
                sb.append("id INT UNSIGNED AUTO_INCREMENT").append(",");
            }else if (isDatetimeField(field)) {
                sb.append(field).append(" datetime").append(",");
            }else {
                // 判断数据类型
                String fieldType = "varchar";
                sb.append("`" + list.get(i) + "`").append(" ");
                // 追加列
                sb.append("varchar(50)").append(",");
            }
            if (i==list.size()-1)
            {
                sb.append("PRIMARY KEY (`id`)");
                sb.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
            }
        }
        //System.out.println(sb.toString());
        return sb.toString();
    }
    //验证是否包含有dt
    private static boolean isDatetimeField(String field) {
        return StringUtils.containsIgnoreCase(field, "dt");
    }
}
