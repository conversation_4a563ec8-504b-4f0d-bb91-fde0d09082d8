
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Flaws;
import com.javaweb.system.query.FlawsQuery;

/**
 * <p>
 *  服务类
 * </p>
 *
 * 
 * @since 2020-11-02
 */
public interface IFlawsService extends IBaseService<Flaws> {

    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(FlawsQuery flawsQuery);


}
