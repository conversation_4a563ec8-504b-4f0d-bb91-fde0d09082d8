package com.javaweb.system.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class HesEnergyDto {

    /**
     * 字段id
     */
    private Integer id;

    /**
     *
     */
    private String  fSCH;

    /**
     *
     */
    private String  fSCF;

    /**
     * 环境温度
     */
    private Integer avgt;


    /**
     * 能耗
     */
    private Double energy;


    /**
     * 能耗
     */
    private Double theoryEnergy;

    /**
     * 换热站编号
     */
    private Double area;

    /**
     * 时间dt
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dt;

}
