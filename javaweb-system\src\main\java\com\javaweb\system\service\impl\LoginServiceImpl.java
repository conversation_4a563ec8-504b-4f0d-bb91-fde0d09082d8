
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.exception.user.UserNotExistsException;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.MessageUtils;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.VerifyUtil;
import com.javaweb.system.constant.Constants;
import com.javaweb.system.dto.LoginDto;
import com.javaweb.system.entity.User;
import com.javaweb.system.manager.AsyncFactory;
import com.javaweb.system.manager.AsyncManager;
import com.javaweb.system.mapper.UserMapper;
import com.javaweb.system.security.IpWhitelistValidator;
import com.javaweb.system.security.InputSecurityValidator;
import com.javaweb.system.security.SecurityAuditLogger;
import com.javaweb.system.service.ILoginService;
import com.javaweb.system.utils.ShiroUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.PBEKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.Key;
import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <p>
 * 后台用户管理表 服务实现类
 * </p>
 *
 * 
 * @since 2020-02-26
 */
@Service
public class LoginServiceImpl extends ServiceImpl<UserMapper, User> implements ILoginService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IpWhitelistValidator ipWhitelistValidator;

    @Autowired
    private InputSecurityValidator inputSecurityValidator;

    @Autowired
    private SecurityAuditLogger securityAuditLogger;

//    @Autowired
//    private UserRoleMapper userRoleMapper;

    /**
     * 获取验证码
     *
     * @param response 请求响应
     * @return
     */
    @Override
    public JsonResult captcha(HttpServletResponse response) {
        VerifyUtil verifyUtil = new VerifyUtil();
        Map<String, String> result = new HashMap();
        try {
            String key = UUID.randomUUID().toString();
            response.setContentType("image/png");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Expire", "0");
            response.setHeader("Pragma", "no-cache");
            // 返回base64
            //写入redis缓存
            Map<String, String> mapInfo = verifyUtil.getRandomCodeBase64();
            String randomStr = mapInfo.get("randomStr");
            redisUtils.set(key, randomStr, 60 * 5);

            result.put("captcha", "data:image/png;base64," + mapInfo.get("img"));
            result.put("key", key);
        } catch (Exception e) {
            log.error(e.getMessage());
            return JsonResult.error(e.getMessage());
        }
        return JsonResult.success(result);
    }

    /**
     * 用户登录
     *
     * @param loginDto 登录Dto
     * @return
     */
    @Override
    public JsonResult login(LoginDto loginDto, HttpServletRequest request) {
        // 1. IP白名单验证
        if (!ipWhitelistValidator.isIpAllowed(request)) {
            securityAuditLogger.logIpWhitelistViolation(request);
            return JsonResult.error("访问被拒绝");
        }

        // 2. 输入安全验证
        // 验证用户名
        InputSecurityValidator.ValidationResult usernameValidation =
            inputSecurityValidator.validateUsername(loginDto.getUsername());
        if (!usernameValidation.isValid()) {
            securityAuditLogger.logInputValidationFailure(request, "username",
                loginDto.getUsername(), usernameValidation.getMessage());
            return JsonResult.error(usernameValidation.getMessage());
        }

        // 验证验证码Key
        InputSecurityValidator.ValidationResult keyValidation =
            inputSecurityValidator.validateCaptchaKey(loginDto.getKey());
        if (!keyValidation.isValid()) {
            securityAuditLogger.logInputValidationFailure(request, "captchaKey",
                loginDto.getKey(), keyValidation.getMessage());
            return JsonResult.error(keyValidation.getMessage());
        }

        // 验证验证码
        InputSecurityValidator.ValidationResult captchaValidation =
            inputSecurityValidator.validateCaptcha(loginDto.getCaptcha());
        if (!captchaValidation.isValid()) {
            securityAuditLogger.logInputValidationFailure(request, "captcha",
                loginDto.getCaptcha(), captchaValidation.getMessage());
            return JsonResult.error(captchaValidation.getMessage());
        }

        // 3. 验证码校验
        if (!loginDto.getCaptcha().equals("520")) {
            if (StringUtils.isEmpty(loginDto.getCaptcha())) {
                securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "验证码为空");
                return JsonResult.error("验证码不能为空");
            }
            // 验证码校验
            Object cachedCaptcha = redisUtils.get(loginDto.getKey());
            if (cachedCaptcha == null) {
                securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "验证码已过期");
                return JsonResult.error("验证码已过期，请重新获取");
            }
            if (!loginDto.getCaptcha().toLowerCase().equals(cachedCaptcha.toString().toLowerCase())) {
                securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "验证码不正确");
                return JsonResult.error("验证码不正确");
            }
        }
        // 4. 密码解密和验证
        String key = "randomsalttbkj20151022";
        String decryptedPassword;
        try {
            // 验证加密密码的安全性
            InputSecurityValidator.ValidationResult passwordValidation =
                inputSecurityValidator.validateEncryptedPassword(loginDto.getPassword());
            if (!passwordValidation.isValid()) {
                securityAuditLogger.logInputValidationFailure(request, "password",
                    "***", passwordValidation.getMessage());
                return JsonResult.error("密码格式不正确");
            }

            decryptedPassword = decrypt(loginDto.getPassword(), key);
            decryptedPassword = decrypt(decryptedPassword, key);
        } catch (Exception e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "密码解密失败");
            return JsonResult.error("密码格式不正确");
        }

        try {
            // 5. 用户状态检查
            User user = userMapper.getUserInfo(loginDto.getUsername());
            if (user != null) {
                // 限制的分钟
                int limitMinute = 5;
                // 若为锁定状态，则进行时间段的比较
                if (user.getStatus() == 2) {
                    int minutesDiff = com.javaweb.common.utils.StringUtils.getMinuteDiff(user.getLoginTime());
                    if (minutesDiff > limitMinute) {
                        userMapper.updateUserState(loginDto.getUsername(), 1, 0);
                    } else {
                        int time = limitMinute - minutesDiff;
                        if (time == 0) {
                            time = 1;
                        }
                        securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false,
                            "账户已锁定，剩余" + time + "分钟");
                        return JsonResult.error("账户已锁定，请" + time + "分钟之后再登录");
                    }
                }
            }

            // 6. 执行登录验证
            Subject subject = SecurityUtils.getSubject();
            UsernamePasswordToken token = new UsernamePasswordToken(loginDto.getUsername(), decryptedPassword);

            // 进行登录操作
            subject.login(token);

            // 7. 登录成功处理
            Map<String, String> result = new HashMap<>();
            result.put("access_token", SecurityUtils.getSubject().getSession().getId().toString());
            result.put("token_type", "Bearer");

            // 登录成功，更改用户登陆次数。设置为0
            userMapper.updateLoginErrNum(loginDto.getUsername());

            // 记录成功日志
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), true, null);
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginDto.getUsername(), Constants.LOGIN_SUCCESS, "登录成功"));

            return JsonResult.success(result);

        } catch (UnknownAccountException e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "用户不存在");
            return JsonResult.error("用户名或密码不正确");
        } catch (IncorrectCredentialsException e) {
            // 插入到登录次数中。锁定时长，错误次数
            // 得到用户信息
            User user = userMapper.getUserInfo(loginDto.getUsername());
            if (user != null) {
                // 若登录次数小于配置次数，更新错误登录次数
                if (user.getLoginNum() <= 5) {
                    userMapper.updateLoginCount(loginDto.getUsername());
                    securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false,
                        "密码错误，失败次数: " + (user.getLoginNum() + 1));
                } else {
                    // 锁定用户 更改状态为2
                    userMapper.updateUserState(loginDto.getUsername(), 2, 5);
                    securityAuditLogger.logAccountLockout(loginDto.getUsername(), "密码错误次数过多", 5);
                }
            }
            return JsonResult.error("用户名或密码不正确");
        } catch (LockedAccountException e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "账号已锁定");
            return JsonResult.error("账号已锁定");
        } catch (ExcessiveAttemptsException e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "尝试次数过多");
            return JsonResult.error("用户名或密码错误次数过多");
        } catch (AuthenticationException e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "认证失败");
            return JsonResult.error("用户名或密码不正确");
        } catch (Exception e) {
            securityAuditLogger.logLoginAttempt(request, loginDto.getUsername(), false, "系统异常: " + e.getMessage());
            log.error("登录异常", e);
            return JsonResult.error("系统异常，请稍后重试");
        }
    }

    //验证密码强度
    public boolean isPasswordStrong(String password) {
        if (password == null || password.isEmpty()) {
            return false;
        }

        // 密码最小长度
        if (password.length() < 8) {
            return false;
        }

        // 白名单验证：只能包含字母、数字、部分符号（可扩展）
        if (!password.matches("^[A-Za-z0-9!@#$%^&*()_+\\-=\\[\\]{}~`.?,:/]*$")) {
            return false;
        }

        // 必须包含大小写字母和数字
        if (!password.matches(".*[A-Z].*") ||
                !password.matches(".*[a-z].*") ||
                !password.matches(".*\\d.*")) {
            return false;
        }

        return true;
    }
    public static String decrypt(String ciphertext, String key) {

            byte[] decodedCiphertext = Base64.getDecoder().decode(ciphertext);
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < decodedCiphertext.length; i++) {
                result.append((char) (decodedCiphertext[i] ^ key.charAt(i % key.length())));
            }
            return result.toString();


    }

//    public static String decrypt(String ciphertext, String key) {
//        StringBuilder result = new StringBuilder();
//        for (int i = 0; i < ciphertext.length(); i++) {
//            result.append((char) (ciphertext.charAt(i) ^ key.charAt(i % key.length())));
//        }
//        return result.toString();
//    }



    /**
     * 退出登录
     *
     * @return
     */
    @Override
    public JsonResult logout() {
        // 获取当前登录人信息
        User user = ShiroUtils.getUserInfo();
        // 记录用户退出日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUsername(), Constants.LOGOUT, "注销成功"));
        // 退出登录
        ShiroUtils.logout();
        return JsonResult.success("注销成功");
    }

    /**
     * 根据用户名获取用户对象
     *
     * @param username 名称
     * @return
     */
    @Override
    public User getUserByName(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        queryWrapper.eq("mark", 1);
        User user = userMapper.selectOne(queryWrapper);
        return user;
    }

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return
     */
    @Override
    public User login(String username, String password) {
        // 用户名和验证码校验
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
//        // 验证码为空校验
//        String captcha = ServletUtils.getRequest().getSession().getAttribute("captcha").toString();
//        if (StringUtils.isEmpty(captcha)) {
//            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
//            throw new CaptchaException();
//        }
//        // 验证码校验
//        if (!captcha.equals("520")) {
//            if (!captcha.toLowerCase().equals(redisUtils.get("key").toString().toLowerCase())) {
//                AsyncManager.me().execute(AsyncFactory.recordLogininfor(captcha, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
//                throw new CaptchaException();
//            }
//        }

        // 查询用户信息
        User user = getUserByName(username);
        if (user == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new UserNotExistsException();
        }
        // 判断用户状态
        if (user.getStatus() != 1) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.blocked")));
            throw new LockedAccountException();
        }

        // 创建登录日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor("admin", Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        return user;
    }

}
