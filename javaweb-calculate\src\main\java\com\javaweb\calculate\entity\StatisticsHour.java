package com.javaweb.calculate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

//日能耗统计表
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hes_statistics_hour")
public class StatisticsHour {
    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 监测站编号
     */
    private String hescode;


    /**
     * 时间
     */
    private String dt;

    /**
     * 是否计算
     */
    private Integer iscalc;

    /**
     * 面积
     */
    private double area;


    private String h0;

    private String h1;

    private String h2;

    private String h3;

    private String h4;

    private String h5;

    private String h6;

    private String h7;

    private String h8;

    private String h9;

    private String h10;

    private String h11;

    private String h12;

    private String h13;

    private String h14;

    private String h15;

    private String h16;

    private String h17;

    private String h18;

    private String h19;

    private String h20;

    private String h21;

    private String h22;

    private String h23;

    public void setHourlyEnergy(int hour, String value) {
        switch (hour) {
            case 0:  h0  = value; break;
            case 1:  h1  = value; break;
            case 2:  h2  = value; break;
            case 3:  h3  = value; break;
            case 4:  h4  = value; break;
            case 5:  h5  = value; break;
            case 6:  h6  = value; break;
            case 7:  h7  = value; break;
            case 8:  h8  = value; break;
            case 9:  h9  = value; break;
            case 10: h10 = value; break;
            case 11: h11 = value; break;
            case 12: h12 = value; break;
            case 13: h13 = value; break;
            case 14: h14 = value; break;
            case 15: h15 = value; break;
            case 16: h16 = value; break;
            case 17: h17 = value; break;
            case 18: h18 = value; break;
            case 19: h19 = value; break;
            case 20: h20 = value; break;
            case 21: h21 = value; break;
            case 22: h22 = value; break;
            case 23: h23 = value; break;
            default: throw new IllegalArgumentException("Invalid hour: " + hour);
        }
    }

}
