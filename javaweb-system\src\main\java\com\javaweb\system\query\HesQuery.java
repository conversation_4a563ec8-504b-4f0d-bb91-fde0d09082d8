package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;


@Data
public class HesQuery extends BaseQuery {

    /**
     * 监测站名称
     */
    private String  name;

    /**
     * 运行模式
     */
    private Integer runmode;

    /**
     *每页数
     */
    private Integer limit;


    /**
     * 页码
     */
    private Integer page;


    /**
     *排序的字段
     */
    private String orderField;


    /**
     *排序的顺序  ascend  ascend
     */
    private String order;

}
