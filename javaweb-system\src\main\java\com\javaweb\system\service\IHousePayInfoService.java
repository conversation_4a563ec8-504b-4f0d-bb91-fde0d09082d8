package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.HousePayInfo;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2024/09/09 8:40
 */
public interface IHousePayInfoService extends IService<HousePayInfo> {



    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);


    JsonResult getUserPayInfo(HouseInfo houseinfo);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(HousePayInfo entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

}

