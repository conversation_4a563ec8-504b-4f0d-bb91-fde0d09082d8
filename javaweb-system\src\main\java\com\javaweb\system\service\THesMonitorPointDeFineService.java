package com.javaweb.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.dto.MonitorDto;
import com.javaweb.system.dto.MpdictDto;
import com.javaweb.system.entity.HesMonitorPointDeFine;
import com.javaweb.system.query.HesMonitorPointDeFineQuery;
import com.javaweb.system.query.LevelQuery;
import com.javaweb.system.vo.level.LevelInfoVo;
import com.javaweb.system.vo.monitor.MonitorInfoVo;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/16 17:29
 */

public interface THesMonitorPointDeFineService extends IService<HesMonitorPointDeFine> {



    /**
     * 根据查询条件获取所有数据列表
     *
     * @param hesMonitorPointDeFineQuery 查询条件
     * @return
     */
    JsonResult getList(HesMonitorPointDeFineQuery hesMonitorPointDeFineQuery);


    JsonResult getHesMonitorPointDeFineList();

    /**
     * 编辑
     *
     * @return
     */
     JsonResult edit(HesMonitorPointDeFine entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);


    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult setStatus(HesMonitorPointDeFine entity);

    /**
     * 根据换热站的配置字段
     * @param
     * @return
     */

    JsonResult getHesFieldSetting(int hescode);

    /*JsonResult List(int hescode);*/

    /**
     * 导入Excel数据
     *
     * @param request 网络请求
     * @param name    目录名称
     * @return
     */
    JsonResult importExcel(HttpServletRequest request, String name);

     /**
     * 生成数据表
     * @param  hescode
     * @return
     */
    JsonResult creatDataBaseTable(int hescode) throws SQLException;

    /**
     * 配置换热站数据表
     * @param  Ids
     * @return
     */
    JsonResult setHesFieldSetting(Integer[] Ids,int hescode);


    /**
     * 配置换热站数据表;从配置表中
     * @param  ids
     * @return
     */
    JsonResult setHesFieldByOtherCode(Integer[] ids, int sourceHesCode, int targetHesCode);


    List<MonitorDto> getDictDataByCode(Integer hescode);


    /**
     * 导出Excel数据
     *
     * @param hescode 站编号
     * @return
     */
    List<MonitorInfoVo> exportExcel(Integer hescode);


    /**
     * 导出txt数据
     *
     * @param hescode 站编号
     * @return
     */
    JsonResult exportTxt(HttpServletResponse response,Integer hescode);

}




