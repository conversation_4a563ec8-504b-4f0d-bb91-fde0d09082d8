package com.javaweb.system.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

//数据库操作接口
public interface DatabaseOptMapper {

    //查询数据表是否存在
    @Select("SELECT EXISTS (SELECT 1 FROM information_schema.TABLES WHERE TABLE_NAME = #{tableName})")
    Boolean tableExists( @Param("tableName") String tableName);

    //创建数据表
    @Update("${sql}")
    void createTable(@Param("sql") String createTableSql);

    //添加字段
    @Update("ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType};")
    void addColumn(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("columnType") String columnType);
}
