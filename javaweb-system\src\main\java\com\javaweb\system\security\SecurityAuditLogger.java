package com.javaweb.system.security;

import com.javaweb.common.utils.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 安全审计日志记录器
 * 用于记录系统安全相关的事件和操作
 */
@Component
public class SecurityAuditLogger {

    private static final Logger securityLog = LoggerFactory.getLogger("SECURITY_AUDIT");
    private static final Logger log = LoggerFactory.getLogger(SecurityAuditLogger.class);

    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 记录登录尝试
     *
     * @param request  HTTP请求
     * @param username 用户名
     * @param success  是否成功
     * @param reason   失败原因（成功时为null）
     */
    public void logLoginAttempt(HttpServletRequest request, String username, boolean success, String reason) {
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        
        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType(success ? "LOGIN_SUCCESS" : "LOGIN_FAILURE")
            .clientIp(clientIp)
            .username(username)
            .userAgent(userAgent)
            .details(success ? "登录成功" : "登录失败: " + reason)
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录IP白名单验证失败
     *
     * @param request HTTP请求
     */
    public void logIpWhitelistViolation(HttpServletRequest request) {
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String uri = request.getRequestURI();
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);

        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType("IP_WHITELIST_VIOLATION")
            .clientIp(clientIp)
            .userAgent(userAgent)
            .details("IP地址不在白名单中，访问URI: " + uri)
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录输入验证失败
     *
     * @param request    HTTP请求
     * @param inputType  输入类型
     * @param inputValue 输入值（敏感信息会被脱敏）
     * @param reason     失败原因
     */
    public void logInputValidationFailure(HttpServletRequest request, String inputType, String inputValue, String reason) {
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);

        // 脱敏处理
        String sanitizedValue = sanitizeForLog(inputValue);

        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType("INPUT_VALIDATION_FAILURE")
            .clientIp(clientIp)
            .userAgent(userAgent)
            .details(String.format("输入验证失败 - 类型: %s, 值: %s, 原因: %s", inputType, sanitizedValue, reason))
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录可疑的安全攻击
     *
     * @param request    HTTP请求
     * @param attackType 攻击类型
     * @param details    攻击详情
     */
    public void logSecurityAttack(HttpServletRequest request, String attackType, String details) {
        String clientIp = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String uri = request.getRequestURI();
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);

        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType("SECURITY_ATTACK")
            .clientIp(clientIp)
            .userAgent(userAgent)
            .details(String.format("检测到%s攻击 - URI: %s, 详情: %s", attackType, uri, details))
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录账户锁定事件
     *
     * @param username 用户名
     * @param reason   锁定原因
     * @param duration 锁定时长（分钟）
     */
    public void logAccountLockout(String username, String reason, int duration) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);

        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType("ACCOUNT_LOCKOUT")
            .username(username)
            .details(String.format("账户被锁定 - 原因: %s, 锁定时长: %d分钟", reason, duration))
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录密码修改事件
     *
     * @param request  HTTP请求
     * @param username 用户名
     * @param success  是否成功
     */
    public void logPasswordChange(HttpServletRequest request, String username, boolean success) {
        String clientIp = IpUtils.getIpAddr(request);
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);

        SecurityEvent event = SecurityEvent.builder()
            .timestamp(timestamp)
            .eventType(success ? "PASSWORD_CHANGE_SUCCESS" : "PASSWORD_CHANGE_FAILURE")
            .clientIp(clientIp)
            .username(username)
            .details(success ? "密码修改成功" : "密码修改失败")
            .build();

        logSecurityEvent(event);
    }

    /**
     * 记录安全事件到日志
     *
     * @param event 安全事件
     */
    private void logSecurityEvent(SecurityEvent event) {
        try {
            String logMessage = formatSecurityEvent(event);
            
            // 根据事件类型选择不同的日志级别
            switch (event.getEventType()) {
                case "LOGIN_SUCCESS":
                    securityLog.info(logMessage);
                    break;
                case "LOGIN_FAILURE":
                case "INPUT_VALIDATION_FAILURE":
                    securityLog.warn(logMessage);
                    break;
                case "IP_WHITELIST_VIOLATION":
                case "SECURITY_ATTACK":
                case "ACCOUNT_LOCKOUT":
                    securityLog.error(logMessage);
                    break;
                default:
                    securityLog.info(logMessage);
            }
        } catch (Exception e) {
            log.error("记录安全事件失败", e);
        }
    }

    /**
     * 格式化安全事件为日志消息
     *
     * @param event 安全事件
     * @return 格式化后的日志消息
     */
    private String formatSecurityEvent(SecurityEvent event) {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(event.getTimestamp()).append("] ");
        sb.append("事件类型: ").append(event.getEventType()).append(" | ");
        
        if (event.getClientIp() != null) {
            sb.append("客户端IP: ").append(event.getClientIp()).append(" | ");
        }
        
        if (event.getUsername() != null) {
            sb.append("用户名: ").append(event.getUsername()).append(" | ");
        }
        
        if (event.getUserAgent() != null) {
            sb.append("用户代理: ").append(event.getUserAgent()).append(" | ");
        }
        
        sb.append("详情: ").append(event.getDetails());
        
        return sb.toString();
    }

    /**
     * 脱敏处理日志内容
     *
     * @param value 原始值
     * @return 脱敏后的值
     */
    private String sanitizeForLog(String value) {
        if (value == null || value.length() <= 6) {
            return "***";
        }
        
        // 只显示前2位和后2位，中间用*代替
        return value.substring(0, 2) + "***" + value.substring(value.length() - 2);
    }

    /**
     * 安全事件数据类
     */
    private static class SecurityEvent {
        private String timestamp;
        private String eventType;
        private String clientIp;
        private String username;
        private String userAgent;
        private String details;

        public static SecurityEventBuilder builder() {
            return new SecurityEventBuilder();
        }

        // Getters
        public String getTimestamp() { return timestamp; }
        public String getEventType() { return eventType; }
        public String getClientIp() { return clientIp; }
        public String getUsername() { return username; }
        public String getUserAgent() { return userAgent; }
        public String getDetails() { return details; }

        // Builder pattern
        public static class SecurityEventBuilder {
            private SecurityEvent event = new SecurityEvent();

            public SecurityEventBuilder timestamp(String timestamp) {
                event.timestamp = timestamp;
                return this;
            }

            public SecurityEventBuilder eventType(String eventType) {
                event.eventType = eventType;
                return this;
            }

            public SecurityEventBuilder clientIp(String clientIp) {
                event.clientIp = clientIp;
                return this;
            }

            public SecurityEventBuilder username(String username) {
                event.username = username;
                return this;
            }

            public SecurityEventBuilder userAgent(String userAgent) {
                event.userAgent = userAgent;
                return this;
            }

            public SecurityEventBuilder details(String details) {
                event.details = details;
                return this;
            }

            public SecurityEvent build() {
                return event;
            }
        }
    }
}
