package com.javaweb.system.controller;

import com.javaweb.common.common.BaseController;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.query.HouseDataInfoQuery;
import com.javaweb.system.service.IHouseDataInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  阀门数据
 * </p>
 *
 * @Date: 2024/10/8 15:40
 */
@RestController
@RequestMapping("/housevalves")
public class HouseValvesController extends BaseController {



    /**
     * 获取最新一条数据
     * @param useheatunitId romno
     * @return
     */
    @GetMapping("/getHouseValvesData/{useheatunitId}/{romno}")
    public JsonResult getHouseValvesData(@PathVariable("useheatunitId") Integer useheatunitId,@PathVariable("romno") String romno) {
        return JsonResult.success();
    }

}

