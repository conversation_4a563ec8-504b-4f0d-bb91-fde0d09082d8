package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.entity.RunRules;
import com.javaweb.system.mapper.RunRulesMapper;
import com.javaweb.system.query.RulesQuery;
import com.javaweb.system.query.RunRulesQuery;
import com.javaweb.system.service.IRulesService;
import com.javaweb.system.service.IRunRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 运行配置 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/runRules")
public class RunRulesController extends BaseController {

    @Autowired
    private IRunRulesService runService;

    @Autowired
    private RunRulesMapper runRulesMapper;
    /**
     * 获取报警配置列表
     *
     * @param rulesQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(RunRulesQuery rulesQuery) {
        return runService.getList(rulesQuery);
    }

    /**
     * 添加报警配置
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "运行配置", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody RunRules entity) {
        return runService.edit(entity);
    }


    /**
     * 编辑报警配置
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "运行配置", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody RunRules entity) {
        return runService.edit(entity);
    }


    /**
     * 删除配置
     *
     * @param runIds
     * @return
     */
    @Log(title = "运行配置", logType = LogType.DELETE)
    @DeleteMapping("/delete/{runIds}")
    public JsonResult delete(@PathVariable("runIds") Integer[] runIds) {
        return runService.deleteByIds(runIds);
    }


    @GetMapping("/getRunRulesLst")
    public JsonResult getRunRulesLst() {
        return JsonResult.success(runRulesMapper.getRunRulesLst());
    }
}