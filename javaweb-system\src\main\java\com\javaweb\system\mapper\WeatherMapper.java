package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.WeatherDto;
import com.javaweb.system.entity.Weather;

import java.util.List;

public interface WeatherMapper extends BaseMapper<Weather> {


    /**
     * 获取气象站数据最新1000 条数据
     *
     * @param
     * @return
     */
    List<Weather> getWeatherBytimes();


    /**
     * 根据日期获取气象站数据
     *
     * @param  dt
     * @return
     */
    List<Weather> getWeatherBydt(String dt);


    WeatherDto getWeatherDayMinMaxBydt(String startdt, String enddt);

}

