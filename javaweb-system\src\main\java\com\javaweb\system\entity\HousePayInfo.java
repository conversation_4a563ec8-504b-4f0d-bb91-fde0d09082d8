package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HousePayInfo {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 小区id
     */
    private Integer useheatunitId;


    /**
     * 小区
     */
    private String name;


    /**
     * 小区编号
     */
    private String useheatno;
    /**
     * 楼宇id
     */
    private String floorname;


    /**
     * 单元id
     */
    private String floorunitname;

    /**
     * 房号
     */
    private String romno;

    /**
     * 住户id
     */
    private Integer houseinfoId;

    /**
     * 楼层
     */
    private String floorno;

    /**
     * 住户姓名
     */
    private String housemaster;

    /**
     * 住户电话
     */
    private String housemastertel;


    /**
     * 住户面积
     */
    private String builtarea;


    /**
     * 缴费年限
     */
    private Integer heatyearpay;

    /**
     * 是否缴费
     */
    private Integer ispay;



    /**
     * 缴费时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paydt;


    /**
     * 应缴费
     */
    private double payamount;


    /**
     * 已缴费
     */
    private double fullpayamount;


    /**
     * 供热状态
     */
    private Integer warmstatus;


    /**
     * 欠费
     */
    private double notpayamount;



    /**
     * 操作人
     */
    private String optuser;

    /**
     * 操作时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date optdt;
}
