
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 后台用户管理表 Mapper 接口
 * </p>
 *
 * 
 * @since 2020-10-30
 */
public interface UserMapper extends BaseMapper<User> {

    /*
     * 更新用户状态
     *
     * */
    void updateUserState(String userName,Integer status,Integer loginNum);

    /*
     * 获得用户登录信息
     *
     * */
    User getUserInfo(String userName);


    /*
     * 更新用户错误登录时间
     *
     * */
    void updateLoginCount(String userName);


    /*
     * 登录成功之后，将错误登录次数置为0
     *
     * */
    void updateLoginErrNum(String userName);

    /*
     * 根据角色id 获得用户信息
     *
     * */
    List<User> getUserByroleIds(@Param("roldIds") Integer[] roldIds);

}
