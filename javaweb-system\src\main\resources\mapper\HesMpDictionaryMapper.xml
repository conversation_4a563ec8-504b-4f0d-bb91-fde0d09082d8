<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HesMpDictionaryMapper">


    <select id="getDictionaryList" resultType="com.javaweb.system.entity.HesMpDictionary">
        select * from t_hesmpdictionary order by Mpdesc asc;
    </select>

    <update id="updateMonitorStatius">
        UPDATE t_hesmonitorpointdefine hmd
            JOIN t_hesmpdictionary hd ON hd.MPField = hmd.MPFieldDefine
            SET hmd.iswebshow = hd.is_show;
    </update>
</mapper>
