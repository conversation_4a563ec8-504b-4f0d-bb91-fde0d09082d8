<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.UserRoleMapper">

    <!-- 获取用户角色 -->
    <select id="getRolesByUserId" resultType="com.javaweb.system.entity.Role">
        SELECT r.* FROM tb_role AS r
        INNER JOIN tb_user_role AS ur ON r.id=ur.role_id
        WHERE ur.user_id=#{userId} AND r.mark=1;
    </select>

</mapper>
