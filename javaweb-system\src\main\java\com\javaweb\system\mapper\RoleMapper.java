
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.Role;

import java.util.List;

/**
 * <p>
 * 系统角色表 Mapper 接口
 * </p>
 *
 * 
 * @since 2020-10-31
 */
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据用户ID获取角色信息
     *
     * @param userId 用户ID
     * @return
     */
    List<Role> getRolesByUserId(Integer userId);


    /**
     * 根据用户ID获取角色
     *
     * @param userId 用户ID
     * @return
     */
    List<Role> getRoleByUserId(Integer userId);

}
