package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>测点配置
 *
 * @Date: 2022/12/16 15:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hesmpdictionary")
public class HesMpDictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分区
     */
    private Integer num;

    /**
     * 字段名
     */
    private String mpfield;

    /**
     * 字段别名
     */
    private String fieldAlisa;

    /**
     * 描述
     */
    private String mpdesc;

    /**
     * 类型
     */
    private String mpvaluetype;

    /**
     * 最小量程
     */
    private String mpminvalue;

    /**
     * 最大量程
     */
    private String mpmaxvalue;

    /**
     * 单位
     */
    private String unit;

    /**
     * 状态是否可控(0.否 1.是)
     */
    private Integer isControl;

    /**
     * 是否报警(0.否 1.是)
     */
    private Integer isAlarm;

    /**
     * 是否显示(0.否 1.是)
     */
    private Integer isShow;

    /**
     * 是否通知(0.否 1.是)
     */
    private Integer isNotice;

}
