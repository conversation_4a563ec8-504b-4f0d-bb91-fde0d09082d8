package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.mapper.RulesMapper;
import com.javaweb.system.query.RulesQuery;
import com.javaweb.system.service.IRulesService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 报警规则 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/rules")
public class RulesController extends BaseController {

    @Autowired
    private IRulesService rulesService;

    @Autowired
    private RulesMapper rulesMapper;
    /**
     * 获取报警配置列表
     *
     * @param rulesQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(RulesQuery rulesQuery) {
        return rulesService.getList(rulesQuery);
    }

    /**
     * 添加报警配置
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "报警规则", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Rules entity) {
        return rulesService.edit(entity);
    }


    /**
     * 编辑报警规则
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "报警规则", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Rules entity) {
        return rulesService.edit(entity);
    }


    /**
     * 删除报警配置
     *
     * @param rulesIds 职级ID
     * @return
     */
    @Log(title = "报警规则", logType = LogType.DELETE)
    @DeleteMapping("/delete/{rulesIds}")
    public JsonResult delete(@PathVariable("rulesIds") Integer[] rulesIds) {
        return rulesService.deleteByIds(rulesIds);
    }

    @GetMapping("/getRulesLst")
    public JsonResult getRulesLst() {
        return JsonResult.success(rulesMapper.getRulesLst());
    }
}
