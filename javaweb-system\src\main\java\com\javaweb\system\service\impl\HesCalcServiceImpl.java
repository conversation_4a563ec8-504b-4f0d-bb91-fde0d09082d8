package com.javaweb.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HesCalc;
import com.javaweb.system.mapper.HesCalcMapper;
import com.javaweb.system.query.HesCalcQuery;
import com.javaweb.system.service.IHesCalcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 控制算法 服务实现类
 * </p>
 *
 *
 * @since 2023-2-1
 */
@Service
public class HesCalcServiceImpl  extends ServiceImpl<HesCalcMapper, HesCalc> implements IHesCalcService {


    @Autowired
    private HesCalcMapper  hescalcMapper;
    /**
     * 获取控制算法
     *
     * @param hesCalcQuery 查询条件
     * @return
     */


    @Override
    public JsonResult getList(HesCalcQuery hesCalcQuery) {

        // 查询条件
        QueryWrapper<HesCalc> queryWrapper = new QueryWrapper<>();

        // 算法编号
//        if (!StringUtils.isEmpty(hescalcQuery.getCalcIndex())) {
//            queryWrapper.like("calc_index", hescalcQuery.getCalcIndex());
//        }

        int nPage = 1;
        int nLimit = 10;

        if(null!=hesCalcQuery.getPage()) {
            nPage = hesCalcQuery.getPage();
        }
        if(null!=hesCalcQuery.getLimit()) {
            nLimit = hesCalcQuery.getLimit();
        }
        // 查询分页数据
        IPage<HesCalc> page = new Page<>(nPage,nLimit);
        IPage<HesCalc> pageData = hescalcMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }
}
