
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.common.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 人员角色表 前端控制器
 * </p>
 *
 * 
 * @since 2020-10-30
 */
@RestController
@RequestMapping("/user-role")
public class UserRoleController extends BaseController {

}
