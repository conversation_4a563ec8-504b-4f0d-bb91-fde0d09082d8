package com.javaweb.system.security;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 安全配置类
 * 用于管理系统安全相关的配置参数
 */
@Component
@ConfigurationProperties(prefix = "security")
public class SecurityConfig {

    /**
     * 登录相关安全配置
     */
    private Login login = new Login();

    /**
     * 输入验证相关配置
     */
    private Input input = new Input();

    public Login getLogin() {
        return login;
    }

    public void setLogin(Login login) {
        this.login = login;
    }

    public Input getInput() {
        return input;
    }

    public void setInput(Input input) {
        this.input = input;
    }

    /**
     * 登录安全配置
     */
    public static class Login {
        /**
         * 是否启用IP白名单验证
         */
        private boolean ipWhitelistEnabled = true;

        /**
         * IP白名单列表
         */
        private String ipWhitelist = "127.0.0.1,::1,localhost";

        /**
         * 最大登录失败次数
         */
        private int maxFailAttempts = 5;

        /**
         * 账户锁定时间（分钟）
         */
        private int lockoutDuration = 30;

        /**
         * 是否启用验证码
         */
        private boolean captchaEnabled = true;

        /**
         * 登录失败多少次后启用验证码
         */
        private int captchaThreshold = 3;

        public boolean isIpWhitelistEnabled() {
            return ipWhitelistEnabled;
        }

        public void setIpWhitelistEnabled(boolean ipWhitelistEnabled) {
            this.ipWhitelistEnabled = ipWhitelistEnabled;
        }

        public String getIpWhitelist() {
            return ipWhitelist;
        }

        public void setIpWhitelist(String ipWhitelist) {
            this.ipWhitelist = ipWhitelist;
        }

        public int getMaxFailAttempts() {
            return maxFailAttempts;
        }

        public void setMaxFailAttempts(int maxFailAttempts) {
            this.maxFailAttempts = maxFailAttempts;
        }

        public int getLockoutDuration() {
            return lockoutDuration;
        }

        public void setLockoutDuration(int lockoutDuration) {
            this.lockoutDuration = lockoutDuration;
        }

        public boolean isCaptchaEnabled() {
            return captchaEnabled;
        }

        public void setCaptchaEnabled(boolean captchaEnabled) {
            this.captchaEnabled = captchaEnabled;
        }

        public int getCaptchaThreshold() {
            return captchaThreshold;
        }

        public void setCaptchaThreshold(int captchaThreshold) {
            this.captchaThreshold = captchaThreshold;
        }
    }

    /**
     * 输入验证配置
     */
    public static class Input {
        /**
         * 是否启用严格输入验证
         */
        private boolean strictValidation = true;

        /**
         * 是否记录安全事件日志
         */
        private boolean logSecurityEvents = true;

        /**
         * 用户名最大长度
         */
        private int maxUsernameLength = 50;

        /**
         * 用户名最小长度
         */
        private int minUsernameLength = 2;

        /**
         * 验证码最大长度
         */
        private int maxCaptchaLength = 10;

        /**
         * 验证码最小长度
         */
        private int minCaptchaLength = 3;

        public boolean isStrictValidation() {
            return strictValidation;
        }

        public void setStrictValidation(boolean strictValidation) {
            this.strictValidation = strictValidation;
        }

        public boolean isLogSecurityEvents() {
            return logSecurityEvents;
        }

        public void setLogSecurityEvents(boolean logSecurityEvents) {
            this.logSecurityEvents = logSecurityEvents;
        }

        public int getMaxUsernameLength() {
            return maxUsernameLength;
        }

        public void setMaxUsernameLength(int maxUsernameLength) {
            this.maxUsernameLength = maxUsernameLength;
        }

        public int getMinUsernameLength() {
            return minUsernameLength;
        }

        public void setMinUsernameLength(int minUsernameLength) {
            this.minUsernameLength = minUsernameLength;
        }

        public int getMaxCaptchaLength() {
            return maxCaptchaLength;
        }

        public void setMaxCaptchaLength(int maxCaptchaLength) {
            this.maxCaptchaLength = maxCaptchaLength;
        }

        public int getMinCaptchaLength() {
            return minCaptchaLength;
        }

        public void setMinCaptchaLength(int minCaptchaLength) {
            this.minCaptchaLength = minCaptchaLength;
        }
    }
}
