package com.javaweb.system.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.entity.HeatUnitFloorUnit;

import java.util.List;

/**
 * <p>
 *小区信息表Mapper 接口
 * </p>
 *
 * @Date: 2022/12/12 14:31
 */

public interface HeatUnitFloorUnitMapper extends BaseMapper<HeatUnitFloorUnit> {

    List<HouseInfoDto> getUnitFloorUnitByLinkId();

    Integer getCountByNo(HeatUnitFloorUnit heatUnitFloorUnit);


    List<HeatUnitFloorUnit> getHeatUnitFloorUnitBaseinfo(Integer useheatunitid,Integer useheatunitfloorid);
}

