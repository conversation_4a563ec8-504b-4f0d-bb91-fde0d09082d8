package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.MapInfo;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.query.MapInfoQuery;
import com.javaweb.system.query.RulesQuery;

import javax.servlet.http.HttpServletRequest;

public interface IMapInfoService extends IService<MapInfo> {

    /**
     * 根据查询条件获取数据列表
     *
     * @param mapInfoQuery 查询条件
     * @return
     */
    JsonResult getList(MapInfoQuery mapInfoQuery);


    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(MapInfo entity);


    /**
     * 根据ID删除记录
     *
     * @param ids 记录ID
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);


    /**
     * 导入Excel数据
     *
     * @param request 网络请求
     * @param name    目录名称
     * @return
     */
    JsonResult importExcel(HttpServletRequest request, String name);
}
