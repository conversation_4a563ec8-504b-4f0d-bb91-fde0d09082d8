package com.javaweb.calculate.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 换热站信息
 *
 *
 * @Date: 2022/12/6 8:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hes")
public class Hes implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *小区名称
     */
    private String useheatunitName;
    /**
     *  监测站名称
     */
    private String name;
    /**
     * 监测站编号
     */
    private Integer hescode;
    /**
     * 使用年限
     */
    private Integer usedyear;
    /**
     *分区编码
     */
    private Integer equipmentnum;
    /**
     * 运行模式
     */
    private Integer runmode;
    /**
     * 是否使用
     */

    private Integer isused;
    /**
     * 类型
     */
    private Integer heatingtype;

    /**
     *是否运行
     */
    private Integer isrun;

    /**
     *热系数
     */
    private Double heatingindex;

    /**
     * 热能系数
     */
    private Double heatRate;

    /**
     * 算法控制
     */
    private Integer calcMode;


    /**
     *是否供热
     */
    private Integer isheating;

    /**
     * 流量系数
     */
    private BigDecimal gRate;
}

