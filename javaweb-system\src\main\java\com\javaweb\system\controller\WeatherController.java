package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.ExcelUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Weather;
import com.javaweb.system.query.WeatherQuery;
import com.javaweb.system.service.IWeatherService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 气象站 前端控制器
 * </p>
 *
 *
 * @since 2022-12-12
 */

@RestController
@RequestMapping("/weather")
public class WeatherController extends BaseController {

    @Autowired
    private IWeatherService weatherService;

    /**
     * 获取全部气象站数据列表
     *
     * @param weatherQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(WeatherQuery weatherQuery) {
        return weatherService.getList(weatherQuery);
    }



    /**
     * 获取气象站当天列表
     *
     * @return
     */
    @GetMapping("/getWeatherAll")
    public JsonResult getWeatherAll() {
        return  weatherService.getWeatherAll();
    }



    /**
     * 获取气象站当天列表
     * @param dt
     * @return
     */
    @GetMapping("/getweather/{dt}")
    public JsonResult getweather(@PathVariable("dt") String dt) {
        return weatherService.getWeatherDate(dt);
    }

    /**
     * 获取气象站天每小时平均数据
     * @param dt
     * @return
     */
    @GetMapping("/getWeatherHourAvgData/{dt}")
    public JsonResult getWeatherDayData(@PathVariable("dt") String dt) {
        return weatherService.getWeatherHourAvgData(dt);
    }


    /**
       * 获取气象站日最高低气温
     * @param dt
     * @return
             */
    @GetMapping("/getWeatherDayMinMax/{dt}")
    public JsonResult getWeatherDayMinMax(@PathVariable("dt") String dt) {
        return weatherService.getWeatherDayMinMax(dt);
    }

    /**
     * 获取气象站时间段天平均值
     * @param startdt  enddt
     * @return
     * */
    @GetMapping("/getWeatherDayAvgData/{startdt}/{enddt}")
    public JsonResult getWeatherDayAvgData(@PathVariable("startdt") String startdt,@PathVariable("enddt") String enddt) {
        return weatherService.getWeatherDayAvgData(startdt,enddt);
    }





}
