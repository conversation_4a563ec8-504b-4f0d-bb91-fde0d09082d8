package com.javaweb.system.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.HeatUnit;

import java.util.List;

/**
 * <p>
 *小区信息表Mapper 接口
 * </p>
 *
 * @Date: 2022/12/12 14:31
 */

public interface HeatUnitMapper extends BaseMapper<HeatUnit> {


    Integer getCountByNo(HeatUnit heatUnit);

    //得到基础信息
    List<HeatUnit> getHeatUnitBaseinfo();

    String getHeatUnitNoById(Integer id);

    Integer getHeatUnitIdByName(String name);
}

