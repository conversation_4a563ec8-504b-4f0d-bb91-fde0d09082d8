package com.javaweb.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 *
 * <p>
 *  报警规则
 * </p>
 *
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_alarmrules")
public class AlarmRules implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报警规则名称
     */
    private String name;

    /**
     * 报警字段
     */
    private String mpfield;


    /**
     * 字段别名
     */
    private String fieldAlisa;


    /**
     * 告警条件
     */
    private  String alarmCond;


    /**
     * 报警类型
     */
    private  Integer alarmType;


    /**
     * 报警等级
     */
    private  Integer alarmLevel;


    /**
     * 输出字段
     */
    private  String outField;

    /**
     * 告警描述
     */
    private  String alarmDesc;


}
