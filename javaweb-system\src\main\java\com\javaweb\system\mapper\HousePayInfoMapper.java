package com.javaweb.system.mapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.entity.HousePayInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 小区缴费表Mapper 接口
 * </p>
 *
 * @Date: 2022/12/12 14:31
 */

public interface HousePayInfoMapper extends BaseMapper<HousePayInfo> {


    Integer getCountByNo(HousePayInfo housePayInfo);


    List<HousePayInfo> getHousePayInfoByIdEx(@Param("useheatunitId")  Integer useheatunitId,
                          @Param("romno")   String romno,
                          @Param("tablename")   String tablename);


    void insertPayTable(HousePayInfo housePayInfo,
                        @Param("tablename")   String tablename);

}

