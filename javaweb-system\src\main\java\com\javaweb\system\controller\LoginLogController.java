
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.query.LoginLogQuery;
import com.javaweb.system.service.ILoginLogService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 登录日志表 前端控制器
 * </p>
 *
 * 
 * @since 2020-10-31
 */
@RestController
@RequestMapping("/loginlog")
public class LoginLogController extends BaseController {

    @Autowired
    private ILoginLogService loginLogService;

    /**
     * 获取登录日志列表
     *
     * @param loginLogQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(LoginLogQuery loginLogQuery) {
        return loginLogService.getList(loginLogQuery);
    }

    /**
     * 删除登录日志
     *
     * @param loginLogIds 登录日志ID
     * @return
     */
    @Log(title = "登录日志", logType = LogType.DELETE)
    @DeleteMapping("/delete/{loginLogIds}")
    public JsonResult delete(@PathVariable("loginLogIds") Integer[] loginLogIds) {
        return loginLogService.deleteByIds(loginLogIds);
    }

}
