
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Dept;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * 
 * @since 2020-11-03
 */
public interface IDeptService extends IBaseService<Dept> {

    /**
     * 获取部门列表
     *
     * @return
     */
    JsonResult getDeptList();

}
