package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_map_info")
public class MapInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备类型
     */
    private String devType;


    /**
     * 标记描述
     */
    private String markDesc;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 方向
     */
    private String direction;

    /**
     * 机房编号
     */
    private String machineRoomNo;

    /**
     * 公里标
     */
    private String  kilometerMark;


    /**
     * 安装地点
     */
    private String installSite;

    /**
     * 图标
     */
    private String markIcon;

    /**
     * 标记类型  1-标记 2-直线 3-面积
     */
    private Integer markType;


    /**
     * 几何信息
     */
    private String geometry;

    /**
     * 属性数据
     */
    private String properties;


    @TableField(exist=false)
    private Object geometryInfo;


    @TableField(exist=false)
    private Object propertiesInfo;


    /**
     * 是否告警
     */
    private Integer isWarning;

    /**
     * 告警值
     */
    private  String warnVal;

    /**
     * 样式
     */
    private String styles;


    @TableField(exist=false)
    private Object style;

    /**
     * 线条宽度
     */
    private  Integer lineWidth;


    /**
     * 路径类型  铁路 高速公路
     */
    private  Integer styleType;

}



