package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.entity.HeatUnitFloorUnit;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 14:44
 */
public interface IHeatUnitFloorUnitService extends IService<HeatUnitFloorUnit> {

    /**
     * 获取单元信息列表
     * @return
     */
    JsonResult getHeatUnitFloorUnitList();


    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(HeatUnitFloorUnit entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

}

