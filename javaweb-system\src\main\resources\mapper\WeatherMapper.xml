<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.WeatherMapper">

    <!-- 获取当天气象数据 -->
    <select id="getWeatherBytimes" resultType="com.javaweb.system.entity.Weather">
        SELECT  * FROM t_weatherstationdata  order by id desc limit 10;
    </select>



    <!-- 获取当天气象数据 -->
    <select id="getWeatherBydt" resultType="com.javaweb.system.entity.Weather">
        SELECT * FROM t_weatherstationdata  where collectdt >= #{dt} order by id desc;
    </select>

    <select  id="getWeatherDayMinMaxBydt" resultType="com.javaweb.system.dto.WeatherDto">
        SELECT  CAST(AVG(T) as decimal(10,2)) as avgT,CAST(Min(T) as decimal(10,2)) as minT, CAST(Max(T) as decimal(10,2))
          as maxT from t_weatherstationdata where CollectDT>=#{startdt} and CollectDT &lt;=#{enddt};
    </select>
</mapper>
