package com.javaweb.system.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HeatUnitFloor;

import java.util.List;

/**
 * <p>
 *小区信息表Mapper 接口
 * </p>
 *
 * @Date: 2022/12/12 14:31
 */

public interface HeatUnitFloorMapper extends BaseMapper<HeatUnitFloor> {

//    通过关联id获得小区信息
    List<HouseInfoDto> getHeatUnitFloorByLinkId();

    Integer getCountByNo(HeatUnitFloor heatUnitFloor);


    List<HeatUnitFloor>  getHeatUnitFloorBaseinfo(Integer useheatunitid);


}

