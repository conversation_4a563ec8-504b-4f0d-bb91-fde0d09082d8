package com.javaweb.api.Netty;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaweb.common.config.UploadFileConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/*
* 处理接收的换热站的数据队列
* */
@Component
public class DataProcessor {
    //数据队列
    private final BlockingQueue<String> queue = new LinkedBlockingQueue<>(100); // 设置上限为100

    //状态队列
    private final BlockingQueue<String> udpQueue = new LinkedBlockingQueue<>(100); // 设置上限为100

    private final DataSource dataSource;
    private final String uploadPath;
    private final Map<String, String> hesCodeToRecordsMap = new HashMap<>();

    private TcpClient tcpClient;
    private  String m_AField="";
    private  String m_AField_24="";

    // 构造函数注入 DataSource
    @Autowired
    public DataProcessor(DataSource dataSource, UploadFileConfig uploadFileConfig)  {
        this.dataSource = dataSource;
        this.uploadPath = uploadFileConfig.uploadFolder;
        tcpClient = new TcpClient("10.219.2.197", 6500);

        //tcpClient = new TcpClient("127.0.0.1", 8345);
    }

    @PostConstruct
    public void init(){
        //读取数据库字段配置
        fetchAndGroupHESMonitorPoints();
        String m_AField_path=uploadPath+"udp_hes_point_config.txt";
        String m_AField_24_path=uploadPath+"udp_hes_point_config_24.txt";;
        try {
            m_AField=extractFields(m_AField_path);
            m_AField_24=extractFields(m_AField_24_path);
        } catch (IOException e)
        {
            System.out.println("====读取文件报错=====");
            e.printStackTrace();
        }
        // 启动现有队列的处理线程
        Thread workerThread = new Thread(this::processData);
        workerThread.setDaemon(true);
        workerThread.start();

        // 启动UDP队列的处理线程
        Thread udpWorkerThread = new Thread(this::processUdpData);
        udpWorkerThread.setDaemon(true);
        udpWorkerThread.start();
    }
    private void processData() {
        while (true) {
            try {
                // 打印队列信息
                String data = queue.take(); // 阻塞直到获取到数据
                processAndSaveToDatabase(data);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break; // 或者处理中断异常
            }
        }
    }

    private void processUdpData() {
        while (true) {
            try {
                String data = udpQueue.take(); // 阻塞直到获取到数据
                processAndSaveUdpData(data); // 假设你有一个方法来处理UDP数据
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break; // 或者处理中断异常
            }
        }
    }
    public boolean addDataToQueue(String data) {
        try {
            return queue.offer(data, 1, TimeUnit.SECONDS); // 尝试添加到队列，超时则返回false
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    public boolean addDataToUdpQueue(String data) {
        try {
            return udpQueue.offer(data, 1, TimeUnit.SECONDS); // 尝试添加到队列，超时则返回false
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    //处理插入数据库
    private void processAndSaveToDatabase(String recvdata) {
        // 解析 JSON 数据包
        JSONObject dataPacket = JSONObject.parseObject(recvdata);
        String hescode = dataPacket.getString("hescode");
        int datalen = Integer.parseInt(dataPacket.getString("datalen"));
        int runmodel = Integer.parseInt(dataPacket.getString("runmodel"));
        List<String> dataValues = Arrays.asList(dataPacket.getString("data").split(","));
        // 根据 hescode 获取 MPFieldDefine
        String MPFieldDefine = getMPFieldDefineByHesCode(hescode);
        if (!MPFieldDefine.isEmpty()) {
            // 验证 MPFieldDefine 的字段个数和 datalen 是否一致
            if (validateFieldCount(MPFieldDefine, datalen)) {
                // 插入数据到数据库
                System.err.println("插入换热站 hescode: " + hescode);
                insertDataIntoDB(hescode, MPFieldDefine, dataValues, runmodel);
                //更新运行模式
                updateHesRunMode(hescode, runmodel);  // 修改此处，传递现有的连接
            } else {
                System.err.println("Validation failed for hescode: " + hescode);
            }
        } else {
            System.err.println("No MPFieldDefine found for hescode: " + hescode);
        }
    }
    //更新数据库的运行模式  自动=手动
    private void updateHesRunMode(String hescode, int runMode) {
        String sql = "UPDATE t_hes SET runMode=? WHERE hescode=?";
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(sql)) {

            // 设置参数
            pStatement.setInt(1, runMode);
            pStatement.setString(2, hescode);
            pStatement.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //插入状态数据
    private void processAndSaveUdpData(String udpData) {
        // 解析并保存UDP数据到数据库的逻辑
        // 尝试解析 JSON 对象
        JSONObject obj = JSONObject.parseObject(udpData);
        // 获取字段值
        int hescode = obj.getInteger("hescode");
        String data = obj.getString("data");
        //插入数据库
        SetValue(hescode, data);
    }

    private synchronized void fetchAndGroupHESMonitorPoints() {
        if (!hesCodeToRecordsMap.isEmpty()) {
            return; // 如果已经加载过，则不再重复加载
        }
        String sql = "SELECT id, hescode, MPFieldDefine FROM t_hes_monitorpoint ORDER BY id ASC";

        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

             while (rs.next()) {
                String hescode = rs.getString("hescode");
                String MPFieldDefine = rs.getString("MPFieldDefine");

                // 如果 MPFieldDefine 不为空，则存入 map
                if (MPFieldDefine != null && !MPFieldDefine.trim().isEmpty()) {
                    hesCodeToRecordsMap.put(hescode, MPFieldDefine);
                }
            }
        } catch (SQLException e) {
            // 处理异常
            e.printStackTrace();
        }
    }

    /**
     * 根据 hescode 获取对应的 MPFieldDefine。
     *
     * @param hescode 要查找的 hescode
     * @return 对应的 MPFieldDefine 字符串，如果找不到则返回空字符串。
     */
    public String getMPFieldDefineByHesCode(String hescode) {
        return hesCodeToRecordsMap.getOrDefault(hescode, "");
    }

    /**
     * 验证 MPFieldDefine 的字段个数和 datalen 是否一致
     */
    private boolean validateFieldCount(String MPFieldDefine, int datalen) {
        if (MPFieldDefine == null || MPFieldDefine.isEmpty()) {
            return false;
        }
        // 假设 MPFieldDefine 是以逗号分隔的字段定义列表
        String[] fields = MPFieldDefine.split(",");
        return fields.length == datalen;
    }

    /**
     * 将解析后的数据插入到数据库中
     */
    private void insertDataIntoDB(String hescode, String MPFieldDefine, List<String> dataValues,Integer runmodel) {
        String tableName = "t_hes_data_" + hescode;
        String[] fields = MPFieldDefine.split(",");
        String collectdt = getCurrentDateTime();
        //System.out.println("换热站编号hescode="+hescode);
        StringBuilder columns = new StringBuilder();
        StringBuilder placeholders = new StringBuilder();

        // 确保 dataValues 的大小至少等于 fields 的长度
        if (dataValues.size() < fields.length) {
            System.err.println("Data values count does not match field count for hescode: " + hescode);
            return; // 或者采取其他措施处理这种情况
        }
        // 构建列名和占位符字符串，确保一对一对应
        for (int i = 0; i < fields.length; i++) {
            if (i > 0) {
                columns.append(", ");
                placeholders.append(", ");
            }
            columns.append(fields[i]);
            placeholders.append("?");
        }
        // 添加 collectdt 到列名和占位符
        columns.append(", collectdt");
        placeholders.append(", ?");
        String sql = "INSERT INTO " + tableName + " (" + columns.toString() + ") VALUES (" + placeholders.toString() + ")";
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(sql)) {

            // 设置所有参数值，包括 collectdt
            for (int i = 0; i < dataValues.size(); i++) {
                pstmt.setString(i + 1, dataValues.get(i));
            }
            pstmt.setString(dataValues.size() + 1, collectdt); // 设置 collectdt
            pstmt.executeUpdate(); // 执行更新操作
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 发送数据包  到指定ip 地址
        List<DataItem> dataItems = new ArrayList<>();
        for (int i = 0; i < fields.length; i++) {
            dataItems.add(new DataItem(fields[i], dataValues.get(i)));
        }
        DataPacket packet = new DataPacket(dataItems, hescode, getCurrentDateTime(), runmodel);

        ObjectMapper mapper = new ObjectMapper();
        try {
            String jsonString = mapper.writeValueAsString(packet)+"##";
            tcpClient.send(jsonString); // 使用长连接发送数据
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //读取txt 文件
    public static String extractFields(String filePath) throws IOException {
        StringBuilder fieldNames = new StringBuilder();

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                // 分割每一行，获取字段名
                String[] parts = line.split(",");
                if (parts.length > 1) {
                    String fieldName = parts[1].trim();
                    if (fieldNames.length() > 0) {
                        fieldNames.append(",");
                    }
                    fieldNames.append(fieldName);
                }
            }
        }
        return fieldNames.toString();
    }

    private void SetValue(int code, String value) {
        try {
            if (code == 24) {
                if (!m_AField_24.isEmpty()) {
                    SetSingleValueToDB("dt,code," + m_AField_24,
                            code + "," + value,
                            "tb_hes_state", "code=" + QuotedStr(Integer.toString(code)));
                }
            } else {
                if (!m_AField.isEmpty()) {
                    SetSingleValueToDB("dt,code," + m_AField,
                            code + "," + value,
                            "tb_hes_state", "code=" + QuotedStr(Integer.toString(code)));
                }
            }
        } catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
        }
    }

    private void SetSingleValueToDB(String AField, String AValue, String ATable, String AWhere) {
        try (Connection connection = dataSource.getConnection()) {
            String sql = "SELECT * FROM " + ATable + " WHERE " + AWhere;
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                try (ResultSet rSet = pStatement.executeQuery()) {

                    // 创建日期格式化对象，并指定时区
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));  // 设置为中国时区

                    // 获取当前时间的毫秒值并格式化为字符串
                    String collectdt = sdf.format(System.currentTimeMillis());

                    if (rSet.next()) {
                        AValue = collectdt + "," + AValue;
                        String[] partAField = AField.split(",");
                        String[] partAValue = AValue.split(",");
                        Vector<String> vecAField = new Vector<>();
                        Vector<String> vecAValue = new Vector<>();

                        for (String part : partAField) {
                            vecAField.add(part.trim());
                        }
                        for (String part : partAValue) {
                            vecAValue.add(part.trim());
                        }

                        if (vecAField.size() == 0 || vecAField.size() != vecAValue.size()) {
                            return;
                        }

                        sql = " SET ";
                        for (int i = 0; i < vecAField.size(); i++) {
                            if (i == vecAField.size() - 1) {
                                sql += vecAField.get(i) + "=" + QuotedStr(vecAValue.get(i));
                            } else {
                                sql += vecAField.get(i) + "=" + QuotedStr(vecAValue.get(i)) + ",";
                            }
                        }
                        sql = "UPDATE " + ATable + sql + " WHERE " + AWhere;
                    } else {
                        sql = "INSERT INTO " + ATable + "(" + AField + ") VALUES("
                                + QuotedStr(collectdt) + "," + AValue + ")";
                    }
                    //System.out.println("sql="+sql);
                    try (PreparedStatement updateStatement = connection.prepareStatement(sql)) {
                        updateStatement.executeUpdate();
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
        }
    }

    private String QuotedStr(String str)
    {
        return "'"+str+"'";
    }

    private String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        String formattedDateTime = sdf.format(System.currentTimeMillis());
        return formattedDateTime;
    }

}