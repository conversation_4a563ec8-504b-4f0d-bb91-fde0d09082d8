package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.query.RecodeQuery;
import com.javaweb.system.query.RepairListQuery;
import com.javaweb.system.service.IRecodeService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 报警记录 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/recode")
public class RecodeController extends BaseController {

    @Autowired
    private IRecodeService recodeService;

    /**
     * 获取报警记录列表
     *
     * @param recodeQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(RecodeQuery recodeQuery) {
        return recodeService.getList(recodeQuery);
    }


    /**
     * 获取实时报警列表
     *
     * @param recodeQuery 查询条件
     * @return
     */
    @GetMapping("/alarm")
    public JsonResult alarm(RecodeQuery recodeQuery) {
        return recodeService.getAlarmList(recodeQuery);
    }


    /**
     * 删除报警记录
     *
     * @param recodeIds 职级ID
     * @return
     */
    @Log(title = "报警记录", logType = LogType.DELETE)
    @DeleteMapping("/delete/{recodeIds}")
    public JsonResult delete(@PathVariable("recodeIds") Integer[] recodeIds) {
        return recodeService.deleteByIds(recodeIds);
    }

    @GetMapping("/getAlarmRecode")
    public JsonResult getAlarmRecode() {
        return recodeService.getAlarmRecodedata();
    }
}
