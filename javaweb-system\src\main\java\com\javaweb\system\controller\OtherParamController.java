package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.OtherParam;
import com.javaweb.system.query.OtherParamQuery;
import com.javaweb.system.service.IOtherParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 *
 * @since 2023-02-07
 */

@RestController
@RequestMapping("/otherparam")
public class OtherParamController extends BaseController {

    @Autowired
    private IOtherParamService otherParamService;

    /**
     * 添加用户
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "其他参数配置", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody OtherParam entity) {
        return otherParamService.edit(entity);
    }

    /**
     * 获取数据
     *
     * @return
     */
    @GetMapping("/getCurrentdataAll")
    public JsonResult getCurrentdataAll(OtherParamQuery OtherParamQuery) {
        return  otherParamService.getCurrentDataAll(OtherParamQuery);
    }


    @GetMapping("/getOtherParamInfo")
    public JsonResult getOtherParamInfo() {
        return  otherParamService.getOtherParamInfo();
    }



}
