package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HouseInfo;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 14:44
 */
public interface IHouseInfoService extends IService<HouseInfo> {




    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(HouseInfo entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);


    JsonResult getHouseInfoList();

}

