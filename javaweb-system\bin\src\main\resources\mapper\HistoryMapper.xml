<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HistoryMapper">

    <!-- 获取监测站的历史数据 -->
    <select id="getHistoryStation" resultType="com.javaweb.system.entity.HistoryData">
        SELECT * FROM   #{tablename}  order by id desc limit 0,10;
    </select>


    <!-- 获取监测站的配置测点 -->
    <select id="getHesCedian" resultType="com.javaweb.system.entity.HistoryData">
        SELECT * FROM  t_hesmonitorpointdefine  where hescode=#{hescode} ;
    </select>
</mapper>
