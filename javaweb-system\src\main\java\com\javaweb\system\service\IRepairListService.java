package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.RepairList;
import com.javaweb.system.query.RepairListQuery;

public interface IRepairListService extends IService<RepairList> {


    /**
     * 根据查询条件获取数据列表
     *
     * @param repairlistQuery 查询条件
     * @return
     */
    JsonResult getList(RepairListQuery repairlistQuery);


    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(RepairList entity);

    /**
     * 根据ID删除记录
     *
     * @param ids 记录ID
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

}
