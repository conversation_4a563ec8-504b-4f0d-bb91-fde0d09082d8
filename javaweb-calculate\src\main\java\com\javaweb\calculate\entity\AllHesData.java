package com.javaweb.calculate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_allhesdata")
public class AllHesData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 时间
     */
    private String collectDt;

    /**
     * 监测站名称
     */
    private String hesname;

    /**
     * 供热类型
     */
    private String heatType;

    /**
     * 控制模式
     */
    private String controlMode;

    /**
     *高区供水温度
     */
    private String hSst;

    /**
     *高区回水温度
     */
    private String hSbt;

    /**
     *低区供水温度
     */
    private String lSst;

    /**
     *低区回水温度
     */
    private String lSbt;

    /**
     *三区供水温度
     */
    private String midSst;

    /**
     *三区回水温度
     */
    private String midSbt;

    /**
     *四区供水温度
     */
    private String fourSst;

    /**
     *四区回水温度
     */
    private String fourSbt;

    /**
     *供水温度
     */
    private String fst;

    /**
     *回水温度
     */
    private String fbt;

    /**
     *瞬时流量
     */
    private String fsf;


}



