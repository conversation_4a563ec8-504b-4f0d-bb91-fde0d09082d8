
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Notice;

/**
 * <p>
 * 通知公告表 服务类
 * </p>
 *
 * 
 * @since 2020-11-07
 */
public interface INoticeService extends IBaseService<Notice> {

    /**
     * 设置是否置顶
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult setIsTop(Notice entity);

}
