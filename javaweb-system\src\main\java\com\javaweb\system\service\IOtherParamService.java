package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.OtherParam;
import com.javaweb.system.entity.SystemParam;
import com.javaweb.system.query.OtherParamQuery;
import com.javaweb.system.query.SystemParamQuery;

public interface IOtherParamService extends IService<OtherParam> {


    /**
     * 获取所有监测站实时数据
     *
     * @return
     */
    JsonResult getCurrentDataAll(OtherParamQuery currentdataQuery);



    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(OtherParam entity);


    /**
     * 获取参数信息
     *
     * @return
     */
    JsonResult getOtherParamInfo();




}
