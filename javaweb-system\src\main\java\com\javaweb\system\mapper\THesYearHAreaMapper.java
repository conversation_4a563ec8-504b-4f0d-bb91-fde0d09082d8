package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.HesYearHArea;

import java.util.List;

/**
 * 增删改查
 *
 * @Description :
 * <AUTHOR> mc
 * @Date: 2022/12/6 15:15
 */
public interface THesYearHAreaMapper extends BaseMapper<HesYearHArea> {



    List<HesYearHArea> getList();

    /*
    * 获得换热站的面积
    *
    * */
    String getHareaByDate(int hescode,int hyear,int hmonth);

    /*
     * 获得换热站的年面积
     *
     * */
    String getHareaByYear(int hescode,int hyear);
}

