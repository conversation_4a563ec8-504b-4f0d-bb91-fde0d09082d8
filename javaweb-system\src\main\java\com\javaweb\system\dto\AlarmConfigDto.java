package com.javaweb.system.dto;

import lombok.Data;

@Data
public class AlarmConfigDto {

    private Integer id;

    /**
     * 换热站编号
     */
    private Integer hescode;

    /**
     * 换热站名称
     */
    private String hesname;


    /**
     * 报警规则名称
     */
    private String name;

    /**
     * 报警字段
     */
    private String mpfield;


    /**
     * 字段别名
     */
    private String fieldAlisa;


    /**
     * 告警条件
     */
    private  String cond;

    /**
     * 报警类型
     */
    private  Integer alarmType;


    /**
     * 报警等级
     */
    private  Integer alarmLevel;

    /**
     * 输出字段 （不要）
     */
    private  String outField;

    /**
     * 告警描述
     */
    private  String alarmDesc;


    /**
     * 条件
     */
    private String runCond;

    /**
     * 工作周
     */
    private String weekRule;


    /**
     * 是否启用
     */
    private Integer isUsed;


}
