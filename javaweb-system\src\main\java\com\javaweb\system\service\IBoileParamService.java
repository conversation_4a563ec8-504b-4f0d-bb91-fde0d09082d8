package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.BoileParam;
import com.javaweb.system.entity.CurrentData;
import com.javaweb.system.entity.User;
import com.javaweb.system.query.CurrentDataQuery;


public interface IBoileParamService extends IService<BoileParam> {

    /**
     * 根据实体对象添加、编辑记录
     *
     * @param entity 实体对象
     * @return
     */
    JsonResult edit(BoileParam entity);


}
