package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HouseDataInfo;
import com.javaweb.system.entity.HousePayInfo;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2024/09/09 8:40
 */
public interface IHouseDataInfoService extends IService<HouseDataInfo> {


    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);


    /**
     * 获取日数据
     *
     * @return
     */
    JsonResult getIndoorTDayData(Integer useheatunitId,String heatno);

}

