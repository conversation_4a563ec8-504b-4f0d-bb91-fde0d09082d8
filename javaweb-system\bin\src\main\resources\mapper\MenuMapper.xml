<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.MenuMapper">

    <!-- 获取用户权限列表 -->
    <select id="getPermissionsListByUserId" resultType="com.javaweb.system.entity.Menu">
        SELECT DISTINCT m.* FROM tb_menu AS m
         INNER JOIN tb_role_menu AS rm ON m.id=rm.menu_id
         INNER JOIN tb_user AS ur ON ur.role_id=rm.role_id
        WHERE ur.id=#{userId} AND m.type = 0 AND m.pid=#{pid} AND m.`status`=1 AND m.mark=1
        ORDER BY m.sort ASC;
    </select>

    <!-- 获取所有菜单权限 -->
    <select id="getPermissionsAll" resultType="com.javaweb.system.entity.Menu">
        SELECT * FROM tb_menu WHERE type &lt;= 2 AND mark=1 ORDER BY pid ASC,sort ASC;
    </select>

    <!-- 获取用户权限节点列表 -->
    <select id="getPermissionList" resultType="java.lang.String">
        SELECT DISTINCT m.permission FROM tb_menu AS m
          INNER JOIN tb_role_menu AS rm ON m.id=rm.menu_id
          INNER JOIN tb_user AS ur ON ur.role_id=rm.role_id
        WHERE ur.id=#{userId} AND m.type=1 AND m.`status`=1 AND m.mark=1
        ORDER BY m.sort ASC;

    </select>

    <!-- 更新所有pid下级的可视 -->
    <update id="updateMenuHide" parameterType="com.javaweb.system.entity.Menu">
        update tb_menu set  syshide=#{syshide}  where  pid=#{id}
    </update>

</mapper>
