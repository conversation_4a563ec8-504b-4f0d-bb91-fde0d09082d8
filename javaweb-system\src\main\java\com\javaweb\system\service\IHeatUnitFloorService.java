package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HeatUnitFloor;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 14:44
 */
public interface IHeatUnitFloorService extends IService<HeatUnitFloor> {

    /**
     * 获取楼宇信息列表
     * @return
     */
    JsonResult getHeatUnitFloorList();


    /**
     * 获取列表
     *
     * @return
     */
    JsonResult getList(BaseQuery query);

    /**
     * 编辑
     *
     * @return
     */
    JsonResult edit(HeatUnitFloor entity);

    /**
     * 删除
     *
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);

}

