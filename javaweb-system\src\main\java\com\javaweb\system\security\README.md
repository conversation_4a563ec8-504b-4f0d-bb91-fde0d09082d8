# 登录安全增强功能

本模块为系统登录功能提供了全面的安全增强，包括IP白名单验证、输入安全验证、安全审计日志等功能。

## 功能特性

### 1. IP白名单验证 (IpWhitelistValidator)

**功能描述：**
- 对访问登录接口的客户端进行IP地址限制验证
- 支持精确IP匹配、CIDR网段匹配、通配符匹配
- 可配置是否启用IP白名单验证

**支持的IP格式：**
- 精确匹配：`127.0.0.1`
- CIDR格式：`***********/24`
- 通配符：`192.168.1.*`
- 特殊值：`localhost`、`::1`

**配置示例：**
```yaml
security:
  login:
    ip-whitelist-enabled: true
    ip-whitelist: 127.0.0.1,::1,localhost,***********/24,10.0.0.0/8
```

### 2. 输入安全验证 (InputSecurityValidator)

**功能描述：**
- 验证用户输入的安全性，防止注入攻击
- 过滤和转义特殊字符
- 检测SQL注入、命令注入、XSS攻击等

**验证内容：**
- 用户名：只允许字母、数字、下划线、中文字符
- 密码：检测危险字符和注入攻击
- 验证码：只允许字母和数字
- 验证码Key：UUID格式验证

**危险字符检测：**
- 特殊字符：`|`, `;`, `&`, `$`, `\n`, `\r`, `<`, `>`, `"`, `'`, `\`
- SQL关键词：`union`, `select`, `insert`, `update`, `delete`, `drop`, `create`, `alter`, `exec`
- 命令注入：`cmd`, `powershell`, `bash`, `sh`, `exec`, `system`, `runtime`
- XSS攻击：`<script`, `</script`, `javascript:`, `vbscript:`, `onload=`, `onerror=`

### 3. 安全审计日志 (SecurityAuditLogger)

**功能描述：**
- 记录所有安全相关的事件和操作
- 提供详细的审计跟踪
- 支持不同级别的日志记录

**记录的事件类型：**
- `LOGIN_SUCCESS`：登录成功
- `LOGIN_FAILURE`：登录失败
- `IP_WHITELIST_VIOLATION`：IP白名单违规
- `INPUT_VALIDATION_FAILURE`：输入验证失败
- `SECURITY_ATTACK`：安全攻击检测
- `ACCOUNT_LOCKOUT`：账户锁定
- `PASSWORD_CHANGE_SUCCESS/FAILURE`：密码修改

**日志格式：**
```
[2024-01-20 10:30:45] 事件类型: LOGIN_FAILURE | 客户端IP: ************* | 用户名: admin | 用户代理: Mozilla/5.0... | 详情: 密码错误，失败次数: 3
```

### 4. 登录安全拦截器 (LoginSecurityInterceptor)

**功能描述：**
- 在Controller层进行安全验证
- 对登录接口进行IP白名单检查
- 提供统一的安全响应处理

**拦截路径：**
- `/login/**`
- `/api/login/**`

**排除路径：**
- `/login/captcha`（验证码接口）
- `/login/logout`（退出登录接口）
- `/login/un_auth`（未授权接口）

## 安全配置

### 配置文件 (application-dev.yml)

```yaml
security:
  login:
    # 是否启用IP白名单验证
    ip-whitelist-enabled: true
    # IP白名单列表（支持CIDR格式和通配符）
    ip-whitelist: 127.0.0.1,::1,localhost,***********/24,10.0.0.0/8
    # 最大登录失败次数
    max-fail-attempts: 5
    # 账户锁定时间（分钟）
    lockout-duration: 30
    # 是否启用验证码
    captcha-enabled: true
    # 登录失败多少次后启用验证码
    captcha-threshold: 3
  input:
    # 是否启用严格输入验证
    strict-validation: true
    # 是否记录安全事件日志
    log-security-events: true
    # 用户名最大长度
    max-username-length: 50
    # 用户名最小长度
    min-username-length: 2
    # 验证码最大长度
    max-captcha-length: 10
    # 验证码最小长度
    min-captcha-length: 3
```

## 安全增强的登录流程

1. **IP白名单验证**：检查客户端IP是否在允许的白名单中
2. **输入安全验证**：验证用户名、密码、验证码的安全性
3. **验证码校验**：验证验证码的正确性和有效性
4. **密码解密验证**：安全解密并验证密码
5. **用户状态检查**：检查用户账户状态和锁定情况
6. **登录认证**：执行Shiro认证
7. **成功处理**：记录成功日志，返回token
8. **异常处理**：记录失败日志，更新失败次数，必要时锁定账户

## 安全事件监控

系统会自动记录以下安全事件：

- **登录尝试**：成功和失败的登录尝试
- **IP违规**：不在白名单中的IP访问
- **输入攻击**：检测到的注入攻击尝试
- **账户锁定**：因多次失败导致的账户锁定
- **密码修改**：密码修改操作

## 部署建议

### 生产环境配置

1. **严格的IP白名单**：只允许必要的IP地址访问
2. **启用所有安全验证**：确保所有安全功能都已启用
3. **监控安全日志**：定期检查安全审计日志
4. **及时更新配置**：根据安全需求调整配置参数

### 开发环境配置

1. **宽松的IP白名单**：允许开发团队的IP地址
2. **保留安全验证**：确保开发过程中也进行安全测试
3. **日志调试**：启用详细的安全日志用于调试

## 注意事项

1. **性能影响**：IP白名单验证和输入验证会增加少量性能开销
2. **配置更新**：修改IP白名单后需要重启应用
3. **日志存储**：安全日志会占用存储空间，需要定期清理
4. **误报处理**：某些合法输入可能被误判为攻击，需要调整验证规则

## 扩展功能

可以根据需要扩展以下功能：

1. **动态IP白名单**：支持运行时动态更新IP白名单
2. **地理位置限制**：基于IP地理位置的访问控制
3. **设备指纹**：基于设备特征的安全验证
4. **行为分析**：基于用户行为模式的异常检测
5. **多因素认证**：集成短信验证码、邮箱验证等
