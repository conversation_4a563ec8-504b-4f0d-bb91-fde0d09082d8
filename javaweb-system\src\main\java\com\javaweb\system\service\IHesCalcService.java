package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HesCalc;
import com.javaweb.system.query.HesCalcQuery;

public interface IHesCalcService  extends IService<HesCalc> {

    /**
     * 获取控制算法
     *
     * @return
     */
    //JsonResult getHesCalcList();

    /**
     * 根据查询条件获取所有数据列表
     *
     * @param hesCalcQuery 查询条件
     * @return
     */
    JsonResult getList(HesCalcQuery hesCalcQuery);

}
