package com.javaweb.calculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.calculate.entity.AllHesData;
import com.javaweb.calculate.entity.Hes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AllHesDataMapper extends BaseMapper<AllHesData> {

    List<AllHesData> getAllHesData();


    int getNumByHescode(@Param("hescode") String hescode);
}
