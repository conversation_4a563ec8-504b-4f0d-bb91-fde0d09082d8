package com.javaweb.system.workthread;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.MonitorDto;
import com.javaweb.system.entity.*;
import com.javaweb.system.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Configuration
@EnableScheduling
public class WorkMainThreadServer {
    @Autowired
    DataSource dataSource;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    HesMonitorPointDeFineMapper hesMonitorPointDeFineMapper;

    @Autowired
    THesMapper tHesMapper;

    @Autowired
    RedisDataOptServer redisDataOptServer;

    @Autowired
    HesMpDictionaryMapper hesMpDictionaryMapper;

    @Autowired
    ConfigLoader configLoader;

    @Autowired
    OtherParamMapper otherParamMapper;

    @Autowired
    AlarmConfigMapper alarmConfigMapper;

    @Autowired
    RulesMapper alarmRulesMapper;

    @Autowired
    RunRulesMapper runRulesMapper;

    @Autowired
    RecodeMapper recodeMapper;




    /*
    * 保存换热站数据大屏redis
    * */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay=1000,fixedDelay = Long.MAX_VALUE)
    public void initRedisData()
    {
        //换热站信息表
        redisDataOptServer.redisSaveHesList();

        List<THes>  heslist=redisDataOptServer.getRedisSaveHesList();
        //获取测点库字段列表
        List<HesMpDictionary> hesfieldlist=hesMpDictionaryMapper.getDictionaryList();
        redisUtils.lSet("HesFieldList",hesfieldlist);
        for (int i=0; i<heslist.size();i++)
        {
            int hescode = heslist.get(i).getHescode();
            List<MonitorDto> hesdictDatas = hesMonitorPointDeFineMapper.getDictDataByHescode(hescode);
            String  hesfield="hesfield:"+String.valueOf(hescode);
            redisUtils.del(hesfield);
            //存入到redis中
            redisUtils.lSet(hesfield,hesdictDatas);
        }
        /*
         * 加载告警类
         * */
        configLoader.loadConfigsAndRules();
    }


    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay=5000,fixedDelay= 1800000)
    public void startCalcThread1() {
        String hashname="FD:HesTypeCount";
        Map<String, Object> map= new HashMap<>();
        String[] unitType = {"住宅楼", "办公楼", "商业楼", "工厂区", "别墅", "学校", "其他"};
        try (Connection connection = dataSource.getConnection()) {
            for (String type : unitType) {
                String sql = "SELECT COUNT(b.id) AS num FROM t_useheatunit a JOIN t_hes b ON a.Name = b.UseHeatUnit_name WHERE a.UnitType = ?";
                try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                    preparedStatement.setString(1, type);
                    try (ResultSet resultSet = preparedStatement.executeQuery()) {
                        if (resultSet.next()) {
                            int num = resultSet.getInt("num");
                            map.put(type,num);
                        }
                    }
                }
            }
            redisUtils.hmset(hashname,map);
        } catch (SQLException e) {
            e.printStackTrace();
            // 可以在这里记录日志或抛出自定义异常
        } catch (Exception e) {
            e.printStackTrace();
            // 可以在这里记录日志或抛出自定义异常
        }
    }



    /*   得到配置表中的告警检测周期，看是否满足检测间隔。满足则开始下面逻辑
     *  1.加载所有告警配置信息，包括告警配置模板和运行规则
     *  2.遍历告警配置信息，先验证是否在运行规则周期里面，
     *  3.若满足运行周期，解析对应的采集数据包
     *  4.根据告警规则进行验证是否告警，
     *    若是告警：查询同一规则中是否存在有故障状态的记录，
     *       若有故障状态的记录，则更新
     *       1).若为提醒状态，则根据提醒周期进行提醒，以及最后一次提醒时间进行判断，提醒并更新最后一次提醒时间
     *       2).若为忽略状态，则验证忽略周期，是否超过忽略周期，判断是否提醒，更新最后一次提醒时间
     *       3).若为不提醒状态，则不提醒；
     *      若无故障状态的记录，则插入，
     *   若是正常：查询同一规则中是否存在有故障状态的记录，若有故障状态，则更新故障状态为正常；
     *
     *
     */
    //@Async("threadPoolTaskExecutor")
    //@Scheduled(initialDelay=5000,fixedDelay= 900000) //15分钟执行一次
    public void redisAlarmRules() {

        //判断是否满足检测间隔周期 满足
        OtherParam  Otherparam=otherParamMapper.getOtherParamInfo();
        Boolean isInspect=StringUtils.isTimeExceeded(Otherparam.getLastAlarmDate(),Otherparam.getAlarmCycle());
        if(isInspect)
        {
            // 从数据库加载所有监测站告警配置
            List<AlarmConfig> alarmConfigLst = alarmConfigMapper.getAlarmConfigLst();

            // 从数据库加载所有运行规则模板
            List<RunRules> runRulesLst = runRulesMapper.getRunRulesLst();

            // 从数据库加载所有告警规则模板
            List<Rules> alarmRulesLst = alarmRulesMapper.getRulesLst();

            try (Connection connection = dataSource.getConnection()) {
                //从redis 中获取最新数据
                // 验证是否告警
                for (AlarmConfig config : alarmConfigLst) {
                    // 获取运行规则
                    Integer runRuleId = config.getRunRulesIds();
                    RunRules runRule = getRunRuleById(runRulesLst, runRuleId);
                    //判断是否满足运行规则
                    if (configLoader.isInRunRuleTime(runRule))
                    {
                        //得到对应的换热站编号，并通过编号得到数据包
                        Integer hescode = config.getHescode();
                        String data = redisDataOptServer.getRedisHesDataByCode(hescode);
                        if(StringUtils.isNotEmpty(data) && data !="null")
                        {
                            JSONObject recvdata = JSONObject.parseObject(data);
                            JSONArray data_arr = recvdata.getJSONArray("data");

                            // 获取告警规则
                            String rules[] = config.getAlarmRulesIds().split(",");
                            // 循环遍历规则
                            for (String ruleId : rules)
                            {
                                Integer Id=Integer.valueOf(ruleId);
                                Rules rule = getAlarmRuleById(alarmRulesLst, Id);
                                //查询记录中是否有告警状态的 此条规则的记录
                                Recode recode=recodeMapper.getAlarmBycode(hescode,Id);
                                //若满足告警条件
                                if (configLoader.isAlarmConditionMet(rule, data_arr))
                                {
                                    //记录为空则插入,并需要进行提醒
                                    if(StringUtils.isNull(recode))
                                    {
                                        // 满足告警条件，插入告警记录
                                        configLoader.insertAlarmRecord(connection, hescode,rule);
                                    }else
                                    {
                                        //判断最后一次提醒时间是否满足了提醒周期
                                    }

                                }else //若不满足
                                {
                                    //记录不为空更新该记录的告警状态为正常；
                                    if(StringUtils.isNotNull(recode))
                                    {
                                        recodeMapper.updateAlarmStatusById(recode.getId());
                                    }
                                }
                                //删除redis数据
                                redisDataOptServer.delRedisHesDataByCode(hescode);
                            }
                        }

                    }
                }
            } catch (Exception ex) {
                System.out.println("错误信息: " + ex.getMessage());
                ex.printStackTrace();  // 增加详细的堆栈跟踪
            }

        }
//        List<AlarmConfig> alarmConfigLst=configLoader.getAlarmConfigByRedis(hescode);
//        // 验证是否告警
//        for (AlarmConfig config : alarmConfigLst)
//        {
//            // 获取运行规则
//            RunRules runRule = config.getRunRuleTemplate();
//            // 检查是否在运行规则的时间段内
//            if (configLoader.isInRunRuleTime(runRule))
//            {
//                // 获取告警规则模板列表
//                List<Rules> alarmRuleTemplates = config.getAlarmRuleTemplates();
//                // 检查告警条件
//                for (Rules rule : alarmRuleTemplates)
//                {
//                    if (configLoader.isAlarmConditionMet(rule, data_arr))
//                    {
//                        // 满足告警条件，插入告警记录
//                        configLoader.insertAlarmRecord(connection, Integer.parseInt(hescode),rule);
//                        break;
//                    }
//                }
//            }
//        }
    }


    public RunRules getRunRuleById(List<RunRules> runRulesLst, int id) {
        // 使用Java 8的Stream API来查找id匹配的RunRules对象
        Optional<RunRules> runRuleOpt = runRulesLst.stream()
                .filter(runRule -> runRule.getId() == id)
                .findFirst();

        // 如果找到了，返回RunRules对象；如果没有找到，返回null或者你可以选择抛出异常
        return runRuleOpt.orElse(null);
    }

    public Rules getAlarmRuleById(List<Rules> alarmRulesLst, int id) {
        // 使用Java 8的Stream API来查找id匹配的Rules对象
        Optional<Rules> ruleOpt = alarmRulesLst.stream()
                .filter(rule -> rule.getId() == id)
                .findFirst();

        // 如果找到了，返回Rules对象；如果没有找到，返回null或者你可以选择抛出异常
        return  ruleOpt.orElse(null);
    }
    /*
     *
     *计算换热站能耗实时能耗曲线
     *
     *
     */
    public void startCalcThread3()
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String sql=null;
        String dt="2022-03-17";
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        List<THes>  hesList= tHesMapper.getHesUsed();
        for(int i=0;i<hesList.size();i++)
        {
            int    hescode=hesList.get(i).getHescode();
            Double heatrate=hesList.get(i).getHeatRate();
            String hesname=hesList.get(i).getName();
            Double heatingindex=hesList.get(0).getHeatingindex();
            String tablename="t_hes_statistics_"+String.valueOf(hescode);
            try {
                connection = dataSource.getConnection();
                String sql_a="SELECT top 1  heara  FROM t_hes_Year_HArea where hescode="+hescode+" order by id desc";
                pStatement = connection.prepareStatement(sql_a);
                rSet = pStatement.executeQuery();
                while (rSet.next())
                {
                    Double area = Double.valueOf(rSet.getString("heara"));
                    // 查询时间
                    if (!StringUtils.isEmpty(dt))
                    {
                        sql = "select  F_S_C_H,avgT  from " + tablename + " where dt='"+dt+"'";
                    }else
                    {
                        sql = "select  top 1 F_S_C_H,avgT  from " + tablename + " order by id desc ";
                    }

                    pStatement = connection.prepareStatement(sql);
                    rSet = pStatement.executeQuery();
                    ResultSetMetaData data = rSet.getMetaData();
                    while (rSet.next())
                    {
                        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                        Double heat = Double.valueOf(rSet.getString("F_S_C_H"));
                        //能耗
                        Double enery = (heat * 1000000000) * heatrate / area / 86400 / 10000;
                        Double jieneng=heatingindex-enery;
                        //电量
                        Double dian=jieneng*area*10000/1000/24;
                        //二氧化碳
                        String CO2=String.format("%.2f",dian*0.977);
                        //碳排放
                        String tan=String.format("%.2f",dian*0.272);
                        String energys=String.format("%.2f",enery);
                        resultMap.put("name",  hesname);
                        resultMap.put("value", CO2);
                        resultMap.put("enery", energys);
                        resultMap.put("heat",  heat);
                        resultMap.put("dian", String.format("%.2f",dian));
                        resultMap.put("tan", tan);
                        resultMap.put("dt", dt);
                        lst.add(resultMap);
                    }
                }
                System.out.println(lst);
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }finally {
                if (connection!=null) try {connection.close();}catch (Exception ignore) {}
            }
        }
    }

   /*
    *   只计算一次 更新计算能耗理论值
    *  Q=1000*Q0/A0*(A1+(A0-A1)*0.3)/A1*(Tn-Tw)/21.4
        式中：
        Q：理论天能耗，W/m2
        Q0：换热站设计负荷，kW
        A0：换热站设计面积，m2
        A1：换热站实际供热面积，m2
        Tn：室内实际日平均温度，℃
        Tw：室外实际日平均温度，℃
    * */
    //@Async("threadPoolTaskExecutor")
    //@Scheduled(initialDelay=1000,fixedDelay = Long.MAX_VALUE)
    public void updateTheoryEnergy() {
        String sqlSelect = "SELECT hescode, dt, avgT, area, energy FROM t_hes_statistics_day";
        String sqlDesign = "SELECT hescode, design_load, design_area FROM t_hes GROUP BY hescode";
        String sqlUpdate = "UPDATE t_hes_statistics_day SET theory_energy = ? WHERE hescode = ? AND dt = ?";
        Map<Integer, Double[]> designValuesCache = new HashMap<>();
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false); // 开始事务

            // 查询设计负荷和设计面积并存入缓存
            try (PreparedStatement stmtDesign = connection.prepareStatement(sqlDesign);
                 ResultSet rsDesign = stmtDesign.executeQuery()) {
                while (rsDesign.next()) {
                    int hescode = rsDesign.getInt("hescode");
                    double designLoad = rsDesign.getDouble("design_load");
                    double designArea = rsDesign.getDouble("design_area");
                    designValuesCache.put(hescode, new Double[]{designLoad, designArea});
                }
            }

            // 查询统计数据并进行理论能耗计算和更新
            try (PreparedStatement stmtSelect = connection.prepareStatement(sqlSelect);
                 ResultSet rs = stmtSelect.executeQuery();
                 PreparedStatement stmtUpdate = connection.prepareStatement(sqlUpdate)) {

                 while (rs.next()) {
                    Integer hescode = rs.getInt("hescode");
                    String dt = rs.getString("dt");
                    double Tw = rs.getDouble("avgT");
                    double A1 = rs.getDouble("area");
                    double Tn = calcAvgIndoorT(connection, hescode, dt);

                    Double[] designValues = designValuesCache.get(hescode);
                    double Q0 = designValues[0];
                    double A0 = designValues[1];

                    double Q = 1000 * Q0 / A0 * (A1 + (A0 - A1) * 0.3) / A1 * (Tn - Tw) / 21.4;

                     if (!Double.isNaN(Q) && !Double.isInfinite(Q)) {
                         stmtUpdate.setDouble(1, Q);
                         stmtUpdate.setInt(2, hescode);
                         stmtUpdate.setString(3, dt);
                         stmtUpdate.executeUpdate();
                     } else {
                         // 处理无效的 Q 值，例如记录日志、跳过更新或设置为默认值
                         System.err.println("Invalid Q value for hescode: " + hescode + " and dt: " + dt);
                     }
                }
            }

            connection.commit(); // 提交事务
            System.err.println("更新完毕");
        } catch (SQLException e) {
            // 处理异常，例如回滚事务或记录日志
            e.printStackTrace();
        }
    }


    public double calcAvgIndoorT(Connection connection, Integer hescode, String dt) {
        String sql = "SELECT AVG(install_t) as avg_temp FROM t_hes_indoort WHERE hescode = ? AND install_dt >= ? and  install_dt<?";
        double avgTemperature = 23; // 默认值

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, hescode);
            stmt.setString(2, dt+" 00:00:00");
            stmt.setString(3, dt+" 23:59:59");
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    avgTemperature = rs.getDouble("avg_temp");
                    if (rs.wasNull()) {
                        avgTemperature = 23; // 如果查询结果为空，则使用默认值
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace(); // 在实际应用中，你可能需要更合适的错误处理
        }

        return avgTemperature;
    }






    /*只计算一次,计算*/
    //@Async("threadPoolTaskExecutor")
    //@Scheduled(initialDelay=1000,fixedDelay = Long.MAX_VALUE)
//    public void calcHourEnergy() {
//        String sqlSelect = "SELECT * FROM t_hes_statistics_hour";
//        String sqlDesign = "SELECT hescode, design_load, design_area FROM t_hes GROUP BY hescode";
//        String updateSQLTemplate = "UPDATE t_hes_statistics_hour SET %s = ? WHERE id = ?";
//        Map<Integer, Double[]> designValuesCache = new HashMap<>();
//
//        try (Connection connection = dataSource.getConnection()) {
//            //connection.setAutoCommit(false); // 开始事务
//
//            // 查询设计负荷和设计面积并存入缓存
//            try (PreparedStatement stmtDesign = connection.prepareStatement(sqlDesign);
//                 ResultSet rsDesign = stmtDesign.executeQuery()) {
//                while (rsDesign.next()) {
//                    int hescode = rsDesign.getInt("hescode");
//                    double designLoad = rsDesign.getDouble("design_load");
//                    double designArea = rsDesign.getDouble("design_area");
//                    designValuesCache.put(hescode, new Double[]{designLoad, designArea});
//                }
//            }
//            // 查询统计数据并进行理论能耗计算和更新
//            try (PreparedStatement stmtSelect = connection.prepareStatement(sqlSelect);
//                 ResultSet rs = stmtSelect.executeQuery()) {
//                while (rs.next()) {
//                    int id = rs.getInt("id");
//                    Integer hescode = rs.getInt("hescode");
//                    String dt = rs.getString("dt");
//                    Double[] designValues = designValuesCache.get(hescode);
//                    double Q0 = designValues[0];
//                    double A0 = designValues[1];
//                    double A1 = rs.getDouble("area");
//
//                    for (int i = 0; i <= 23; i++) {
//                        String fieldName = "h" + i;
//                        String originalValue = rs.getString(fieldName);
////                        double Tn = calcAvgIndoorTForSpecificHour(connection, hescode, dt, i);
//                        if (originalValue != null && !originalValue.trim().isEmpty()) {
//                            String[] parts = originalValue.split("~");
//                            int secondLastHyphenIndex = originalValue.lastIndexOf("~", originalValue.lastIndexOf("-") - 1);
//                            // 截取第一个数值（可能是负数）
//                            String firstValue = originalValue.substring(0, secondLastHyphenIndex);
//                            String updatedValue="0.0";
//                            if (parts.length > 0) {
//                                double Tw = Double.parseDouble(firstValue);
//                                double Q = 1000 * Q0 / A0 * (A1 + (A0 - A1) * 0.3) / A1 * (Tn - Tw) / 21.4;
//                                if (!Double.isNaN(Q) && !Double.isInfinite(Q)) {
//                                    DecimalFormat df = new DecimalFormat("#.###");
//                                    String formattedQ = df.format(Q);
//                                    updatedValue = originalValue + "~" + formattedQ;
//                                }
//
//                                // 动态生成更新语句
//                                String updateSQL = String.format(updateSQLTemplate, fieldName);
//                                try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
//                                    updateStmt.setString(1, updatedValue);
//                                    updateStmt.setInt(2, id);
//                                    updateStmt.executeUpdate();
//
//                                }
//                            }
//                        }
//                    }
//                    System.out.println("计算完毕！id="+id);
//                }
//            }
//
//           // connection.commit(); // 提交事务
//
//        } catch (SQLException e) {
//            // 处理异常，例如回滚事务或记录日志
//            e.printStackTrace();
//        }
//    }

    // Helper method to extract the substring before the first occurrence of a delimiter
    private String substringBeforeFirst(String s, String delimiter) {
        int index = s.indexOf(delimiter);
        if (index == -1) {
            return s;
        } else {
            return s.substring(0, index);
        }
    }

//    public void calcHourEnergy() {
//        Connection connection = null;
//        PreparedStatement selectStmt = null;
//
//        try {
//            connection = dataSource.getConnection();
//            connection.setAutoCommit(false); // 开始事务
//
//            String selectSQL = "SELECT * FROM t_hes_statistics_hour" +
//                    " WHERE h0='0.0'";
//            String updateSQLTemplate = "UPDATE t_hes_statistics_hour SET %s = ? WHERE id = ?";
//
//            selectStmt = connection.prepareStatement(selectSQL);
//
//            ResultSet rs = selectStmt.executeQuery();
//            while (rs.next()) {
//                int id = rs.getInt("id");
//                boolean hasUpdates = false;
//
//                for (int i = 0; i <= 23; i++) {
//                    String fieldName = "h" + i;
//                    String originalValue = rs.getString(fieldName);
//
//                    if (originalValue != null && !originalValue.trim().isEmpty()) {
//                        try {
////                            String processedValue = substringBeforeLast(originalValue,"-");
//                            String processedValue ="0.0-0.0-0.0-0.0";
//                                    // 动态生成更新语句
//                            String updateSQL = String.format(updateSQLTemplate, fieldName);
//                            try (PreparedStatement updateStmt = connection.prepareStatement(updateSQL)) {
//                                updateStmt.setString(1, processedValue);
//                                updateStmt.setInt(2, id);
//                                updateStmt.executeUpdate();
//                                hasUpdates = true;
//                            }
//                        } catch (IllegalArgumentException e) {
//                            System.out.println("Error processing field " + fieldName + " for ID " + id + ": " + e.getMessage());
//                        }
//                    }
//                }
//
//                if (hasUpdates) {
//                    connection.commit(); // 每处理完一行就提交事务
//                    System.out.println("小时能耗更新完毕！ID: " + id);
//                } else {
//                    connection.rollback(); // 如果没有更新，则回滚该行的事务
//                }
//            }
//
//        } catch (SQLException e) {
//            if (connection != null) {
//                try {
//                    connection.rollback(); // 回滚事务
//                    System.out.println("Transaction rolled back due to an error.");
//                } catch (SQLException rollbackEx) {
//                    System.out.println("Error rolling back transaction."+ rollbackEx);
//                }
//            }
//            System.out.println("An error occurred while processing data."+ e);
//        } finally {
//            // 关闭资源
//            try {
//                if (selectStmt != null) selectStmt.close();
//                if (connection != null) connection.close();
//            } catch (SQLException e) {
//                System.out.println("Error closing resources."+ e);
//            }
//        }
//    }


}


