
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.admin.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.CommonUtils;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.admin.constant.TAlarmConstant;
import com.javaweb.admin.entity.TAlarm;
import com.javaweb.admin.mapper.TAlarmMapper;
import com.javaweb.admin.query.TAlarmQuery;
import com.javaweb.admin.service.ITAlarmService;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.utils.ShiroUtils;
import com.javaweb.admin.vo.talarm.TAlarmInfoVo;
import com.javaweb.admin.vo.talarm.TAlarmListVo;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

/**
  * <p>
  *  服务类实现
  * </p>
  *
  * <AUTHOR>
  * @since 2024-09-10
  */
@Service
public class TAlarmServiceImpl extends BaseServiceImpl<TAlarmMapper, TAlarm> implements ITAlarmService {

    @Autowired
    private TAlarmMapper tAlarmMapper;

    /**
     * 获取数据列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        TAlarmQuery tAlarmQuery = (TAlarmQuery) query;
        // 查询条件
        QueryWrapper<TAlarm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mark", 1);
        queryWrapper.orderByDesc("id");

        // 获取数据列表
        IPage<TAlarm> page = new Page<>(tAlarmQuery.getPage(), tAlarmQuery.getLimit());
        IPage<TAlarm> pageData = tAlarmMapper.selectPage(page, queryWrapper);
        pageData.convert(x -> {
            TAlarmListVo tAlarmListVo = Convert.convert(TAlarmListVo.class, x);
            return tAlarmListVo;
        });
        return JsonResult.success(pageData);
    }

    /**
     * 获取详情Vo
     *
     * @param id 记录ID
     * @return
     */
    @Override
    public Object getInfo(Serializable id) {
        TAlarm entity = (TAlarm) super.getInfo(id);
        // 返回视图Vo
        TAlarmInfoVo tAlarmInfoVo = new TAlarmInfoVo();
        // 拷贝属性
        BeanUtils.copyProperties(entity, tAlarmInfoVo);
        return tAlarmInfoVo;
    }

    /**
     * 添加、更新记录
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(TAlarm entity) {
        if (StringUtils.isNotNull(entity.getId()) && entity.getId() > 0) {
        } else {
        }
        return super.edit(entity);
    }

    /**
     * 删除记录
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult delete(TAlarm entity) {
        entity.setMark(0);
        return super.delete(entity);
    }

}