package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.mapper.HouseInfoMapper;
import com.javaweb.system.query.HouseInfoQuery;
import com.javaweb.system.query.UseHeatUnitQuery;
import com.javaweb.system.service.IHeatUnitService;
import com.javaweb.system.service.IHouseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/houseinfo")
public class HouseInfoController extends BaseController {

    @Autowired
    private IHouseInfoService houseInfoService;

    @Autowired
    private HouseInfoMapper houseInfoMapper;
    /**
     * 获取查询列表
     * @param houseInfoQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HouseInfoQuery houseInfoQuery) {
        return houseInfoService.getList(houseInfoQuery);
    }


    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "单元信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HouseInfo entity){

        return houseInfoService.edit(entity);
    }

    @Log(title = "单元信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HouseInfo entity){

        return houseInfoService.edit(entity);
    }

    @Log(title = "单元信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{useHeatUnitIds}")
    public JsonResult delete(@PathVariable("useHeatUnitIds") Integer[]useHeatUnitIds){

        return houseInfoService.deleteByIds(useHeatUnitIds);
    }
    @GetMapping("/getHouseInfoList")
    public JsonResult getHouseInfoList(){
        return houseInfoService.getHouseInfoList();
    }

    //获得基本信息  id 名称
    @GetMapping("/getHouseBaseinfo/{useheatunitId}/{useheatunitfloorId}/{useheatunitfloorunitId}")
    public JsonResult getHouseBaseinfo(@PathVariable Integer useheatunitId,
                                       @PathVariable Integer useheatunitfloorId,
                                       @PathVariable Integer useheatunitfloorunitId)
    {
        return JsonResult.success(houseInfoMapper.getHouseBaseinfo(useheatunitId,useheatunitfloorId,useheatunitfloorunitId));
    }

}

