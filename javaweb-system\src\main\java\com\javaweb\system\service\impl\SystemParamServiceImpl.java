package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.CommonUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.SystemParam;
import com.javaweb.system.mapper.SystemParamMapper;
import com.javaweb.system.query.SystemParamQuery;
import com.javaweb.system.service.ISystemParamService;
import com.javaweb.system.vo.configweb.SystemParamVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 实时监测 服务实现类
 * </p>
 *
 *
 * @since 2022-12-12
 */

@Service
public class SystemParamServiceImpl extends ServiceImpl<SystemParamMapper, SystemParam> implements ISystemParamService
{
    @Autowired
    private SystemParamMapper systemParamMapper;

    /**
     * 获取所有数据列表
     *
     * @return
     */
    @Override
    public JsonResult getCurrentDataAll(SystemParamQuery currentdataQuery) {
        QueryWrapper<SystemParam>  queryWrapper = new QueryWrapper<>();
        IPage<SystemParam> page = new Page<>(currentdataQuery.getPage(), currentdataQuery.getLimit());
        IPage<SystemParam> pageData = systemParamMapper.selectPage(page, queryWrapper);
        return JsonResult.success(pageData);
    }


    @Override
    public JsonResult edit(SystemParam entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        // logo处理
        if (!StringUtils.isEmpty(entity.getSystemLogo()) && entity.getSystemLogo().contains(CommonConfig.imageURL)) {
            entity.setSystemLogo(entity.getSystemLogo().replaceAll(CommonConfig.imageURL, ""));
        }

        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }




    /**
     * 获取系统参数信息
     *
     * @return
     */
    @Override
    public JsonResult getSystemParamInfo() {


        // 获取参数信息
        SystemParam systemParam = systemParamMapper.selectById(1);

        // 拷贝属性
        SystemParamVo systemParamVo = new SystemParamVo();
        BeanUtils.copyProperties(systemParam, systemParamVo);
        // logo
        if (!StringUtils.isEmpty(systemParam.getSystemLogo())) {
            systemParamVo.setSystemLogo(CommonUtils.getImageURL(systemParam.getSystemLogo()));
        }

        return JsonResult.success(systemParamVo);

    }

}





