package com.javaweb.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 *
 * <p>
 *  气象站
 * </p>
 *
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_weatherstationdata")

public class Weather implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 气象站名称
     */
    private String ws;

    /**
     * 时间
     */
    private String collectdt;

    /**
     * 温度
     */
    private  String t;

    /**
     * 湿度
     */
    private  String h;


    /**
     * 风速
     */
    private  String s;

    /**
     * 太阳辐射
     */
    private  String r;

}
