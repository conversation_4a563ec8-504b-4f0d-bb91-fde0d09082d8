package com.javaweb.calculate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

//日能耗统计表
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hes_statistics_day")
public class StatisticsDay {
    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 监测站编号
     */
    private String hescode;


    /**
     * 时间
     */
    private String dt;

    /**
     * 是否计算
     */
    private Integer iscalc;


    /**
     * 热量
     */
    private double fSCH;


    /**
     * 流量
     */
    private double fSCF;


    /**
     * 平均温度
     */
    private double avgt;


    private double area;


    private double unitH;

    private double energy;


    private double theoryEnergy;
}
