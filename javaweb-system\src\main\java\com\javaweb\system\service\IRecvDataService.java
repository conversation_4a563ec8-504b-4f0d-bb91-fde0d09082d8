package com.javaweb.system.service;


import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.JsonResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IRecvDataService{

    /**
     * 获取验证码
     *
     * @param request 请求响应
     * @return
     */
//    JsonResult getRecvdata(HttpServletResponse response);

    JsonResult getRecvdata(JSONObject data, HttpServletRequest request);


    /**
     * 接收换热站数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvHesdata(String data, HttpServletRequest request);


    /**
     * 接收气象站数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvWsdata(String data, HttpServletRequest request);

    /**
     * 接收室内采集数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvIndoorTdata(String data, HttpServletRequest request);

}
