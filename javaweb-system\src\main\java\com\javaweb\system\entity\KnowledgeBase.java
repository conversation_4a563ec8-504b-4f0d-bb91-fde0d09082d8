package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 维修工单表
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tb_knowledge_base")
public class KnowledgeBase extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
     *的问题分类相对应
     */
    private String faultClass;

    /**
     * 存储问题的简短描述
     */
    private String title;


    /**
     * 存储问题的详细描述及解决方案
     */
    private String detail;


    /**
     * 存储便于搜索和分类的关键词
     */
    private String keywords;


}

