<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 子模块的parent要使用顶层的父模块-->
    <parent>
        <artifactId>javaweb</artifactId>
        <groupId>com.javaweb</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>javaweb-generator</artifactId>
    <packaging>jar</packaging>
    <name>javaweb-generator</name>
    <description>系统工具模块</description>

    <!-- 依赖声明 -->
    <dependencies>
        <!-- 基础依赖 -->
        <dependency>
            <groupId>com.javaweb</groupId>
            <artifactId>javaweb-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.javaweb</groupId>
            <artifactId>javaweb-system</artifactId>
        </dependency>
        <!-- MySql驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--mybatis-plus 代码自动生成 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.2.0</version>
        </dependency>
        <!-- freemarker模板引擎依赖 -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>