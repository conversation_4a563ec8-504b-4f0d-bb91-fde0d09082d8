<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.calculate.mapper.HesMapper">

    <!-- 获取换热站信息 -->
    <select id="getHesList" resultType="com.javaweb.calculate.entity.Hes">
        select Name,hescode,runMode,heatingType,equipmentNum from t_hes where isused=1;
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesName" resultType="String">
        SELECT Name FROM t_hes where hescode=#{hescode};
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesByNo" resultType="com.javaweb.calculate.entity.Hes">
        SELECT hescode,Name,UseHeatUnit_name,heat_rate,heatingindex FROM t_hes where hescode=#{hescode};
    </select>


</mapper>
