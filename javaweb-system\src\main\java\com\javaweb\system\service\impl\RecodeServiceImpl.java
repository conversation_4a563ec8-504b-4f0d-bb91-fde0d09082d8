package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.constant.UserConstant;
import com.javaweb.system.entity.*;
import com.javaweb.system.mapper.DictDataMapper;
import com.javaweb.system.mapper.DictMapper;
import com.javaweb.system.mapper.RecodeMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.RecodeQuery;
import com.javaweb.system.service.IRecodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 报警记录 服务实现类
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Service
public class RecodeServiceImpl extends ServiceImpl<RecodeMapper, Recode> implements IRecodeService {

    @Autowired
    private RecodeMapper recodeMapper;

    @Autowired
    private THesMapper thesMapper;

    @Autowired
    DataSource dataSource;

    @Override
    public JsonResult getList(RecodeQuery recodeQuery) {

        // 查询条件
        QueryWrapper<Recode> queryWrapper = new QueryWrapper<>();

        // 换热站ID
        if (StringUtils.isNotNull(recodeQuery.getHescode())) {
            queryWrapper.eq("hescode", recodeQuery.getHescode());
        }
        queryWrapper.orderByDesc("id");
        int nPage = 1;
        int nLimit = 10;

        if(null!=recodeQuery.getPage()) {
            nPage = recodeQuery.getPage();
        }
        if(null!=recodeQuery.getLimit()) {
            nLimit = recodeQuery.getLimit();
        }

        // 查询分页数据
        IPage<Recode> page = new Page<>(nPage,nLimit);
        IPage<Recode> pageData = recodeMapper.selectPage(page, queryWrapper);

        pageData.convert(x -> {
            Recode recodelist = Convert.convert(Recode.class, x);
            // 换热站名称
            if (StringUtils.isNotNull(x.getHescode())) {
                THes hesInfo = thesMapper.selectByCode(x.getHescode());
                if (StringUtils.isNotNull(hesInfo)) {
                    recodelist.setHesname(hesInfo.getName());
                }
            }
            return recodelist;
        });

        return JsonResult.success(pageData);
    }

    //实时报警
    public JsonResult getAlarmList(RecodeQuery recodeQuery)
    {

        QueryWrapper<Recode> queryWrapper = new QueryWrapper<>();
        // 换热站ID
        if (StringUtils.isNotNull(recodeQuery.getHescode())) {
            queryWrapper.eq("hescode", recodeQuery.getHescode());
        }
        // 换热站ID
        if (StringUtils.isNotNull(recodeQuery.getHescode())) {
            queryWrapper.eq("hescode", recodeQuery.getHescode());
        }
        queryWrapper.eq("isalarm", 1);
        queryWrapper.orderByDesc("id");
        int nPage = 1;
        int nLimit = 10;

        if(null!=recodeQuery.getPage()) {
            nPage = recodeQuery.getPage();
        }
        if(null!=recodeQuery.getLimit()) {
            nLimit = recodeQuery.getLimit();
        }

        // 查询分页数据
        IPage<Recode> page = new Page<>(nPage,nLimit);
        IPage<Recode> pageData  = recodeMapper.selectPage(page, queryWrapper);
        pageData.convert(x -> {
            Recode recodelist = Convert.convert(Recode.class, x);
            // 换热站名称
            if (StringUtils.isNotNull(x.getHescode())) {
                THes hesInfo = thesMapper.selectByCode(x.getHescode());
                if (StringUtils.isNotNull(hesInfo)) {
                    recodelist.setHesname(hesInfo.getName());
                }
            }
            return recodelist;
        });

        return JsonResult.success(pageData);
    }
    /**
     * 根据用户ID删除用户
     *
     * @param ids 记录ID
     * @return
     */
    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }


    /**
     * 获得最新5条报警数据
     *
     * @return
     */
    @Override
    public JsonResult getAlarmRecodedata()
    {

        List<Recode>  recodeList= recodeMapper.getAlarmData();
        for(int i=0;i<recodeList.size();i++)
        {
            THes hesInfo = thesMapper.selectByCode(recodeList.get(i).getHescode());
            if (StringUtils.isNotNull(hesInfo)) {
                recodeList.get(i).setHesname(hesInfo.getName());
            }
        }
        System.out.println(recodeList);
        return JsonResult.success(recodeList);
    }

}
