
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.admin.controller;

import com.javaweb.common.enums.LogType;
import com.javaweb.common.common.BaseController;
import com.javaweb.admin.entity.TAlarm;
import com.javaweb.admin.query.TAlarmQuery;
import com.javaweb.admin.service.ITAlarmService;
import com.javaweb.common.annotation.Log;
import com.javaweb.common.utils.JsonResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@RestController
@RequestMapping("/talarm")
public class TAlarmController extends BaseController {

    @Autowired
    private ITAlarmService tAlarmService;

    /**
     * 获取数据列表
     *
     * @param query 查询条件
     * @return
     */
    @RequiresPermissions("sys:talarm:index")
    @GetMapping("/index")
    public JsonResult index(TAlarmQuery query) {
        return tAlarmService.getList(query);
    }

    /**
     * 添加记录
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "", logType = LogType.INSERT)
    @RequiresPermissions("sys:talarm:add")
    @PostMapping("/add")
    public JsonResult add(@RequestBody TAlarm entity) {
        return tAlarmService.edit(entity);
    }

    /**
     * 获取详情
     *
     * @param talarmId 记录ID
     * @return
     */
    @GetMapping("/info/{talarmId}")
    public JsonResult info(@PathVariable("talarmId") Integer talarmId) {
        return tAlarmService.info(talarmId);
    }

    /**
     * 更新记录
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "", logType = LogType.UPDATE)
    @RequiresPermissions("sys:talarm:edit")
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody TAlarm entity) {
        return tAlarmService.edit(entity);
    }

    /**
     * 删除记录
     *
     * @param talarmIds 记录ID
     * @return
     */
    @Log(title = "", logType = LogType.DELETE)
    @RequiresPermissions("sys:talarm:delete")
    @DeleteMapping("/delete/{talarmIds}")
    public JsonResult delete(@PathVariable("talarmIds") Integer[] talarmIds) {
        return tAlarmService.deleteByIds(talarmIds);
    }

}