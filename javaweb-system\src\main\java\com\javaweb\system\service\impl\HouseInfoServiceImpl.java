package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.*;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.HouseInfoMapper;
import com.javaweb.system.query.HouseInfoQuery;
import com.javaweb.system.query.UseHeatUnitQuery;
import com.javaweb.system.service.IHeatUnitService;
import com.javaweb.system.service.IHouseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *小区信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HouseInfoServiceImpl extends ServiceImpl<HouseInfoMapper, HouseInfo> implements IHouseInfoService {

    @Autowired
    HouseInfoMapper houseInfoMapper;

    /**
     * 查询小区列表
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        HouseInfoQuery houseInfoQuery =(HouseInfoQuery) query;
        //查询条件
        QueryWrapper<HouseInfo> queryWrapper = new QueryWrapper<>();
        //热表编号
        if(!StringUtils.isEmpty(houseInfoQuery.getHeatmeterno())){
            queryWrapper.like("heatmeterno",houseInfoQuery.getHeatmeterno());
        }
        //住户姓名
        if(!StringUtils.isEmpty(houseInfoQuery.getHousemaster())){
            queryWrapper.like("housemaster",houseInfoQuery.getHousemaster());
        }
        queryWrapper.orderByDesc("id");
         Integer npage=1;
         Integer nlimit=10;
         if(houseInfoQuery.getPage() !=null)
         {
             npage=houseInfoQuery.getPage();
        }
         if(houseInfoQuery.getLimit() !=null)
         {
          nlimit=houseInfoQuery.getLimit();
         }
        //查询分页数据
        IPage<HouseInfo> page = new Page<>(npage,nlimit);
        IPage<HouseInfo> pageData = houseInfoMapper.selectPage(page,queryWrapper);
       // System.out.println("分页数据："+pageData);
        //根据关联关系得到名字
        List<HouseInfoDto> houseInfoDtolst=houseInfoMapper.getHouseInfoByLinkId();
        pageData.convert(x -> {
            HouseInfo houseInfoList = Convert.convert(HouseInfo.class, x);

                houseInfoDtolst.forEach(houseInfo->{
                    if(houseInfoList.getId().equals(houseInfo.getId()))
                    {
                        houseInfoList.setName(houseInfo.getName());
                        houseInfoList.setFloorname(houseInfo.getFloorname());
                        houseInfoList.setFloorunitname(houseInfo.getFloorunitname());
                    }
                });
            return houseInfoList;
        });
        System.out.println(pageData);
        return  JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HouseInfo entity) {
        //通过验证热表编号是否存在
        Integer num=0;
        num= houseInfoMapper.getCountByNo(entity);
        if(num>0)
        {
            return JsonResult.error("热表编号重复，该住户已存在");
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHouseInfoList() {
        QueryWrapper<HouseInfo> queryWrapper = new QueryWrapper<>();
        List<HouseInfo> list = list(queryWrapper);
        return JsonResult.success(list);
    }
}




