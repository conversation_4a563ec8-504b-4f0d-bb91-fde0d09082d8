package com.javaweb.api.Netty;

import com.alibaba.fastjson.JSONObject;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.CharsetUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: gfk
 * @create: 2024-10-08
 * @Description:
 * @FileName: UdpServerhandler
 * @History:
 * @自定义内容：
 */
@Component
public class UdpServerhandler  extends SimpleChannelInboundHandler<DatagramPacket> {

    private final DataProcessor dataProcessor;

    // 文件保存目录路径
    @Autowired
    public UdpServerhandler(DataProcessor dataProcessor) {
        this.dataProcessor = dataProcessor;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) throws Exception {
        ByteBuf content = packet.content();

        // 检查内容是否为空
        if (content == null || !content.isReadable()) {
            //System.err.println("Received an empty or unreadable packet.");
            return;
        }
        // 将内容转换为字符串
        String request = content.toString(CharsetUtil.UTF_8);
        //System.out.println("UDP接收到数据==" + request);
        // 检查请求字符串是否为空
        if (request == null || request.isEmpty()) {
            //System.err.println("Received an empty request string.");
            return;
        }

        try {
            // 尝试解析 JSON 对象
            JSONObject obj = JSONObject.parseObject(request);

            // 检查 JSON 对象是否包含必要的字段
            if (obj == null || !obj.containsKey("hescode") || !obj.containsKey("data")) {
                System.err.println("Invalid JSON object: " + request);
                return;
            }

            // 添加到队列中进行处理
            boolean added = dataProcessor.addDataToQueue(request);
            if (!added) {
                // 队列已满时的处理逻辑
                System.err.println("Queue is full, data discarded.");
            }
            String hescode = obj.getString("hescode");
            String data = obj.getString("data");

            // 构造响应
            String response = "Response: Received hescode=" + hescode + " and data=" + data;
            ByteBuf responseBuf = Unpooled.copiedBuffer(response, CharsetUtil.UTF_8);

            // 发送响应
            ctx.writeAndFlush(new DatagramPacket(responseBuf, packet.sender()));
        } catch (Exception e) {
            // 处理非 JSON 数据的情况
            // System.err.println("Error parsing JSON: " + request);
            // e.printStackTrace();
            // 可选：发送一个错误响应
            String errorResponse = "Error: Invalid JSON format";
            ByteBuf errorResponseBuf = Unpooled.copiedBuffer(errorResponse, CharsetUtil.UTF_8);
            ctx.writeAndFlush(new DatagramPacket(errorResponseBuf, packet.sender()));
        }
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }




}

