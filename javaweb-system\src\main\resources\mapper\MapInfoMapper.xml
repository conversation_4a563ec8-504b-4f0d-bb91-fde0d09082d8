<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.MapInfoMapper">
        <!-- 获取数据 -->
        <select id="getMapInfoLst" resultType="com.javaweb.system.entity.MapInfo">
                SELECT * FROM t_map_info  order by id desc ;
        </select>

        <select id="getMapInfo" resultType="com.javaweb.system.entity.MapInfo">
                SELECT * FROM t_map_info where id=#{id};
        </select>

        <select id="getMapInfoByName" resultType="com.javaweb.system.entity.MapInfo">
                SELECT * FROM t_map_info where name=#{name};
        </select>


        <!--根据ids得到标记信息-->
        <select id="getMapInfoLstByIds" resultType="com.javaweb.system.entity.MapInfo">

                select * from t_map_info
                where  id in
                <foreach collection="Ids" item="item" index="index" open="(" separator="," close=")" >
                        (#{item})
                </foreach>  and mark_type=1
                order by id asc
        </select>
</mapper>
