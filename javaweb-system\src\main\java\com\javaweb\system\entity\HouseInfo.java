package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *小区信息
 * </p>
 *
 * @Date: 2022/12/12 14:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_houseinfo")
public class HouseInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 小区id
     */
    private Integer useheatunitId;

    /**
     * 小区
     */
    @TableField(exist=false)
    private String name;

    /**
     * 楼宇id
     */
    private Integer useheatunitfloorId;

    /**
     * 楼宇
     */
    @TableField(exist=false)
    private String floorname;

    /**
     * 单元id
     */
    private Integer useheatunitfloorunitId;

    /**
     * 单元
     */
    @TableField(exist=false)
    private String floorunitname;

    /**
     * 热表编号
     */
    private String heatmeterno;

    /**
     * 小区编号
     */
    private String unitno;

    /**
     * 楼层
     */
    private String floorno;

    /**
     * 房号
     */
    private String romno;

    /**
     * 住户
     */
    private String housemaster;

    /**
     * 联系电话
     */
    private String housemastertel;

    /**
     * 户型
     */
    private String housetypeId;

    /**
     * 建筑面积
     */
    private Double builtarea;


    /**
     * 平面图
     */
    private String housepic;


    /**
     * 采暖类型
     */
    private Integer heatingtype;
    /**
     *是否安装热表
     */
    private Integer isinstallationheatmeter;
    /**
     *备注
     */
    private String other;

}

