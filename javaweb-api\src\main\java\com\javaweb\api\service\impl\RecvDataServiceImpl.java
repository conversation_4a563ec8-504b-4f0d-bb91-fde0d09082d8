package com.javaweb.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.javaweb.api.entity.AlarmConfig;
import com.javaweb.api.entity.AlarmRules;
import com.javaweb.api.entity.RunRules;
import com.javaweb.api.service.IRecvDataService;
import com.javaweb.api.utils.ProjectUtils;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.io.IOException;
import java.sql.*;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;

/**
 * <p>
 * 接收数据 服务实现类
 * </p>
 *
 *
 * @since 2023-1-10
 */

@Service
public class RecvDataServiceImpl implements IRecvDataService {

    @Autowired
    DataSource dataSource;

    @Autowired
    RedisUtils redisUtils;

    /*
     *
     * 接收换热站数据
     * */
    @Async("threadPoolTaskExecutor")
    @Override
    public JsonResult getRecvHesdata(String data, HttpServletRequest request) {
        String hescode="";
        try (Connection connection = dataSource.getConnection())
        {
            JSONObject recvdata = JSONObject.parseObject(data);
            hescode = recvdata.getString("hescode");
            JSONArray data_arr = recvdata.getJSONArray("data");
            int dataLen = Integer.parseInt(recvdata.getString("datalen"));
            int runmodel = Integer.parseInt(recvdata.getString("runmodel"));

            // 获取当前时间
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            String collectdt = sdf.format(System.currentTimeMillis());
            recvdata.put("collectdt", collectdt);

            //将接收到的数据存放到redis
            String hashname="FD:HesDatalist";
            // 将JSONObject转换为字符串
            String jsonString = recvdata.toJSONString();
            redisUtils.hset(hashname,hescode,jsonString);

            //验证是否告警
            //获取告警规则
//            List<AlarmConfig> alarmConfigLst=getAlarmConfigByRedis(hescode);
//            // 验证是否告警
//            for (AlarmConfig config : alarmConfigLst)
//            {
//                // 获取运行规则
//                RunRules runRule = config.getRunRuleTemplate();
//                // 检查是否在运行规则的时间段内
//                if (isInRunRuleTime(runRule))
//                {
//                    // 获取告警规则模板列表
//                    List<AlarmRules> alarmRuleTemplates = config.getAlarmRuleTemplates();
//                    // 检查告警条件
//                    for (AlarmRules rule : alarmRuleTemplates)
//                    {
//                        if (isAlarmConditionMet(rule, data_arr))
//                        {
//                            // 满足告警条件，插入告警记录
//                            insertAlarmRecord(connection, Integer.parseInt(hescode),rule);
//                            break;
//                        }
//                    }
//                }
//            }

            // 构建插入语句

            String insertSql = buildInsertSql(hescode, data_arr, dataLen);

            // 检查分区是否存在
            if (!checkPartitionExists(connection, hescode, year)) {
                addPartition(connection, hescode, year);
            }
            //更新运行模式
            updateHesRunMode(connection, hescode, runmodel);  // 修改此处，传递现有的连接
            // 执行插入语句
            try (PreparedStatement psInsert = connection.prepareStatement(insertSql)) {
                psInsert.setString(1, collectdt);
                for (int i = 0; i < dataLen; i++) {
                    JSONObject dataIndex = data_arr.getJSONObject(i);
                    psInsert.setString(i + 2, dataIndex.getString("value")); // 第一个位置是CollectDt
                }
                int rNum = psInsert.executeUpdate();
                return JsonResult.success("OK");
            }

        } catch (Exception ex) {
            System.out.println("换热站{"+hescode+"}数据异常报错XXXXXXXX！错误信息: " + ex.getMessage());
            ex.printStackTrace();  // 增加详细的堆栈跟踪
            return JsonResult.error("ERROR");
        }
    }



    //检查分区是否存在
    private boolean checkPartitionExists(Connection connection, String hescode, int year) throws SQLException {
        String partitionName = "t_hes_data_" + hescode + "_" + year;
        String checkPartitionSql = "SELECT * FROM INFORMATION_SCHEMA.PARTITIONS WHERE TABLE_NAME = ? AND PARTITION_NAME = ?";
        try (PreparedStatement psCheckPartition = connection.prepareStatement(checkPartitionSql)) {
            psCheckPartition.setString(1, "t_hes_data_" + hescode);
            psCheckPartition.setString(2, partitionName);
            try (ResultSet rsCheckPartition = psCheckPartition.executeQuery()) {
                return rsCheckPartition.next();
            }
        }
    }


    //添加分区
    private void addPartition(Connection connection, String hescode, int year) throws SQLException {
        String partitionName = "t_hes_data_" + hescode + "_" + year;
        String createPartitionSql = "ALTER TABLE t_hes_data_" + hescode + " ADD PARTITION (PARTITION " + partitionName + " VALUES LESS THAN (" + (year + 1) + "))";
        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createPartitionSql);
        }
    }

    private String buildInsertSql(String hescode, JSONArray data_arr, int dataLen) {
        StringBuilder sqlField = new StringBuilder("INSERT INTO t_hes_data_").append(hescode).append(" (CollectDt, ");
        StringBuilder sqlValue = new StringBuilder("VALUES (?, ");

        for (int i = 0; i < dataLen; i++) {
            JSONObject dataIndex = data_arr.getJSONObject(i);
            if (i < dataLen - 1) {
                sqlField.append("`").append(dataIndex.getString("field")).append("`, ");
                sqlValue.append("?, ");
            } else {
                sqlField.append("`").append(dataIndex.getString("field")).append("`) ");
                sqlValue.append("?)");
            }
        }
        String insertSql = sqlField.toString() + sqlValue.toString();
        //System.out.println("Generated SQL: " + insertSql); // 打印生成的 SQL 语句
        return insertSql;
    }


    //更新数据库的运行模式  自动=手动
    private void updateHesRunMode(Connection connection,String hescode, int runMode) {
        String sql = "UPDATE t_hes SET runMode=? WHERE hescode=?";
        try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
            // 设置参数
            pStatement.setInt(1, runMode);
            pStatement.setString(2, hescode);
            pStatement.executeUpdate();
        } catch (Exception e) {
            System.err.println("Update hescode=" + hescode + " error: " + e.getMessage());
        }
    }
    /*
    *
    * 接收气象站数据
    * */
    @Override
    public JsonResult getRecvWsdata(String data, HttpServletRequest request)
    {
        String Rst="";
        String temperature="0";
        String humidity="0";
        String speed="0";
        String solar="0";
        try {
            // 解析 JSON 数据
            JSONObject recvdata = JSONObject.parseObject(data);

            // 获取并检查每个字段，如果为空字符串则设置默认值
            if (!recvdata.getString("T").isEmpty()) {
                temperature = recvdata.getString("T");
            }
            if (!recvdata.getString("H").isEmpty()) {
                humidity = recvdata.getString("H");
            }
            if (!recvdata.getString("S").isEmpty()) {
                speed = recvdata.getString("S");
            }
            if (!recvdata.getString("R").isEmpty()) {
                solar = recvdata.getString("R");
            }

            try (Connection conn = dataSource.getConnection()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                String collectdt = sdf.format(System.currentTimeMillis());
                String tablename = "T_WeatherStationData";
                // 插入数据
                String sql = "INSERT INTO " + tablename + " (ws, CollectDt, T, H, S, R) VALUES (?, ?, ?, ?, ?, ?)";
                try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                    stmt.setString(1, "华能沣东供热站内气象站");
                    stmt.setString(2, collectdt);
                    stmt.setString(3, temperature);
                    stmt.setString(4, humidity);
                    stmt.setString(5, speed);
                    stmt.setString(6, solar);

                    int num = stmt.executeUpdate();
                    if (num > 0) {
                        Rst = "OK";
                    } else {
                        Rst = "ERROR";
                    }
                }
             }
            } catch (SQLException ex) {
                ex.printStackTrace();
                return JsonResult.error("数据库操作错误: 数据=" +data+" 报错="+ex.getMessage());
            } catch (Exception e) {
                e.printStackTrace();
                return JsonResult.error("解析数据报错： 数据=" +data+" 报错="+ e.getMessage());
            }
        return JsonResult.success(Rst);
    }



    /*
    *
    * 接收室内采集温度入库
    *
    * */

    @Override
    public JsonResult getRecvIndoorTdata(String data, HttpServletRequest request)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        JSONObject recvdata=JSONObject.parseObject(data);
        //小区编号
        String heatunitno = recvdata.getString("heatunitno");
        //热表编号
        String heatMeterNo = recvdata.getString("heatMeterNo");
        //房号
        String romNo = recvdata.getString("romNo");
        String F_S_T1 = recvdata.getString("F_S_T1");
        String F_B_T1 = recvdata.getString("F_B_T1");
        String F_B_V = recvdata.getString("F_B_V");
        String F_S_H = recvdata.getString("F_S_H");
        String F_S_F = recvdata.getString("F_S_F");
//        JSONObject recvdata = data.getJSONObject("jsondata");
        String key=recvdata.getString("md5Key");
        int    dataLen=Integer.parseInt(recvdata.getString("dataLen"));
        int    rate=recvdata.getInteger("rate");
        String  md5Key=getVerify(dataLen,rate,heatunitno);
        //字符串比对
        if(key.equals(md5Key))
        {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String collectdt = sdf.format(System.currentTimeMillis());

                String tablename = "t_indoort_data_"+heatunitno;
                connection = dataSource.getConnection();
                String sql = "insert into " + tablename + " (collectDt,heatMeterNo,romNo,F_S_T1,F_B_T1,F_B_V,F_S_H,F_S_F) values(" + collectdt + "','"
                        + heatMeterNo + "','" + romNo + "','" + F_S_T1 + "','" + F_B_T1 + "','" + F_B_V + "','" + F_S_H + "','" + F_S_F + "')";
                rSet = connection.getMetaData().getTables(null, null, tablename, null);
                if (rSet.next()) {
                    pStatement = connection.prepareStatement(sql);
                    int num = pStatement.executeUpdate();
                    Rst = "OK";
                } else {
                    Rst = "Database not exist";
                }
                connection.close();

            } catch (SQLException ex) {
                ex.printStackTrace();
                return JsonResult.error("ERROR");
            }
        }
        return JsonResult.success(Rst);
    }

    /*
    *
    *  接收换热站参数
    * */
    @Override
    public String getHesParam(Integer hesCode) {
        String strReturn = "";
        try (Connection connection = dataSource.getConnection()) {
            // 使用 try-with-resources 语句来自动关闭资源
            try (PreparedStatement calcModeStmt = connection.prepareStatement(
                    "SELECT calc_mode, sst_field_index FROM t_hes WHERE hescode = ?")) {
                calcModeStmt.setInt(1, hesCode);
                try (ResultSet rs = calcModeStmt.executeQuery()) {
                    if (rs.next()) {
                        int calcModel = rs.getInt("calc_mode");
                        int fieldIndex = rs.getInt("sst_field_index");
                        double area = getLatestHeara(connection, hesCode);
                        Map<String, Object> weatherData = getTw(connection);
                        double tw = (double) weatherData.getOrDefault("tw", 5.0);
                        double tn = getTn(connection, hesCode);
                        Map<String, Object> sstResult = getSst(connection, hesCode);
                        Map<String, Object> autoResult= getAutov(connection, hesCode);
                        double G = getG(connection, hesCode);
                        // 构建返回字符串
                        strReturn = buildResponseString(tw, tn, area, sstResult, autoResult,G, calcModel, fieldIndex, connection, hesCode,weatherData);
                    }
                }
            }
        } catch (SQLException e) {
            // 记录异常信息到日志
            strReturn = "{'rst':'0'}";
        }
        return strReturn;
    }


    /*
     *
     *  接收换热站前端程序版本号
     * */
    @Override
    public String getHesVersion() {
        String strReturn = "";
        try (Connection connection = dataSource.getConnection()) {
            // 使用 try-with-resources 语句来自动关闭资源
            try (PreparedStatement calcModeStmt = connection.prepareStatement(
                    "SELECT station_versions FROM tb_system_param WHERE id=1")) {

                try (ResultSet rs = calcModeStmt.executeQuery()) {
                    if (rs.next()) {
                        String versions = rs.getString("station_versions");
                        strReturn = "{'version':"+versions+"}";
                       }

                }
            }
        } catch (SQLException e) {
            // 记录异常信息到日志
            //log.error("Error while fetching HES parameters", e);
            strReturn = "{'version':'1'}";
        }
        return strReturn;
    }


    private double getLatestHeara(Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT heara FROM t_hes_year_Harea WHERE hescode = ? ORDER BY id DESC LIMIT 1";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble("heara");
                }
            }
        }
        return 0.5; // 默认值
    }

    private Map<String, Object> getTw(Connection conn) throws SQLException {
        String sql = "SELECT tw FROM t_hs";
        Map<String, Object> weatherData = new HashMap<>();

        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next() && !"NO".equals(rs.getString("tw"))) {
                double tw = Math.min(rs.getDouble("tw"), 10); // 确保不超过10
                weatherData.put("tw", tw);
                return weatherData;
            }
        }
        // 如果没有有效的 tw，则从天气站获取当前气温
        return getCurrentTemperature(conn);
    }

    private Map<String, Object> getCurrentTemperature(Connection conn) throws SQLException {
        String sql = "SELECT T, H, R, S, CollectDT FROM T_WeatherStationData ORDER BY id DESC LIMIT 1";
        Map<String, Object> weatherData = new HashMap<>();

        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                double T = rs.getDouble("T");
                String H = rs.getString("H");
                String R = rs.getString("R");
                String S = rs.getString("S");
                String dt = rs.getString("CollectDT");

                weatherData.put("tw", T);
                weatherData.put("T", T);
                weatherData.put("H", H);
                weatherData.put("R", R);
                weatherData.put("S", S);
                weatherData.put("dt", dt);
            }
        }
        return weatherData;
    }

    // 其他get方法类似...
    private double getTn(Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT tn1 FROM T_Hes_Rules WHERE hescode = ? AND dt = ? LIMIT 1";
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
        String strYmdhms = now.format(formatter);

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            stmt.setString(2, strYmdhms);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble("tn1");
                }
            }
        }
        return 20.0; // 默认值
    }

    private Map<String, Object> getSst(Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT sst, period, step FROM T_Hes_CalcParam3 WHERE hescode = ? ORDER BY id DESC LIMIT 1";
        Map<String, Object> resultMap = new HashMap<>();

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    resultMap.put("sst", rs.getString("sst"));
                    resultMap.put("period", rs.getString("period"));
                    resultMap.put("step", rs.getString("step"));
                    return resultMap;
                }
            }
        }
        // 设置默认值
        resultMap.put("sst", 60.0);
        resultMap.put("period", 60);
        resultMap.put("step", 1);
        return resultMap;
    }


    //获取断网规则
    private Map<String, Object> getAutov(Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT is_auto_v, is_control_f, is_control_v, frequency, opening, begin_tm, end_tm FROM t_auto_v WHERE hescode = ? ORDER BY id DESC LIMIT 1";
        Map<String, Object> resultMap = new HashMap<>();

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    resultMap.put("isAutoV", rs.getInt("is_auto_v"));
                    resultMap.put("isControlF", rs.getInt("is_control_f"));
                    resultMap.put("isControlV", rs.getInt("is_control_v"));
                    resultMap.put("frequency", rs.getString("frequency"));
                    resultMap.put("opening", rs.getString("opening"));
                    resultMap.put("beginTm", rs.getString("begin_tm"));
                    resultMap.put("endTm", rs.getString("end_tm"));

                    return resultMap;
                }
            }
        }
        // 设置默认值
        resultMap.put("isAutoV", 0);
        resultMap.put("isControlF", 0);
        resultMap.put("isControlV", 0);
        resultMap.put("frequency","30");
        resultMap.put("opening", "50");
        resultMap.put("beginTm", "00:00:00");
        resultMap.put("endTm", "23:59:59");
        return resultMap;
    }


    private double getG(Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT G FROM T_Hes_CalcParam4 WHERE hescode = ? ORDER BY id DESC LIMIT 1";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble("G");
                }
            }
        }
        return 0.0; // 默认值
    }

    private String buildResponseString(double tw, double tn, double area, Map<String, Object> sstResult,
                                       Map<String, Object> autoResult,double G,
                                       int calcModel, int fieldIndex, Connection conn, Integer hesCode,
                                       Map<String, Object> weatherData) throws SQLException {
        // 构建JSON字符串
        StringBuilder sb = new StringBuilder();
        sb.append("{'rst':'1','tw':'").append(tw).append("','tn':'").append(tn)
                .append("','area':'").append(area).append("','sst':'").append(sstResult.get("sst"))
                .append("','period':'").append(sstResult.get("period"))
                .append("','step':'").append(sstResult.get("step"))
                .append("','is_auto_v':'").append(autoResult.get("isAutoV"))
                .append("','is_control_f':'").append(autoResult.get("isControlF"))
                .append("','is_control_v':'").append(autoResult.get("isControlV"))
                .append("','frequency':'").append(autoResult.get("frequency"))
                .append("','opening':'").append(autoResult.get("opening"))
                .append("','begin_tm':'").append(autoResult.get("beginTm"))
                .append("','end_tm':'").append(autoResult.get("endTm"))
                .append("','calc_model':'").append(calcModel).append("','field_index':'")
                .append(fieldIndex).append("','G':'").append(G)
                .append("','T':'").append(weatherData.getOrDefault("T", "")).append("','H':'")
                .append(weatherData.getOrDefault("H", "")).append("','R':'")
                .append(weatherData.getOrDefault("R", "")).append("','S':'")
                .append(weatherData.getOrDefault("S", "")).append("','dt':'")
                .append(weatherData.getOrDefault("dt", "")).append("','data':[");

        // 添加分时段二次供水温度
        appendControlTemperatures(sb, conn, hesCode);

        // 去除最后一个逗号并闭合JSON
        if (sb.charAt(sb.length() - 1) == ',') {
            sb.setLength(sb.length() - 1);
        }
        sb.append("]}");

        return sb.toString();
    }

    private void appendControlTemperatures(StringBuilder sb, Connection conn, Integer hesCode) throws SQLException {
        String sql = "SELECT run_h, control_t FROM t_hes_rule WHERE hescode = ? ORDER BY run_h ASC";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, hesCode);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    sb.append("{'run_h':'").append(rs.getString("run_h")).append("','sst':'")
                            .append(rs.getString("control_t")).append("'},");
                }
            }
        }
    }


//    @Override
//    public String getHesParam(Integer hesCode) {
//        PreparedStatement pStatement = null;
//        ResultSet rSet = null;
//        int calcModel = 1;
//        int fieldIndex = 1;
//        double area = 0.5;
//        double tw = 5.0;
//        double tn = 18.0;
//        double sst = 60.0;
//        double G = 0.0;
//        String T = "", H = "", R = "", S = "", dt = "";
//        String strReturn="";
//        try (Connection connection = dataSource.getConnection())
//        {
//            // 获取计算模式
//            String sql = "SELECT calc_mode, sst_field_index FROM t_hes WHERE hescode = ?";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            rSet = pStatement.executeQuery();
//            if (rSet.next()) {
//                calcModel = rSet.getInt("calc_mode");
//                fieldIndex = rSet.getInt("sst_field_index");
//            }
//
//            // 获取供暖面积
//            sql = "SELECT heara FROM t_hes_year_Harea WHERE hescode = ? ORDER BY id DESC limit 1";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            rSet = pStatement.executeQuery();
//            if (rSet.next()) {
//                area = rSet.getDouble("heara");
//            }
//
//            // 获取气温参数
//            sql = "SELECT tw FROM t_hs";
//            pStatement = connection.prepareStatement(sql);
//            rSet = pStatement.executeQuery();
//            if (rSet.next())
//            {
//                if (!"NO".equals(rSet.getString("tw"))) {
//                    tw = rSet.getDouble("tw");
//                } else {
//                    // 获取当前气温
//                    sql = "SELECT T, H, R, S, CollectDT FROM T_WeatherStationData ORDER BY id DESC limit 1";
//                    pStatement = connection.prepareStatement(sql);
//                    rSet = pStatement.executeQuery();
//                    if (rSet.next()) {
//                        tw = rSet.getDouble("T");
//                        T = rSet.getString("T");
//                        H = rSet.getString("H");
//                        R = rSet.getString("R");
//                        S = rSet.getString("S");
//                        dt = rSet.getString("CollectDT");
//                    }
//                }
//            }
//            if (tw > 10) {
//                tw = 10;
//            }
//
//            // 获取阶段室温
//            LocalDateTime now = LocalDateTime.now();
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
//            String strYmdhms = now.format(formatter);
//            sql = "SELECT  tn1 FROM T_Hes_Rules WHERE hescode = ? AND dt = ? limit 1";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            pStatement.setString(2, strYmdhms);
//            rSet = pStatement.executeQuery();
//            if(rSet !=null)
//            {
//
//                while (rSet.next())
//                {
//                    tn = rSet.getDouble("tn1");
//                }
//            }else
//            {
//                tn = 20.0;
//            }
//            // 获取二次供水温度设定值
//            sql = "SELECT sst FROM T_Hes_CalcParam3 WHERE hescode = ? ORDER BY id DESC";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            rSet = pStatement.executeQuery();
//            if (rSet.next()) {
//                sst = rSet.getDouble("sst");
//            }
//
//            // 获取计算分配流量
//            sql = "SELECT G FROM T_Hes_CalcParam4 WHERE hescode = ? ORDER BY id DESC limit 1";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            rSet = pStatement.executeQuery();
//            if (rSet.next()) {
//                G = rSet.getDouble("G");
//            } else {
//                G = 0.0;
//            }
//
//            strReturn="{'rst':'1','tw':'"+tw+"','T':'"+T+"','H':'"+H+"','R':'"+R+"','S':'"+S+"','dt':'"+dt+"','tn':'"+tn+"','area':'"+area+"'" +
//                    ",'sst':'"+sst+"','calc_model':'"+calcModel+"','field_index':'"+fieldIndex+"','G':'"+G+"','data':[";
//
//            // 获取分时段二次供水温度
//            sql = "SELECT run_h, control_t FROM t_hes_rule WHERE hescode = ? ORDER BY run_h ASC";
//            pStatement = connection.prepareStatement(sql);
//            pStatement.setInt(1, hesCode);
//            rSet = pStatement.executeQuery();
//            if(rSet !=null)
//            {
//                String str="";
//                while (rSet.next())
//                {
//                    String  run_h=rSet.getString("run_h");
//                    String  control_t=rSet.getString("control_t");
//                    str=str+"{'run_h':'"+run_h+"','sst':'"+control_t+"'},";
//                }
//                strReturn=strReturn+str+"]}";
//            }else
//            {
//                strReturn="{'rst':'0'}";
//            }
//            strReturn = strReturn.replace(",]", "]");
//
//        } catch (SQLException e) {
//            e.printStackTrace();
//            strReturn="{'rst':'0'}";
//        } finally {
//
//        }
//        return strReturn;
//    }

    /*
     *
     *  接收换热站参数
     * */
    @Override
    public String getHesState(Integer hesCode) {
        String currentdt = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try (Connection connection = dataSource.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(
                     "SELECT * FROM tb_hes_state WHERE code = ? ORDER BY id DESC LIMIT 1")) {

            pStatement.setInt(1, hesCode);
            try (ResultSet rSet = pStatement.executeQuery()) {
                if (rSet.next()) {
                    String dt = rSet.getString("dt");
                    long diff = LocalDateTime.parse(currentdt, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                            .toEpochSecond(ZoneOffset.UTC) -
                            LocalDateTime.parse(dt, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                    .toEpochSecond(ZoneOffset.UTC);

                    if (diff > 180) {
                        return "{\"rst\":\"0\"}";
                    } else {
                        // 获取所有需要的字段
                        StringBuilder jsonBuilder = new StringBuilder("{\"rst\":\"1\",");
                        appendFieldToJson(jsonBuilder, "F_S_H", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_F", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_T1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_T1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_STATE1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_FREQUENCY1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_CURRENT1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_REMOTE1", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_STATE2", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_FREQUENCY2", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_CURRENT2", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_REMOTE2", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_STATE3", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_FREQUENCY3", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_CURRENT3", rSet);
                        appendFieldToJson(jsonBuilder, "F_B_WP_REMOTE3", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_H1", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_F1", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_H2", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_F2", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_H3", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_F3", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_H4", rSet);
                        appendFieldToJson(jsonBuilder, "F_S_F4", rSet);

                        // 移除最后一个逗号并闭合 JSON
                        jsonBuilder.setLength(jsonBuilder.length() - 1);
                        jsonBuilder.append("}");

                        return jsonBuilder.toString();
                    }
                } else {
                    return "{\"rst\":\"0\"}";
                }
            }
        } catch (SQLException e) {
            System.out.println("Error while fetching HES state:"+e);
            return "{\"rst\":\"0\"}";
        }
    }

    private void appendFieldToJson(StringBuilder jsonBuilder, String fieldName, ResultSet rSet) throws SQLException {
        jsonBuilder.append("\"").append(fieldName).append("\":\"").append(rSet.getString(fieldName)).append("\",");
    }
    /*
    *
    * 最早之前实现的，带有密钥的入库
    * */
    @Override
    public JsonResult getRecvdata(JSONObject data, HttpServletRequest request)
    {
        Connection connection = null;
        Statement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        int rNum=0;
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        LinkedHashMap<Object, Object> objectObjectHashMap1 = new LinkedHashMap<>();

        System.out.println("接收到的数据："+data);
        JSONObject recvdata = data.getJSONObject("jsondata");
        String hescode = recvdata.getString("stationNo");
        String key=recvdata.getString("md5Key");
        int    dataLen=Integer.parseInt(recvdata.getString("dataLen"));
        int    rate=recvdata.getInteger("rate");
        JSONArray data_arr = recvdata.getJSONArray("data");
        String  md5Key=getVerify(dataLen,rate,hescode);

        //字符串比对
        if(key.equals(md5Key))
        {
            try {
                //获取本年份
                Calendar calendar=Calendar.getInstance();
                int year=calendar.get(Calendar.YEAR);
                SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String collectdt=sdf.format(System.currentTimeMillis());
                String tablename="t_hes_data_"+hescode;
                connection = dataSource.getConnection();
                String sqlfield="insert into "+tablename+" (CollectDt," ;
                String sqlvalue=" values('"+collectdt+"'," ;
                String sql="";
                for(int i = 0 ; i<dataLen ; i++)
                {
                    JSONObject dataIndex = data_arr.getJSONObject(i);
                    if(i<dataLen-1)
                    {
                        sqlfield=sqlfield +"`"+dataIndex.getString("id")+"`,";
                        sqlvalue=sqlvalue +"'"+ dataIndex.getString("value")+"',";
                    }else
                    {
                        sqlfield=sqlfield +"`"+ dataIndex.getString("id")+"`)";
                        sqlvalue=sqlvalue +"'"+dataIndex.getString("value")+"')";
                    }
                }
                sql=sqlfield+sqlvalue;
                rSet=connection.getMetaData().getTables(null,null,tablename,null);
                if(rSet.next())
                {
                    pStatement = connection.createStatement();
                    rNum = pStatement.executeUpdate(sql);
                    Rst="插入成功";
                }else
                {
                    Rst="数据表不存在";
                }
                connection.close();

            }catch (SQLException ex) {

            }
        }

        return JsonResult.success(Rst);
    }

    /*
     *
     * 按照规则获取秘钥 tbkj2015+ 时*分*长度*因子 +插入站号到因子指定的位置
     * param  nCount长度   nV 因子
     *
     */
    public String getVerify(Integer nCount,Integer nV,String nStationNo)
    {
        String  md5Key=null;
        Calendar calendar=Calendar.getInstance();
        int nhour=calendar.get(Calendar.HOUR_OF_DAY);
        int nMinutes=calendar.get(Calendar.MINUTE);
         nhour=20;
         nMinutes=30;
        int nCalc=nhour*nMinutes*nV*nCount;
        String strCalc="tbkj2015"+String.valueOf(nCalc);
        //MD5加密  站号插入到因子指定的位置
        String strEnd=StringUtils.substring(strCalc,nV);
        String strStart=StringUtils.substring(strCalc,0,nV);
        String key=strStart+nStationNo+strEnd;
        //MD5加密
        md5Key = DigestUtils.md5Hex(key);
        return md5Key;
    }
}
