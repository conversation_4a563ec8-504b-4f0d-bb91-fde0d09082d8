
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_alarm")
public class TAlarm extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer hesId;

    /**
     * 
     */
    private String alarmField;

    /**
     * 
     */
    private String alarmValue;

    /**
     * 
     */
    private String alarmMaxval;

    /**
     * 
     */
    private String alarmMinval;

    /**
     * 
     */
    private Integer isalarm;

    /**
     * 
     */
    private Integer ishandle;

    /**
     * 
     */
    private Integer dictId;

    /**
     * 
     */
    private Integer alarmType;

    /**
     * 
     */
    private Integer alarmLevel;

    /**
     * 
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmDt;

    /**
     * 
     */
    private String unit;

}