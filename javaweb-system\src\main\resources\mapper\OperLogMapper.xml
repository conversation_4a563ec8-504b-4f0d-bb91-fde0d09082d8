<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.OperLogMapper">

    <!-- 创建系统操作日志 -->
    <insert id="insertOperlog" parameterType="com.javaweb.system.entity.OperLog">
		insert into tb_oper_log(title, log_type, oper_method, request_method, oper_type, oper_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, note, create_time)
        values (#{title}, #{logType}, #{operMethod}, #{requestMethod}, #{operType}, #{operName}, #{operUrl}, #{operIp}, #{operLocation}, #{operParam}, #{jsonResult}, #{status}, #{note}, sysdate())
	</insert>

</mapper>
