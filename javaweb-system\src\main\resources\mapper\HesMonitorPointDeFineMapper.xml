<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HesMonitorPointDeFineMapper">

    <select id="selectDictDataByHescode" resultType="com.javaweb.system.entity.HesMonitorPointDeFine">
        select id,MPDesc,MPFieldDefine,HesCode,IsControl,IsUsed,unit,field_alisa from t_hesmonitorpointdefine
        where  HesCode = #{hescode} order by Mpdesc asc
    </select>

    <select id="getDictDataByHescode" resultType="com.javaweb.system.dto.MonitorDto">
        select id, MPDesc,MPFieldDefine,HesCode,field_alisa from t_hesmonitorpointdefine
        where  HesCode = #{hescode} and iswebshow=1 order by Mpdesc asc
    </select>

    <select id="getMonitorPointDeFineByHescode" resultType="com.javaweb.system.dto.MonitorPointDefineDto">
        select * from t_hesmonitorpointdefine where id in
        <foreach collection="Ids" item="item" index="index" open="(" separator="," close=")" >
            (#{item})
        </foreach>  and HesCode = #{hescode}
        order by Mpdesc asc
    </select>

    <select id="getDictDataAllList" resultType="com.javaweb.system.dto.MonitorDto">
        select id,MPDesc,MPFieldDefine,HesCode,field_alisa from t_hesmonitorpointdefine
        where iswebshow=1 and  IsUsed = 1 order by Mpdesc asc
    </select>

    <delete id="delFieldDefine">
        delete from t_hes_monitorpoint where hescode=#{hescode};
    </delete>

    <insert id="insertFieldDefineByhescode">
     insert into t_hes_monitorpoint (hescode,MPFieldDefine) values(#{hescode},#{MPFieldDefine})
    </insert>
    <!--    <insert id="insertHesMon"  parameterType="com.javaweb.system.entity.HesMpDictionary">-->

<!--        insert  into  (-->
<!--            <if test="id !=null ">id,</if>-->
<!--            <if test="CollectDT !=null and CollectDT !=''">collectdt,</if>-->
<!--            <if test="wrAddress !=null and wrAddress! =''">wraddress,</if>-->
<!--            <if test="mpdesc !=null and mpdesc!='' ">mpdesc,</if>-->
<!--            <if test="mpfielddefine !=null and mpfielddefine!=''">mpfielddefine,</if>-->
<!--            <if test="minvalue !=null">`minvalue`,</if>-->
<!--            <if test="maxvaluee !=null ">maxvaluee,</if>-->
<!--            <if test="unit!=null and unit!=''">unit,</if>-->
<!--        )-->
<!--        value(-->
<!--            <if test="id !=null ">#{id}</if>-->
<!--            <if test="CollectDT !=null and CollectDT !=''">#{CollectDT}</if>-->
<!--            <if test="wrAddress !=null and wrAddress! =''">#{wrAddress}</if>-->
<!--            <if test="mpdesc !=null and mpdesc!='' ">#{mpdesc}</if>-->
<!--            <if test="mpfielddefine !=null and mpfielddefine!=''">#{mpfielddefine}</if>-->
<!--            <if test="minvalue !=null">#{minvalue}</if>-->
<!--            <if test="maxvaluee !=null ">#{maxvaluee}</if>-->
<!--            <if test="unit!=null and unit!=''">#{unit}</if>-->
<!--            )-->
<!--        CREATE TABLE  IF NOT EXISTS  `t_hes_#{hescode}_`(-->
<!--            `id` Int NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT 'id',-->
<!--            CollectDT DATETIME DEFAULT NULL ,-->
<!--            wrAddress  VARCHAR(140),-->
<!--            mpdesc VARCHAR(140),-->
<!--            mpfielddefine VARCHAR(140),-->
<!--            `minvalue`  int ,-->
<!--            `maxvaluee` int ,-->
<!--            `unit` VARCHAR(140),-->
<!--        )-->
<!--    </insert>-->
<!--    <select id="getList" parameterType="com.javaweb.system.entity.HesMpDictionary">-->
<!--     select mpdesc , (CASE WHEN mpdesc=#{mpdesc} then '已存在')-->
<!--      from t_hesmonitorpointdefine-->
<!--    </select>-->
</mapper>
