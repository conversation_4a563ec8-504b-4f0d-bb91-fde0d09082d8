
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.system.entity.Item;

import java.util.List;

/**
 * <p>
 * 站点配置表 服务类
 * </p>
 *
 * 
 * @since 2020-11-07
 */
public interface IItemService extends IBaseService<Item> {

    /**
     * 获取站点列表
     *
     * @return
     */
    List<Item> getItemList();

}
