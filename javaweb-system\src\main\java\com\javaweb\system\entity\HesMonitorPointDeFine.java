package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 *
 * </p>测点配置
 *
 * @Date: 2022/12/16 15:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_hesmonitorpointdefine")
public class HesMonitorPointDeFine implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *是否使用
     */
    private Integer isused;

    /**
     * 监测站编号
     */
    private Integer hescode;

    /**
     * 换热站名称
     */
    @TableField(exist = false)
    private String  hesname;


    /**
     * 换热站名称
     */
    private String wrAddress;

    /**
     * 描述
     */
    private String Mpdesc;

    /**
     * 字段名
     */
    private String mpfielddefine;



    /**
     * 字段别名
     */
    private String fieldAlisa;


    /**
     * 字段名id
     */
    //private String mpfieldid;



    /**
     *变量组名称
     */
    //private String mpfieldgroup;


    /**
     * 最小量程
     */
    private String minvalue;

    /**
     * 最大量程
     */
//    private String maxvalue;

    /**
     * 是否控制
     */
    private Integer iscontrol;

    /**
     * 是否展示
     */
    private Integer iswebshow;

    /**
     * 是否报警
     */
    @TableField(exist = false)
    private Integer isbaojing;


    /**
     * 单位
     */
    private String unit;

}

