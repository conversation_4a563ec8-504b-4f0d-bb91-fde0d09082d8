package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.entity.HeatUnitFloorUnit;
import com.javaweb.system.mapper.HeatUnitFloorMapper;
import com.javaweb.system.mapper.HeatUnitFloorUnitMapper;
import com.javaweb.system.query.HeatUnitFloorQuery;
import com.javaweb.system.query.HeatUnitFloorUnitQuery;
import com.javaweb.system.service.IHeatUnitFloorService;
import com.javaweb.system.service.IHeatUnitFloorUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 单元管理
 * @Date: 2024/9/4 16:40
 */
@RestController
@RequestMapping("/heatunitfloorunit")
public class HeatUnitFloorUnitController extends BaseController {

    @Autowired
    private IHeatUnitFloorUnitService heatUnitFloorUnitService;

    @Autowired
    private HeatUnitFloorUnitMapper heatUnitFloorUnitMapper;

    /**
     * 获取查询列表
     * @param heatUnitFloorUnitQuery
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HeatUnitFloorUnitQuery heatUnitFloorUnitQuery) {
        return heatUnitFloorUnitService.getList(heatUnitFloorUnitQuery);
    }


    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "单元信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HeatUnitFloorUnit entity){

        return heatUnitFloorUnitService.edit(entity);
    }

    @Log(title = "单元信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HeatUnitFloorUnit entity){

        return heatUnitFloorUnitService.edit(entity);
    }

    @Log(title = "单元信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{heatUnitFloorIds}")
    public JsonResult delete(@PathVariable("heatUnitFloorIds") Integer[]HeatUnitFloorIds){

        return heatUnitFloorUnitService.deleteByIds(HeatUnitFloorIds);
    }

    @GetMapping("/getHeatUnitFloorUnitList")
    public JsonResult getHeatUnitFloorList(){
        return heatUnitFloorUnitService.getHeatUnitFloorUnitList();
    }



    //获得基本信息  id 名称
    @GetMapping("/getHeatUnitFloorUnitBaseinfo/{useheatunitid}/{useheatunitfloorid}")
    public JsonResult getHeatUnitFloorUnitBaseinfo(@PathVariable Integer useheatunitid,
                                               @PathVariable Integer useheatunitfloorid)
    {
        return JsonResult.success(heatUnitFloorUnitMapper.getHeatUnitFloorUnitBaseinfo(useheatunitid,useheatunitfloorid));
    }

}

