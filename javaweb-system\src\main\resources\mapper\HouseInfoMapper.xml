<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HouseInfoMapper">

   <!-- 通过关联id获得小区、楼宇、单元信息-->
  <select id="getHouseInfoByLinkId" resultType="com.javaweb.system.dto.HouseInfoDto">
      SELECT
          mt.id AS id,
          c.name AS name,
          b.floorname AS floorname,
          u.floorunitname AS floorunitname
      FROM
          t_houseinfo mt
              LEFT JOIN t_useheatunit c ON mt.useheatunit_id = c.id
              LEFT JOIN t_useheatunitfloor b ON mt.useheatunitfloor_id = b.id
              LEFT JOIN t_useheatunitfloorunit u ON mt.useheatunitfloorunit_id = u.id

  </select>

    <!-- 通过热表编号获取个数-->
    <select id="getCountByNo" resultType="Integer">
        SELECT count(id) as num  FROM t_houseinfo  where heatmeterno=#{heatmeterno}
         <if test="id !=null">
            and id !=#{id}
         </if>
    </select>
    <!-- 基础信息-->
    <select id="getHouseBaseinfo" resultType="com.javaweb.system.entity.HouseInfo">
        SELECT id, heatmeterno,romno FROM t_houseinfo where useheatunit_id=#{useheatunitId}
          and useheatunitfloor_id=#{useheatunitfloorId}
          and useheatunitfloorunit_id=#{useheatunitfloorunitId}  order by id asc;
    </select>

</mapper>
