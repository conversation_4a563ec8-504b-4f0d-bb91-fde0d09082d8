package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THes;
import com.javaweb.system.query.HesQuery;
import com.javaweb.system.service.THesService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *
 *  报表统计
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 14:46
 */
@RestController
@RequestMapping("reportStatistics")
public class ReportStatisticsController extends BaseController {
    @Autowired
    private  THesService tHesService;


    @GetMapping("/getHesEnergytjData/{startdt}/{enddt}")
    public JsonResult getHesEnergytjData(@PathVariable("startdt") String startdt,
                                         @PathVariable("enddt")   String enddt)
    {
        return tHesService.getHesEnergytjData(startdt,enddt);
    }

    //日每小时的能耗
    @GetMapping("/getHesEnergytjHourData/{startdt}")
    public JsonResult getHesEnergytjHourData(@PathVariable("startdt") String startdt)
    {
        return tHesService.getHesEnergytjHourData(startdt);
    }

}