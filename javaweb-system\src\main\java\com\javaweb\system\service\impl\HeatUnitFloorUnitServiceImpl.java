package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HeatUnitFloor;
import com.javaweb.system.entity.HeatUnitFloorUnit;
import com.javaweb.system.entity.HouseInfo;
import com.javaweb.system.mapper.HeatUnitFloorUnitMapper;
import com.javaweb.system.query.HeatUnitFloorUnitQuery;
import com.javaweb.system.service.IHeatUnitFloorService;
import com.javaweb.system.service.IHeatUnitFloorUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 楼宇信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HeatUnitFloorUnitServiceImpl extends ServiceImpl<HeatUnitFloorUnitMapper, HeatUnitFloorUnit> implements IHeatUnitFloorUnitService {

    @Autowired
    HeatUnitFloorUnitMapper heatUnitFloorUnitMapper;

    /**
     * 查询小区列表
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        HeatUnitFloorUnitQuery heatUnitFloorUnitQuery =(HeatUnitFloorUnitQuery) query;
        //查询条件
        QueryWrapper<HeatUnitFloorUnit> queryWrapper = new QueryWrapper<>();

        //单元名称floorunitname
        if(!StringUtils.isEmpty(heatUnitFloorUnitQuery.getFloorunitname())){
            queryWrapper.like("floorunitname",heatUnitFloorUnitQuery.getFloorunitname());
        }
         queryWrapper.orderByDesc("id");

         Integer npage=1;
         Integer nlimit=10;
         if(heatUnitFloorUnitQuery.getPage() !=null)
         {
             npage=heatUnitFloorUnitQuery.getPage();

        }
         if(heatUnitFloorUnitQuery.getLimit() !=null)
         {
          nlimit=heatUnitFloorUnitQuery.getLimit();
         }
        //查询分页数据
        IPage<HeatUnitFloorUnit> page = new Page<>(npage,nlimit);
        IPage<HeatUnitFloorUnit> pageData = heatUnitFloorUnitMapper.selectPage(page,queryWrapper);

        List<HouseInfoDto> houseInfoDtolst=heatUnitFloorUnitMapper.getUnitFloorUnitByLinkId();

        pageData.convert(x -> {
            HeatUnitFloorUnit heatUnitFloorUnitList = Convert.convert(HeatUnitFloorUnit.class, x);

            houseInfoDtolst.forEach(houseInfo->{
                if(heatUnitFloorUnitList.getId().equals(houseInfo.getId()))
                {
                    heatUnitFloorUnitList.setName(houseInfo.getName());
                    heatUnitFloorUnitList.setFloorname(houseInfo.getFloorname());
                }
            });
            return heatUnitFloorUnitList;
        });
        return  JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HeatUnitFloorUnit entity) {
        //通过验证单元信息是否存在
        Integer num=0;
        num= heatUnitFloorUnitMapper.getCountByNo(entity);
        if(num>0)
        {
            return JsonResult.error("单元已存在");
        }

            boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHeatUnitFloorUnitList() {
        QueryWrapper<HeatUnitFloorUnit> queryWrapper = new QueryWrapper<>();

        List<HeatUnitFloorUnit> list = list(queryWrapper);
        return JsonResult.success(list);
    }
}




