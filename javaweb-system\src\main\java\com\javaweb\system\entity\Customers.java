
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.javaweb.common.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 职级表
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_web_tousu")
public class Customers extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 添加人员
     */
    private int uid;

    /**
     *添加时间
     */
    private String regtime;

    /**
     * 投诉地址
     */
    private String tousuAddress;

    /**
     * 投诉内容
     */
    private String tousuContent;
    /**
     * 解决方案
     */
    private String tousuFangan;

}
