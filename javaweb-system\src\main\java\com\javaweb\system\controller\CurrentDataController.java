package com.javaweb.system.controller;

import com.javaweb.common.common.BaseController;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.query.CurrentDataQuery;
import com.javaweb.system.service.ICurrentDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 实时数据 前端控制器
 * </p>
 *
 *
 * @since 2023-02-07
 */

@RestController
@RequestMapping("/currentdata")
public class CurrentDataController extends BaseController {

    @Autowired
    private ICurrentDataService currentdataService;

    /**
     * 获取所有监测站的实时数据
     *
     * @return
     */
    @GetMapping("/getCurrentdataAll")
    public JsonResult getCurrentdataAll(CurrentDataQuery currentdataQuery) {
        return  currentdataService.getCurrentDataAll(currentdataQuery);
    }

}
