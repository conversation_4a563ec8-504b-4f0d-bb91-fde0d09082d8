package com.javaweb.system.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

//日能耗统计表
@Data
public class HesEnergyHourDto {
    /**
     * ID
     */
    private Integer id;

    /**
     * 监测站编号
     */
    private String hescode;

    /**
     * 时间
     */
    private String dt;

    /**
     * 面积
     */
    private double area;


    private String h0;

    private String h1;

    private String h2;

    private String h3;

    private String h4;

    private String h5;

    private String h6;

    private String h7;

    private String h8;

    private String h9;

    private String h10;

    private String h11;

    private String h12;

    private String h13;

    private String h14;

    private String h15;

    private String h16;

    private String h17;

    private String h18;

    private String h19;

    private String h20;

    private String h21;

    private String h22;

    private String h23;

    public String getHi(int i) {
        switch (i) {
            case 0: return h0;
            case 1: return h1;
            case 2: return h2;
            case 3: return h3;
            case 4: return h4;
            case 5: return h5;
            case 6: return h6;
            case 7: return h7;
            case 8: return h8;
            case 9: return h9;
            case 10: return h10;
            case 11: return h11;
            case 12: return h12;
            case 13: return h13;
            case 14: return h14;
            case 15: return h15;
            case 16: return h16;
            case 17: return h17;
            case 18: return h18;
            case 19: return h19;
            case 20: return h20;
            case 21: return h21;
            case 22: return h22;
            case 23: return h23;
            default: throw new IllegalArgumentException("Invalid hour index: " + i);
        }
    }

}
