package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.SystemParam;
import com.javaweb.system.entity.User;
import com.javaweb.system.query.SystemParamQuery;
import com.javaweb.system.service.ISystemParamService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 *
 * @since 2023-02-07
 */

@RestController
@RequestMapping("/systemparam")
public class SystemParamController extends BaseController {

    @Autowired
    private ISystemParamService systemparamService;

    /**
     * 添加用户
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "系统参数配置", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody SystemParam entity) {
        return systemparamService.edit(entity);
    }

    /**
     * 获取所有监测站的实时数据
     *
     * @return
     */
    @GetMapping("/getCurrentdataAll")
    public JsonResult getCurrentdataAll(SystemParamQuery systemparamQuery) {
        return  systemparamService.getCurrentDataAll(systemparamQuery);
    }


    @GetMapping("/getSystemParamInfo")
    public JsonResult getSystemParamInfo() {
        return  systemparamService.getSystemParamInfo();
    }


    @GetMapping("/info")
    public JsonResult info() {
        return  systemparamService.getSystemParamInfo();
    }


}
