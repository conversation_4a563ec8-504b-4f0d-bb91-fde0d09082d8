package com.javaweb.system.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.HouseInfoDto;
import com.javaweb.system.entity.HouseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  小区信息表Mapper 接口
 * </p>
 *
 * @Date: 2022/12/12 14:31
 */

public interface HouseInfoMapper extends BaseMapper<HouseInfo> {

    List<HouseInfoDto>  getHouseInfoByLinkId();

    Integer getCountByNo(HouseInfo houseInfo);

    List<HouseInfoDto> getHouseBaseinfo(Integer useheatunitId,
                                        Integer useheatunitfloorId,
                                        Integer useheatunitfloorunitId);


}

