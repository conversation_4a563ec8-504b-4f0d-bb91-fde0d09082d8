package com.javaweb.system.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaweb.system.dto.TimePeriod;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 *    项目中使用的功能函数
 * </p>
 *
 * @Date: 2023/1/3 16:33
 */
public class ProjectUtils {


    /*
    *
    * 判断是否为供热季的函数
    *
    * */
    public static boolean isHeatingSeason(LocalDate date) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        // 检查是否在11月15日至12月31日之间
        if (month == 11 && day >= 15 || month == 12) {
            return true;
        }

        // 检查是否在1月1日至3月15日之间
        if (month >= 1 && month <= 3) {
            if (month == 3) {
                // 如果是3月，需要检查是否在15日之前
                return day <= 15;
            }
            return true;
        }

        return false;
    }


    /**
     * 判断是否两个日期之间的函数
     *
     * @param date      需要判断的日期
     * @param startMonth 供热季开始月份
     * @param startDay    供热季开始日期
     * @param endMonth    供热季结束月份
     * @param endDay      供热季结束日期
     * @return 如果是供热季，返回true，否则返回false
     */
    public static boolean isDateBetween(LocalDate date, int startMonth, int startDay, int endMonth, int endDay) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        // 检查日期是否在供热季开始和结束日期之间（结束日期在开始日期之后）
        if (endMonth > startMonth || (endMonth == startMonth && endDay >= startDay)) {
            if ((month > startMonth || (month == startMonth && day >= startDay)) &&
                    (month < endMonth || (month == endMonth && day <= endDay))) {
                return true;
            }
        } else {
            // 检查日期是否在供热季开始和结束日期之间（结束日期在开始日期之前，可能跨越新年）
            if ((month > startMonth || (month == startMonth && day >= startDay)) ||
                    (month < endMonth || (month == endMonth && day <= endDay))) {
                return true;
            }
        }

        return false;
    }


    /**
     * 判断今天是否是参数中指定的星期之一
     *
     * @param weekdays 一个字符串，包含表示星期几的数字，例如 "135" 表示周一、周三、周五
     * @return 如果今天是参数中指定的星期之一，返回true，否则返回false
     */
    public static boolean isTodayOneOfTheWeekdays(String weekdays) {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 获取今天是星期几
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        // 将星期几转换为从1开始的数字，周一为1，周日为7
        int todayNumber = dayOfWeek.getValue() == 7 ? 1 : dayOfWeek.getValue() + 1;

        // 检查今天是否在参数中指定的星期之一
        return weekdays.contains(String.valueOf(todayNumber));
    }

    /*
    *
    * 判断当前时间是否在这个时间段内
    * [{"beginDate":"11-15","endDate":"03-15","begintm":"00:00:00","endtm":"23:59:59"}]  字符串的格式
    *
    * */
    public static boolean isCurrentTimeWithinPeriod(String periodJson) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<TimePeriod> periods = objectMapper.readValue(periodJson, new TypeReference<List<TimePeriod>>() {});

            LocalDateTime now = LocalDateTime.now();
            int currentYear = now.getYear();
            for (TimePeriod period : periods) {

                LocalDate beginDate = LocalDate.parse(currentYear + "-" + period.getBeginDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate endDate = LocalDate.parse(currentYear + "-" + period.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalTime beginTime = LocalTime.parse(period.getBegintm());
                LocalTime endTime = LocalTime.parse(period.getEndtm());
                if (endDate.isBefore(beginDate)) {
                    endDate = endDate.plusYears(1);
                }

                LocalDateTime periodStart = LocalDateTime.of(beginDate, beginTime);
                LocalDateTime periodEnd = LocalDateTime.of(endDate, endTime);

                if (now.isAfter(periodStart) && now.isBefore(periodEnd)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}

