package com.javaweb.system.security;

import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.JsonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 登录安全拦截器
 * 用于在Controller层进行安全验证
 */
@Component
public class LoginSecurityInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoginSecurityInterceptor.class);

    @Autowired
    private IpWhitelistValidator ipWhitelistValidator;

    @Autowired
    private SecurityAuditLogger securityAuditLogger;

    /**
     * 在请求处理之前进行调用
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @return true-继续处理，false-中断处理
     * @throws Exception 异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        
        // 只对登录接口进行IP白名单验证
        if (isLoginEndpoint(requestURI)) {
            return handleLoginSecurity(request, response);
        }
        
        return true;
    }

    /**
     * 判断是否为登录相关的端点
     *
     * @param requestURI 请求URI
     * @return true-是登录端点，false-不是
     */
    private boolean isLoginEndpoint(String requestURI) {
        return requestURI.contains("/login/login") || 
               requestURI.contains("/api/login/login") ||
               requestURI.endsWith("/login");
    }

    /**
     * 处理登录安全验证
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @return true-验证通过，false-验证失败
     * @throws IOException IO异常
     */
    private boolean handleLoginSecurity(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // IP白名单验证
        if (!ipWhitelistValidator.isIpAllowed(request)) {
            securityAuditLogger.logIpWhitelistViolation(request);
            writeErrorResponse(response, "访问被拒绝", 403);
            return false;
        }

        // 记录允许的访问
        ipWhitelistValidator.logIpAccess(request, true);
        return true;
    }

    /**
     * 写入错误响应
     *
     * @param response HTTP响应
     * @param message  错误消息
     * @param status   HTTP状态码
     * @throws IOException IO异常
     */
    private void writeErrorResponse(HttpServletResponse response, String message, int status) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        
        JsonResult result = JsonResult.error(status, message);
        String jsonResponse = JSONObject.toJSONString(result);
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(jsonResponse);
            writer.flush();
        }
    }
}
