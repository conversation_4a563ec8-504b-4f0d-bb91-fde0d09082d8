package com.javaweb.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HesMpDictionary;



/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/16 17:27
 */
public interface THesMpDictionaryService  extends IService<HesMpDictionary> {


   /**
    * 根据条件查询
    * @return
    */
   JsonResult getHesMpDictionaryList();

   /**
    * 根据条件查询 获得列表
    * @return
    */
   JsonResult getList(BaseQuery query);


   /**
    * 编辑
    *
    * @return
    */
   JsonResult edit(HesMpDictionary entity);

   /**
    * 删除
    *
    * @return
    */
   JsonResult deleteByIds(Integer[] ids);


   /**
    * 设置状态
    *
    * @param entity 实体对象
    * @return
    */
   JsonResult setStatus(HesMpDictionary entity);


}

