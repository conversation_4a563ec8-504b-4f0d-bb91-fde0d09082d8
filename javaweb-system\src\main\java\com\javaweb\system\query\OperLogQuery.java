
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.query;

import com.javaweb.common.common.BaseQuery;
import lombok.Data;

/**
 * 操作日志查询条件
 */
@Data
public class OperLogQuery extends BaseQuery {

    /**
     * 用户账号
     */
    private String operName;

    /**
     * 名称名称
     */
    private String title;

}
