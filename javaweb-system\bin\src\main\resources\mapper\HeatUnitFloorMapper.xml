<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HeatUnitFloorMapper">

    <!-- 通过关联id获得小区信息-->
    <select id="getHeatUnitFloorByLinkId" resultType="com.javaweb.system.dto.HouseInfoDto">
        SELECT
            mt.id AS id,
            c.name AS name
        FROM
            t_useheatunitfloor mt
                LEFT JOIN t_useheatunit c ON mt.useheatunitid = c.id

    </select>

    <!-- 通过楼宇获取个数-->
    <select id="getCountByNo" resultType="Integer">
        SELECT count(id) as num  FROM t_useheatunitfloor  where useheatunitid=#{useheatunitid}
        and floorname = #{floorname}
        <if test="id !=null">
            and id !=#{id}
        </if>
    </select>

    <!-- 基础信息-->
    <select id="getHeatUnitFloorBaseinfo" resultType="com.javaweb.system.entity.HeatUnitFloor">
        SELECT id,floorname  FROM t_useheatunitfloor where useheatunitid=#{useheatunitid} order by id asc;
    </select>


</mapper>
