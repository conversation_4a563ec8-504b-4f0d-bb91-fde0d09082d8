package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *小区信息
 * </p>
 *
 * @Date: 2022/12/12 14:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_useheatunitfloorunit")
public class HeatUnitFloorUnit implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单元名称
     */
    private String floorunitname;

    /**
     * 小区id
     */
    private Integer useheatunitid;

    /**
     * 小区
     */
    @TableField(exist=false)
    private String name;

    /**
     * 楼宇id
     */
    private Integer useheatunitfloorid;

    /**
     * 楼宇
     */
    @TableField(exist=false)
    private String floorname;


    /**
     * 住户数
     */
    private Integer floorunithousenum;


    /**
     * 备注
     */
    private String remarks;


}

