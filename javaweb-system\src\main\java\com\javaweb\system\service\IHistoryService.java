package com.javaweb.system.service;

import com.javaweb.common.common.IBaseService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HistoryData;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;


/**
 * <p>
 * 历史数据 服务类
 * </p>
 *
 *
 * @since 2020-11-02
 */
public interface IHistoryService extends IBaseService<HistoryData> {


    /**
     * 获取换热站天数据
     *
     * @return
     */
    public JsonResult getHistoryDt(String hescode, String dt);

    /**
     * 获取换热站 测点数据
     *
     * @return
     */
    public JsonResult getHistoryquxianDt(String hescode, String dt,String field);

    /**
     * 多个换热站的数据对比
     *
     * @return
     */
    public JsonResult  getMultHesHistoryQuxianDt(String hescode, String startDt,String endDt,Integer interval,String field);


    public JsonResult getheslist(int hescode);



    /**
     * 获取换热站配置字段信息
     *
     * @return
     */
    public JsonResult getHesFieldParam(int hescode);


}
