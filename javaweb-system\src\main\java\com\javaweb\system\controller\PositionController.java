
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Position;
import com.javaweb.system.query.PositionQuery;
import com.javaweb.system.service.IPositionService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 岗位表 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-02
 */
@RestController
@RequestMapping("/position")
public class PositionController extends BaseController {

    @Autowired
    private IPositionService positionService;

    /**
     * 获取岗位列表
     *
     * @param positionQuery 查询条件
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(PositionQuery positionQuery) {
        return positionService.getList(positionQuery);
    }

    /**
     * 添加岗位
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "岗位管理", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody Position entity) {
        return positionService.edit(entity);
    }

    /**
     * 编辑岗位
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "岗位管理", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody Position entity) {
        return positionService.edit(entity);
    }

    /**
     * 删除岗位
     *
     * @param positionIds 岗位ID
     * @return
     */
    @Log(title = "岗位管理", logType = LogType.DELETE)
    @DeleteMapping("/delete/{positionIds}")
    public JsonResult delete(@PathVariable("positionIds") Integer[] positionIds) {
        return positionService.deleteByIds(positionIds);
    }

    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "岗位管理", logType = LogType.STATUS)
    @PutMapping("/status")
    public JsonResult status(@RequestBody Position entity) {
        return positionService.setStatus(entity);
    }

    /**
     * 获取岗位列表
     *
     * @return
     */
    @GetMapping("/getPositionList")
    public JsonResult getPositionList() {
        return positionService.getPositionList();
    }

}
