<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.RecodeMapper">
    <!-- 获取最新5条报警数据 -->
    <select id="getAlarmData" resultType="com.javaweb.system.entity.Recode">
        SELECT  * FROM t_alarm  where isalarm=1 order by id desc limit 5;
    </select>

    <select id="getAlarmBycode" resultType="com.javaweb.system.entity.Recode">
        SELECT  * FROM t_alarm where hescode=#{hescode} and isalarm=1 and rule_id=#{ruleId};
    </select>

    <update id="updateAlarmStatusById" >
        update  t_alarm set isalarm=0  where id=#{id};
    </update>

</mapper>
