package com.javaweb.system.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.KnowledgeBase;
import com.javaweb.system.entity.RepairList;
import com.javaweb.system.entity.RepairSon;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.KnowledgeBaseMapper;
import com.javaweb.system.mapper.RepairListMapper;
import com.javaweb.system.mapper.RepairSonMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.KnowledgeBaseQuery;
import com.javaweb.system.query.RepairListQuery;
import com.javaweb.system.service.IKnowledgeBaseService;
import com.javaweb.system.service.IRepairListService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 维修列表 服务实现类
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Service
public class KnowledgeBaseServiceIml extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> implements IKnowledgeBaseService {

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;



    /**
     * 获取维修列表
     *
     * @param knowledgeBaseQuery 查询条件
     * @return
     */
    @Override
    public JsonResult getList(KnowledgeBaseQuery knowledgeBaseQuery) {

        // 查询条件
        QueryWrapper<KnowledgeBase> queryWrapper = new QueryWrapper<>();

        // 关键词
        if (!StringUtils.isEmpty(knowledgeBaseQuery.getKeywords())) {
            queryWrapper.like("keywords", knowledgeBaseQuery.getKeywords());
        }

        // 标题
        if (!StringUtils.isEmpty(knowledgeBaseQuery.getTitle())) {
            queryWrapper.like("title", knowledgeBaseQuery.getTitle());
        }

        // 分类
        if (!StringUtils.isEmpty(knowledgeBaseQuery.getFaultClass())) {
            queryWrapper.eq("fault_class", knowledgeBaseQuery.getFaultClass());
        }
        int nPage = 1;
        int nLimit = 10;

        if(null!=knowledgeBaseQuery.getPage()) {
            nPage = knowledgeBaseQuery.getPage();
        }
        if(null!=knowledgeBaseQuery.getLimit()) {
            nLimit = knowledgeBaseQuery.getLimit();
        }
        // 查询分页数据
        IPage<KnowledgeBase> page = new Page<>(nPage,nLimit);
        IPage<KnowledgeBase> pageData = knowledgeBaseMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(KnowledgeBase entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        if (StringUtils.isNotNull(entity.getId()) && entity.getId() > 0) {
            entity.setUpdateUser(ShiroUtils.getUserId());
            entity.setUpdateTime(DateUtils.now());
        } else {
            entity.setCreateUser(ShiroUtils.getUserId());
            entity.setCreateTime(DateUtils.now());
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }

    /**
     * 根据用户ID删除用户
     *
     * @param ids 记录ID
     * @return
     */
    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0

        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

}
