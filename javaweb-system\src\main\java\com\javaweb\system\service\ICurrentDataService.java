package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.CurrentData;
import com.javaweb.system.query.CurrentDataQuery;

public interface ICurrentDataService extends IService<CurrentData> {


    /**
     * 获取所有监测站实时数据
     *
     * @return
     */
    JsonResult getCurrentDataAll(CurrentDataQuery currentdataQuery);

}
