package com.javaweb.system.workthread;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.AlarmConfigDto;
import com.javaweb.system.entity.AlarmConfig;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.entity.RunRules;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.AlarmConfigMapper;
import com.javaweb.system.mapper.RulesMapper;
import com.javaweb.system.mapper.RunRulesMapper;
import com.javaweb.system.utils.ProjectUtils;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/*
*
* 告警配置加载类
* */
@Service
public class ConfigLoader {

    @Autowired
    AlarmConfigMapper alarmConfigMapper;

    @Autowired
    RulesMapper alarmRulesMapper;

    @Autowired
    RunRulesMapper runRulesMapper;

    @Autowired
    RedisUtils redisUtils;


    // 在系统启动时调用此方法来加载配置和规则
    public void loadConfigsAndRules() {
        String hashname="FD:AlarmRuleslist";
        Map<String, Object> map= new HashMap<>();

        // 从数据库加载所有监测站告警配置
        List<AlarmConfig> alarmConfigLst = alarmConfigMapper.getAlarmConfigLst();

        // 从数据库加载所有运行规则模板
        List<RunRules> runRulesLst = runRulesMapper.getRunRulesLst();

        // 从数据库加载所有告警规则模板
        List<Rules> alarmRulesLst = alarmRulesMapper.getRulesLst();


        // 建立运行规则模板的映射关系
        Map<Integer, RunRules> runRuleTemplateMap = new HashMap<>();
        for (RunRules template : runRulesLst) {
            runRuleTemplateMap.put(template.getId(), template);
        }
        // 建立告警规则模板的映射关系
        Map<Integer, Rules> alarmRuleTemplateMap = new HashMap<>();
        for (Rules template : alarmRulesLst) {
            alarmRuleTemplateMap.put(template.getId(), template);
        }

        // 将配置和规则关联起来，并存储在缓存中
//        for (AlarmConfigDto config : alarmConfigLst)
//        {
//            // 获取监测站的运行规则模板
//            RunRules runRuleTemplate = runRuleTemplateMap.get(config.getRunRulesIds());
//            config.setRunRuleTemplate(runRuleTemplate);
//            String alarmRuleIdsString = config.getAlarmRulesIds();
//            // 获取监测站的告警规则模板列表
//            List<String> alarmRuleTemplateIds = Arrays.asList(alarmRuleIdsString.split(","));
//
//            List<Rules> alarmRuleTemplates=new ArrayList<>();
//            // 检查 alarmRuleTemplateMap 是否包含所有的告警规则模板 ID
//            for (String alarmRuleId : alarmRuleTemplateIds) {
//                if (!alarmRuleTemplateMap.containsKey(Integer.parseInt(alarmRuleId))) {
//                    // 如果 alarmRuleTemplateMap 中没有某个告警规则模板 ID，则跳过
//                    continue;
//                }
//                // 获取告警规则模板并添加到列表中
//                alarmRuleTemplates.add(alarmRuleTemplateMap.get(Integer.parseInt(alarmRuleId)));
//            }
//            config.setAlarmRuleTemplates(alarmRuleTemplates);
//            // 存储在缓存中
//            map.put(String.valueOf(config.getHescode()),config);
//
////            String stationConfig = "stationAlarmconfig:" + String.valueOf(config.getHescode());
////            redisUtils.del(stationConfig);
////            //存入到redis中
////            redisUtils.lSet(stationConfig, config);
//        }

//
//        String stationConfig = "stationAlarmconfig:7" ;
//        List<Object> configList =redisUtils.lGet(stationConfig,0,-1);
//        List<AlarmConfig> rstLst = new ArrayList<>();
//        if (configList != null && !configList.isEmpty()) {
//            for (Object obj : configList) {
//                String json = JSON.toJSONString(obj);
//                rstLst.add(JSON.parseObject(json, AlarmConfig.class));
//            }
//        }
//        System.out.println(rstLst);
    }


    //验证是否满足运行规则
    public Boolean isInRunRuleTime(RunRules runRule)
    {
        Boolean isRun=false;
        LocalDate today = LocalDate.now();
        //id=1 则认为是供热季全天运行
        if(runRule.getId()==1)
        {
            isRun= ProjectUtils.isHeatingSeason(today);
        }else
        {
            //今天是否是配置的工作周
            isRun=ProjectUtils.isTodayOneOfTheWeekdays(runRule.getWeekRule());
            if(isRun)
            {
                //看是否满足运行时间段
                isRun=ProjectUtils.isCurrentTimeWithinPeriod(runRule.getRunCond());
            }
        }
        return isRun;
    }

    //验证是否满足告警规则
    public Boolean isAlarmConditionMet(Rules rule, JSONArray data_arr )
    {
        boolean result=false;
        //取出关系表达式
        String  alarmCond=rule.getCond();
        //字段 变量
        String[] fields=rule.getMpfield().split(",");
        //字段 别名
        String[] fieldAlisas=rule.getFieldAlisa().split(",");
        // 字段与别名的对应关系
        Map<String, String> fieldAliases = new HashMap<>();
        for(int i=0;i<fields.length;i++)
        {
            fieldAliases.put(fields[i], fieldAlisas[i]);
        }
        // 创建一个Map来存储匹配的别名和对应的Value
        Map<String, String> aliasValues = new HashMap<>();
        // 遍历JSONArray
        for (int i = 0; i < data_arr.size(); i++) {
            JSONObject dataObj = data_arr.getJSONObject(i);
            // 获取当前JSONObject的field属性
            String field = dataObj.getString("field");
            String value =dataObj.getString("value");
            String alias = fieldAliases.get(field);
            if (alias != null) {
                aliasValues.put(alias, value);
            }
           //验证关系表达式是否正确
        }
        try
        {
            //解析得到的表达式
            //String  expression=combineAlarmConditions(alarmCond);
            // 判断表达式是否成立
            result = evaluateExpression(alarmCond, aliasValues);
        }catch (Exception e)
        {
            result=false;
            System.out.println("表达式解析有误:"+alarmCond+"Exception e:"+ e);
        }

        return result;
    }
    //解析告警的规则字符串    F_S_T1>70&&S_S_T1<35
    // [{"Exp":"F_S_T1>70","sign":"&&","mode":0},{"Exp":"S_S_T1<35","sign":"&&","mode":0}],
    public static String combineAlarmConditions(String alarmCondJson) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(alarmCondJson);
        StringBuilder combinedExp = new StringBuilder();
        for (int i = 0; i < jsonNode.size(); i++) {
            JsonNode cond = jsonNode.get(i);
            combinedExp.append(cond.get("Exp").asText());
            if (i < jsonNode.size() - 1) { // 不要最后一个操作符
                combinedExp.append(cond.get("sign").asText());
            }
        }
        return combinedExp.toString();
    }

    //判断表达狮子
    public static boolean evaluateExpression(String expression, Map<String, String> fieldValues) {
        // 创建ScriptEngine对象
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("JavaScript");

        // 将字段和值添加到ScriptEngine的上下文中
        for (Map.Entry<String, String> entry : fieldValues.entrySet()) {
            try {
                // 尝试将值转换为数值类型
                Object value = Double.parseDouble(entry.getValue());
                engine.put(entry.getKey(), value);
            } catch (NumberFormatException e) {
                // 如果转换失败，则保留为字符串
                engine.put(entry.getKey(), entry.getValue());
            }
        }

        try {
            // 执行表达式
         //   Object result = engine.eval(expression);
            // 返回表达式的结果
            //return (Boolean) result;


            Object result = engine.eval(expression);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                // 如果结果不是布尔类型，打印出警告并返回false
                System.out.println("表达式返回的不是布尔类型: " + result);
                return false;
            }

        } catch (ScriptException e) {
            // 如果表达式有误，返回false
            e.printStackTrace(); // 打印错误信息
            return false;
        }
    }
    //从redis 中获取告警配置规则
    public  List<AlarmConfig> getAlarmConfigByRedis(String hescode)
    {
        String stationConfig = "stationAlarmconfig:" + hescode;
        List<Object> configList =redisUtils.lGet(stationConfig,0,-1);
        List<AlarmConfig> rstLst = new ArrayList<>();
        if (configList != null && !configList.isEmpty()) {
            for (Object obj : configList) {
                String json = JSON.toJSONString(obj);
                rstLst.add(JSON.parseObject(json, AlarmConfig.class));
            }
        }
        return rstLst;
    }

    public void insertAlarmRecord(Connection connection, Integer hescode, Rules alarmRules)
    {
        String sql = "insert into t_alarm(hescode,alarm_field,alarm_level,alarm_type,alarm_dt,isalarm,ishandle,rule_id," +
                "reminder_cycle,last_reminder_dt)" +
                "values(?,?,?,?,?,?,?,?,?,?)";
        try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
            // 设置参数
            pStatement.setInt(1, hescode);
            pStatement.setString(2, alarmRules.getAlarmDesc());
            pStatement.setInt(3, alarmRules.getAlarmLevel());
            pStatement.setInt(4, alarmRules.getAlarmType());
            pStatement.setObject(5, DateUtils.now());
            pStatement.setInt(6, 1);
            pStatement.setInt(7, 0);
            pStatement.setInt(8, alarmRules.getId());
            pStatement.setInt(9, alarmRules.getReminderCycle());
            pStatement.setObject(10, DateUtils.now());
            pStatement.executeUpdate();
        } catch (Exception e) {
            System.err.println("插入告警记录失败！" + hescode + " error: " + e.getMessage());
        }
    }


}
