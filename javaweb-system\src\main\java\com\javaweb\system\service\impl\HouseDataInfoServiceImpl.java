package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HouseDataInfo;
import com.javaweb.system.entity.HousePayInfo;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.mapper.HouseDataInfoMapper;
import com.javaweb.system.mapper.HousePayInfoMapper;
import com.javaweb.system.query.HouseDataInfoQuery;
import com.javaweb.system.query.HousePayInfoQuery;
import com.javaweb.system.service.IHouseDataInfoService;
import com.javaweb.system.service.IHousePayInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 *  住户缴费信息表 服务实现类
 * </p>
 *
 * @Date: 2022/12/12 14:58
 */
@Service
public class HouseDataInfoServiceImpl extends ServiceImpl<HouseDataInfoMapper, HouseDataInfo> implements IHouseDataInfoService {

    @Autowired
    HousePayInfoMapper housePayInfoMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private HeatUnitMapper heatUnitMapper;


    @Autowired
    DataSource dataSource;

    /**
     * 查询小区列表
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        HouseDataInfoQuery houseDataInfoQuery =(HouseDataInfoQuery) query;
        //查询条件
        String where="";
        String unitNo="";

        //小区名称
        if(!StringUtils.isNull(houseDataInfoQuery.getUseheatunitId())){
            //根据小区id 获得编号
            unitNo=heatUnitMapper.getHeatUnitNoById(houseDataInfoQuery.getUseheatunitId());
        }

        // 热表编号
        if(!StringUtils.isNull(houseDataInfoQuery.getHeatmeterno())){
            where += " WHERE heatMeterNo=" + houseDataInfoQuery.getHeatmeterno();
        }
        Integer npage = houseDataInfoQuery.getPage() != null ? houseDataInfoQuery.getPage() : 1;
        Integer nlimit = houseDataInfoQuery.getLimit() != null ? houseDataInfoQuery.getLimit() : 10;
        IPage<HouseDataInfo> page = new Page<>(npage, nlimit, 0);
          if(!unitNo.isEmpty())
          {
              try{
                  // 创建分页对象
                  String tableName="t_indoorT_data_"+unitNo;
                  // 查询总行数
                  String countSql = "SELECT COUNT(id) FROM " + tableName + where;
                  Integer totalCount = jdbcTemplate.queryForObject(countSql, Integer.class);
                  // 查询分页数据
                  String sql = "SELECT * FROM " + tableName + where;
                  sql += " LIMIT " + (npage - 1) * nlimit + ", " + nlimit;
                  List<HouseDataInfo> records = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(HouseDataInfo.class));
                  //System.out.println(records);
                  // 返回分页结果
                  page = new Page<>(npage, nlimit, totalCount);
                  page.setRecords(records);

                  return  JsonResult.success(page);
              }catch (Exception e)
              {
                  return  JsonResult.success(page);
              }
          }

        return  JsonResult.success(page);
    }

    @Override
    public JsonResult getIndoorTDayData(Integer useheatunitId,String heatno)
    {

        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        String unitNo=heatUnitMapper.getHeatUnitNoById(useheatunitId);
        String tableName="t_indoorT_data_"+unitNo;
        try {
            // 使用 try-with-resources 自动关闭资源
            try(Connection connection = dataSource.getConnection())
            {
                // 构造 SQL 语句并手动拼接表名
                String sql = "SELECT collectdt, F_S_T1, F_B_T1 FROM " + tableName + " WHERE heatMeterNo = ? ORDER BY collectdt DESC LIMIT 100";
                pStatement = connection.prepareStatement(sql);
                // 设置参数
                pStatement.setString(1, heatno);
                rSet = pStatement.executeQuery();
                while (rSet.next())
                {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    String collectdt =rSet.getString("collectdt");
                    String fst =rSet.getString("F_S_T1");
                    String fbt =rSet.getString("F_B_T1");
                    resultMap.put("dt",collectdt);
                    resultMap.put("fst",fst);
                    resultMap.put("fbt",fbt);
                    lst.add(resultMap);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error("数据库查询失败: " + e.getMessage());
        }catch (Exception e) {
            // 处理其他可能的异常
            e.printStackTrace();
            return JsonResult.error("系统错误: " + e.getMessage());
        }
        return JsonResult.success(lst);
    }

}




