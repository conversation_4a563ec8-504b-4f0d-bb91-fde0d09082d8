package com.javaweb.system.service.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.common.CommonVar;
import com.javaweb.common.config.UploadFileConfig;
import com.javaweb.common.constant.CommonConstants;
import com.javaweb.common.utils.*;
import com.javaweb.system.dto.HesDto;
import com.javaweb.system.dto.MonitorDto;
import com.javaweb.system.dto.MonitorPointDefineDto;
import com.javaweb.system.dto.MpdictDto;
import com.javaweb.system.entity.HesMonitorPointDeFine;
import com.javaweb.system.entity.HesMpDictionary;
import com.javaweb.system.entity.Level;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.HesMonitorPointDeFineMapper;
import com.javaweb.system.mapper.HesMpDictionaryMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.query.HesMonitorPointDeFineQuery;
import com.javaweb.system.service.THesMonitorPointDeFineService;
import com.javaweb.system.utils.ShiroUtils;
import com.javaweb.system.vo.monitor.MonitorInfoVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.*;
import java.sql.*;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/16 17:29
 */

@Service
public class THesMonitorPointDeFineServiceImpl extends ServiceImpl<HesMonitorPointDeFineMapper, HesMonitorPointDeFine> implements THesMonitorPointDeFineService {

    @Autowired
    HesMonitorPointDeFineMapper hesMonitorPointDeFineMapper;

    @Autowired
    DataSource dataSource;

    @Autowired
    private HesMpDictionaryMapper hesMpDictionaryMapper;

    @Autowired
    private THesMapper tHesMapper;


    @Autowired
    RedisUtils redisUtils;

    @Override
   public JsonResult getList(HesMonitorPointDeFineQuery hesMonitorPointDeFineQuery)
    {
        //查询条件
        QueryWrapper<HesMonitorPointDeFine> queryWrapper = new QueryWrapper<>();
        //监测站编号
        if (StringUtils.isNotNull(hesMonitorPointDeFineQuery.getHescode())){
           queryWrapper.eq("hescode",hesMonitorPointDeFineQuery.getHescode());
        };
        Integer npage = 1;
        Integer nlimit = 10;
        if (hesMonitorPointDeFineQuery.getPage()!=null){
            npage=hesMonitorPointDeFineQuery.getPage();
        }
        if (hesMonitorPointDeFineQuery.getLimit()!=null){
            nlimit= hesMonitorPointDeFineQuery.getLimit();
        }

       IPage<HesMonitorPointDeFine> page = new Page<>(npage,nlimit);
       IPage<HesMonitorPointDeFine> page1 = hesMonitorPointDeFineMapper.selectPage(page,queryWrapper);
       return JsonResult.success(page1);
   }

    @Override
    public JsonResult edit(HesMonitorPointDeFine entity)
    {
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }


    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        List<HesDto> heslist=tHesMapper.getHesList();
        for (int i=0; i<heslist.size();i++)
        {
            int hescode = heslist.get(i).getHescode();
            List<MonitorDto> hesdictDatas = hesMonitorPointDeFineMapper.getDictDataByHescode(hescode);
            String  hesfield="hesfield:"+String.valueOf(hescode);
            redisUtils.del(hesfield);
            //存入到redis中
            redisUtils.lSet(hesfield,hesdictDatas);
        }
        return JsonResult.success("删除成功");
    }


    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult setStatus(HesMonitorPointDeFine entity) {
        boolean result = this.updateById(entity);
        if (!result) {
            return JsonResult.error();
        }
        List<MonitorDto> hesdictDatas = hesMonitorPointDeFineMapper.getDictDataByHescode(entity.getHescode());
        String  hesfield="hesfield:"+String.valueOf(entity.getHescode());
        redisUtils.del(hesfield);
        //存入到redis中
        redisUtils.lSet(hesfield,hesdictDatas);
        return JsonResult.success();
    }
    /**
     * 导入Excel数据
     *
     * @param request 网络请求
     * @param name    目录名称
     * @return
     */
    @Override
    public JsonResult importExcel(HttpServletRequest request, String name) {
        // 上传文件
        UploadUtils uploadUtils = new UploadUtils();
        uploadUtils.setDirName("files");
        Map<String, Object> result = uploadUtils.uploadFile(request, name);
        List<String> imageList = (List<String>) result.get("image");

        // 文件路径
        String filePath = UploadFileConfig.uploadFolder + imageList.get(imageList.size() - 1);
        // 读取文件
        List<Object> rows = ExcelUtil.readMoreThan1000RowBySheet(filePath, null);
        if (CollectionUtils.isEmpty(rows)) {
            return JsonResult.error("文件读取失败");
        }
        int totalNum = 0;
        for (int i = 1; i < rows.size(); i++) {
            // 排除第一行
            String info = rows.get(i).toString();
            if (info.length() <= 2) {
                continue;
            }

            info = info.substring(1, info.length() - 1);
            String[] cloumns = info.split(",\\s+");
            if (cloumns.length != 8) {
                continue;
            }

            // 插入数据
            HesMonitorPointDeFine hesMonitorPointDeFine = new HesMonitorPointDeFine();
            hesMonitorPointDeFine.setHescode(Integer.parseInt(cloumns[0]));
            hesMonitorPointDeFine.setWrAddress(cloumns[1]);
           // hesMonitorPointDeFine.setMpfieldid(cloumns[2]);
           // hesMonitorPointDeFine.setMpfieldgroup(cloumns[3]);
            hesMonitorPointDeFine.setMpfielddefine(cloumns[4]);
            hesMonitorPointDeFine.setMpdesc(cloumns[5]);
            hesMonitorPointDeFine.setMinvalue(cloumns[6]);
           // hesMonitorPointDeFine.setMaxvalue(cloumns[7]);
            int count = hesMonitorPointDeFineMapper.insert(hesMonitorPointDeFine);
            if (count == 1) {
                totalNum++;
            }
        }
        return JsonResult.success(null, String.format("本次共导入数据【%s】条", totalNum));
    }

    /**
     * 导出Excel
     *
     * @param hescode 编号
     * @return
     */
    @Override
    public List<MonitorInfoVo> exportExcel(Integer hescode) {
        // 查询条件
        QueryWrapper<HesMonitorPointDeFine> queryWrapper = new QueryWrapper<>();
        // 编号
        queryWrapper.eq("hescode", hescode);
        // 编号
        queryWrapper.orderByAsc("id");
        // 查询分页数据
        List<HesMonitorPointDeFine> hesmonitorList = hesMonitorPointDeFineMapper.selectList(queryWrapper);
        List<MonitorInfoVo> hesmonitorInfoVoList = new ArrayList<>();
        if (!hesmonitorList.isEmpty()) {
            hesmonitorList.forEach(item -> {
                MonitorInfoVo monitorInfoVo = new MonitorInfoVo();
                BeanUtils.copyProperties(item, monitorInfoVo);
                hesmonitorInfoVoList.add(monitorInfoVo);
            });
        }
        return hesmonitorInfoVoList;
    }

    @Override
    public JsonResult getHesMonitorPointDeFineList() {
       QueryWrapper<HesMonitorPointDeFine> queryWrapper = new QueryWrapper<>();
        List<HesMonitorPointDeFine> list =list(queryWrapper);
        return JsonResult.success(list);
    }

    /**
     * 从测点池中得到对应分区的所有字段
     *
     * @param hescode    站编号
     * @return
     */
    @Override
    public JsonResult getHesFieldSetting(int hescode) {

        try (Connection connection = dataSource.getConnection()) {
            // 获取设备编号
            String equipmentNumSql = "SELECT equipmentNum FROM T_Hes WHERE HesCode = ?";
            try (PreparedStatement equipmentNumStatement = connection.prepareStatement(equipmentNumSql)) {
                equipmentNumStatement.setInt(1, hescode);
                try (ResultSet equipmentNumResult = equipmentNumStatement.executeQuery()) {
                    if (!equipmentNumResult.next()) {
                        return JsonResult.error(" 没有找到对应的换热站信息");
                    }
                    String equipmentNum = equipmentNumResult.getString("equipmentNum");
                    if (equipmentNum == null || equipmentNum.isEmpty()) {
                        return JsonResult.error("设备编号为空");
                    }
                    equipmentNum = equipmentNum.substring(0, 1);

                    // 查询监控点设置
                    String mpDictSql = "SELECT id, Num, MPDesc, MPField FROM t_hesmpdictionary WHERE Num <= ?";
                    try (PreparedStatement mpDictStatement = connection.prepareStatement(mpDictSql)) {
                        mpDictStatement.setString(1, equipmentNum);
                        try (ResultSet mpDictResult = mpDictStatement.executeQuery()) {
                            List<MpdictDto> lst = new ArrayList<>();
                            while (mpDictResult.next()) {
                                MpdictDto mpdictDto = new MpdictDto();
                                mpdictDto.setId(mpDictResult.getInt("id"));
                                mpdictDto.setMpfielddefine(mpDictResult.getString("MPField"));
                                mpdictDto.setMpdesc(mpDictResult.getString("MPDesc"));
                                mpdictDto.setNum(mpDictResult.getString("Num"));
                                lst.add(mpdictDto);
                            }
                            return JsonResult.success(lst);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 测点配置 从测点库中取出测点进行配置
     *
     * @param Ids        配置测点id
     * @param hescode    编号
     * @return
     */
    @Override
    public JsonResult setHesFieldSetting(Integer[] Ids, int hescode) {

        try (Connection connection = dataSource.getConnection()) {
            // 1. 通过接收过来的 ids 在测点库中查询出所有的测点
            QueryWrapper<HesMpDictionary> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", Ids);
            List<HesMpDictionary> list = hesMpDictionaryMapper.selectList(queryWrapper);
            if (list.isEmpty())
            {
                return JsonResult.error("没有找到对应的测点信息");
            }

            // 2. 删除目标换热站的旧配置
            deleteHesMonitorPointsByHesCode(hescode, connection);

            // 3. 获取目标换热站名称
            String hesname = getHesNameByHesCode(hescode, connection);
            if(hesname == null) {
                return JsonResult.error("没有找到对应的换热站信息");
            }
            // 3. 循环 list 将每一条数据插入到测点配置表中
            String insertSql = "INSERT INTO t_hesmonitorpointdefine (HesCode, `WR_Address`, `Isused`, `MPdesc`, `MPFieldDefine`," +
                    " `unit`, `MinValue`, `MaxValue`, `IsControl`,`isWebshow`,`field_alisa`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?,?)";
            try (PreparedStatement insertStatement = connection.prepareStatement(insertSql)) {
                for (HesMpDictionary item : list) {
                    insertStatement.setInt(1, hescode);
                    insertStatement.setString(2, hesname);
                    insertStatement.setInt(3, 1); // 假设这里的值固定为1
                    insertStatement.setString(4, item.getMpdesc());
                    insertStatement.setString(5, item.getMpfield());
                    insertStatement.setString(6, item.getUnit());
                    insertStatement.setString(7, item.getMpminvalue());
                    insertStatement.setString(8, item.getMpmaxvalue());
                    insertStatement.setInt(9, item.getIsControl()); // 假设这里的值固定为0
                    insertStatement.setInt(10, item.getIsShow());
                    insertStatement.setString(11, item.getFieldAlisa());
                    insertStatement.addBatch();  // 添加到批处理队列
                }
                insertStatement.executeBatch(); // 执行批处理
            }
            // 5. 更新 Redis
            updateRedisForHesMonitorPoints(hescode);
            return JsonResult.success("操作成功");

        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 测点配置 复制另一个换热站配置的测点
     *
     * @param ids        配置测点id
     * @param sourceHesCode    源换热站编号
     * @param targetHesCode    目标换热站编号
     * @return
     */
    @Override
    public JsonResult setHesFieldByOtherCode(Integer[] ids, int sourceHesCode, int targetHesCode) {
        Connection connection = null;
        try {
            connection = dataSource.getConnection();

            // 1. 查询源换热站的测点
            List<MonitorPointDefineDto> sourcePoints = getHesMonitorPointsByIdsAndHesCode(ids, sourceHesCode);
            if (sourcePoints.isEmpty()) {
                return JsonResult.error("没有找到源换热站对应的测点信息");
            }

            // 2. 删除目标换热站的旧配置
            deleteHesMonitorPointsByHesCode(targetHesCode, connection);

            // 3. 获取目标换热站名称
            String targetHesName = getHesNameByHesCode(targetHesCode, connection);
            if (targetHesName == null) {
                return JsonResult.error("没有找到对应的目标换热站信息");
            }

            // 4. 插入新的测点配置到目标换热站
            insertHesMonitorPoints(sourcePoints, targetHesCode, targetHesName, connection);

            // 5. 更新 Redis
            updateRedisForHesMonitorPoints(targetHesCode);

            return JsonResult.success("操作成功");

        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        } finally {
            closeConnection(connection);
        }
    }


    private List<MonitorPointDefineDto> getHesMonitorPointsByIdsAndHesCode(Integer[] ids, Integer hesCode) {
        List<MonitorPointDefineDto> lst=hesMonitorPointDeFineMapper.getMonitorPointDeFineByHescode(ids,hesCode);
        return lst;

    }

    private void deleteHesMonitorPointsByHesCode(int hesCode, Connection connection) throws SQLException {
        String deleteSql = "DELETE FROM t_hesmonitorpointdefine WHERE hescode = ?";
        try (PreparedStatement deleteStatement = connection.prepareStatement(deleteSql)) {
            deleteStatement.setInt(1, hesCode);
            deleteStatement.executeUpdate();
        }
    }

    private String getHesNameByHesCode(int hesCode, Connection connection) throws SQLException {
        QueryWrapper<THes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hescode", hesCode);
        List<THes> lst = tHesMapper.selectList(queryWrapper);
        return lst.isEmpty() ? null : lst.get(0).getName();
    }

    private void insertHesMonitorPoints(List<MonitorPointDefineDto> points, int targetHesCode, String targetHesName, Connection connection) throws SQLException {
        String insertSql = "INSERT INTO t_hesmonitorpointdefine (HesCode, `WR_Address`, `Isused`," +
                " `MPdesc`, `MPFieldDefine`, `unit`, `MinValue`, `MaxValue`, `IsControl`,`field_alisa`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?,?)";
        try (PreparedStatement insertStatement = connection.prepareStatement(insertSql)) {
            for (MonitorPointDefineDto item : points)
            {
                insertStatement.setInt(1, targetHesCode);
                insertStatement.setString(2, targetHesName);
                insertStatement.setInt(3, item.getIsused());
                insertStatement.setString(4, item.getMpdesc());
                insertStatement.setString(5, item.getMpfielddefine());
                insertStatement.setString(6, item.getUnit());
                insertStatement.setString(7, item.getMinvalue());
                insertStatement.setString(8, item.getMaxvalue());
                insertStatement.setInt(9, item.getIscontrol());
                insertStatement.setString(10, item.getFieldAlisa());
                insertStatement.addBatch();
            }
            insertStatement.executeBatch();
        }
    }

    private void updateRedisForHesMonitorPoints(int hesCode) {
        List<MonitorDto> hesdictDatas = hesMonitorPointDeFineMapper.getDictDataByHescode(hesCode);
        String hesfield = "hesfield:" + String.valueOf(hesCode);
        redisUtils.del(hesfield);
        redisUtils.lSet(hesfield, hesdictDatas);
    }

    private void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 测试redis 缓存配置字段
     *
     * @param hescode    编号
     * @return List<MpdictDto>数组
     */
    @Override
    public  List<MonitorDto> getDictDataByCode(Integer hescode)
    {
        //取出redis缓存数据
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        String hesfield = "hesfield:" + String.valueOf(hescode);

        List<Object> dictData = redisUtils.lGet(hesfield, 0, -1);
        //判断redis缓存数据是否存在
        //若存在，则返回redis
        if (StringUtils.isNotNull(dictData))
        {
            List<MonitorDto> rstLst = new ArrayList<>();
            for(int i=0;i<dictData.size();i++)
            {
                //System.out.println(dictData.get(i));
                Object ob = dictData.get(i);
                rstLst =  JSON.parseArray(JSON.toJSONString(ob),MonitorDto.class);
            }
            return rstLst;
        }
        List<MonitorDto> hesdictDataLst = hesMonitorPointDeFineMapper.getDictDataByHescode(hescode);
        hesfield="hesfield:"+String.valueOf(hescode);
        redisUtils.lSet(hesfield,hesdictDataLst);
        return hesdictDataLst;
    }

    public static List<HesMonitorPointDeFine> getDictCache(String key)
    {
        Object cacheObj = SpringUtils.getBean(RedisUtils.class).get(key);
        if (StringUtils.isNotNull(cacheObj))
        {
            List<HesMonitorPointDeFine> DictDatas = StringUtils.cast(cacheObj);
            return DictDatas;
        }
        return null;
    }

    /*
     * 生成数据表
     * 参数：
     * hescode  换热站编号
     * year     年份
     * 2023/1/31 xqt
     * */
    public JsonResult creatDataBaseTable(int hescode) throws SQLException
    {
            Connection connection =null;
            Statement  statement=null;
            PreparedStatement preparedStatement = null;
            ResultSet resultSet =null;
            List<String> list = new ArrayList<>();
            try {
                //连接数据库
                connection = dataSource.getConnection();
                statement = connection.createStatement();
                //sql语句
                String sql = "select MPDesc,MPFieldDefine  from t_hesmonitorpointdefine where Hescode="+hescode;
                //输出sql语句
                preparedStatement= connection.prepareStatement(sql);
                resultSet=preparedStatement.executeQuery();
                //从结果集中取得数据存入list
                while (resultSet.next())
                {
                    list.add(resultSet.getString("MPFieldDefine"));
                }
                //数据库表名
                String tablename="t_hes_data_"+String.valueOf(hescode);
                //判断数据库表名存在
                ResultSet resultSet1 = connection.getMetaData().getTables(null,null, tablename,null);
                if (resultSet1.next())
                {
                    return  JsonResult.success("数据表已经存在");
                }
                list.add(0,"collectDt");
                list.add(0,"id");
                String sqlTable=createTableSQLandPartition(list,tablename);
                statement.executeUpdate(sqlTable);
            } catch (SQLException e)
            {
                e.printStackTrace();
            }finally {
                // 关闭资源
                if (resultSet != null) {
                    try {
                        resultSet.close();
                    } catch (SQLException e) {
                        System.out.println("关闭 ResultSet 时发生错误："+e.getMessage());
                    }
                }
                if (preparedStatement != null) {
                    try {
                        preparedStatement.close();
                    } catch (SQLException e) {
                        System.out.println("关闭 PreparedStatement 时发生错误："+ e.getMessage());
                    }
                }
                if (statement != null) {
                    try {
                        statement.close();
                    } catch (SQLException e) {
                        System.out.println("关闭 Statement 时发生错误：" + e.getMessage());
                    }
                }
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (SQLException e) {
                        System.out.println("关闭 Connection 时发生错误：{}"+e.getMessage());
                    }
                }
            }
            return JsonResult.success("创建数据表成功");
     }

    /*
    * 创建数据表sql语句
    * 参数：
    * list       字段
    * tablename  表名
    * 2023/1/31 xqt
    * */
    public static String createTableSQL(List<String> list,String tablename) {
        StringBuffer sb = new StringBuffer();
        sb.append("CREATE TABLE `" + tablename + "` (\n");

        for (int i = 0; i < list.size(); i++) {
            // 当前条数据
            if (StringUtils.equals(list.get(i), "id")) {
                sb.append("id INT UNSIGNED AUTO_INCREMENT").append(",");
            }else if (StringUtils.equals(list.get(i), "collectDt")) {
                sb.append("collectDt datetime ").append(",");
            }else {
                // 判断数据类型
                String fieldType = "varchar";
                sb.append("`" + list.get(i) + "`").append(" ");
                // 追加列
                sb.append("varchar(50)").append(",");
            }
            if (list.size()-1==i) {
                sb.append("PRIMARY KEY (`id`,`collectDt`)");
                sb.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8;");
            }
        }
        //System.out.println(sb.toString());
        return sb.toString();
    }


    /*
     * 创建数据表以及分区
     * 参数：
     * list       字段
     * tablename  表名
     * 2023/1/31 xqt
     * */
    public static String createTableSQLandPartition(List<String> list,String tablename) {
        StringBuffer sb = new StringBuffer();
        sb.append("CREATE TABLE `" + tablename + "` (\n");

        for (int i = 0; i < list.size(); i++) {
            // 当前条数据
            if (StringUtils.equals(list.get(i), "id")) {
                sb.append("id INT UNSIGNED AUTO_INCREMENT").append(",");
            }else if (StringUtils.equals(list.get(i), "collectDt")) {
                sb.append("collectDt datetime ").append(",");
            }else {
                // 判断数据类型
                String fieldType = "varchar";
                sb.append("`" + list.get(i) + "`").append(" ");
                // 追加列
                sb.append("varchar(50)").append(",");
            }
            if (list.size()-1==i) {
                sb.append("PRIMARY KEY (`id`,`collectDt`)");
                sb.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8");
            }
        }
        sb.append(" PARTITION BY RANGE(YEAR(collectDt)) (");
        Calendar calendar = Calendar.getInstance();

        for (int i = 0; i < 11; i++)
        {
            int year = calendar.get(Calendar.YEAR);
            year=year+i;
            String name=tablename+"_"+String.valueOf(year);
            year=year+1;
            if (i==10)
            {
                sb.append(" PARTITION  "+ name+" VALUES LESS THAN ("+year+"))");
            }else
            {
                sb.append(" PARTITION  "+ name+" VALUES LESS THAN ("+year+"),");
            }
        }
        return sb.toString();
    }

    @Override
    public JsonResult exportTxt(HttpServletResponse response,Integer hescode)
    {
        //获得数据列表
        List<MonitorInfoVo> list = exportExcel(hescode);

        if(list ==null || list.size()<=0)
        {
            return null;
        }
        List txtContentList=new ArrayList();
        try {
           BufferedWriter writer = new BufferedWriter(new FileWriter("output.txt"));
            for (MonitorInfoVo data : list)
            {
                String mpdesc=data.getMpdesc();
                String mpfielddefine=data.getMpfielddefine();
                //sb.append(mpdesc+","+mpfielddefine+"\n");
                String line=mpdesc+","+mpfielddefine;
                writer.write(line);
                writer.newLine(); // 在每行后添加新行
            }
            writer.close();
            exportTxt(response,"output.txt");
            return JsonResult.success();
            //writer.close();
        } catch (IOException e) {
            e.printStackTrace();
            return JsonResult.error();
        }
    }


    /* 导出txt文件

     * <AUTHOR> @param response

     * @param text 导出的字符串

     * @return

     */
    public static void   exportTxt(HttpServletResponse response, String text){

        // 设置响应头，指定文件名
        response.setHeader("Content-Disposition", "attachment; filename=file.txt");
        try {
        // 获取文件输入流
        InputStream inputStream = new FileInputStream("");

        // 创建StreamingResponseBody对象，将文件内容写入响应输出流
        StreamingResponseBody responseBody = outputStream -> {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        };

        // 返回StreamingResponseBody对象
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        responseBody.writeTo(response.getOutputStream());
      } catch (IOException e) {
        e.printStackTrace();
     }
    }
}

