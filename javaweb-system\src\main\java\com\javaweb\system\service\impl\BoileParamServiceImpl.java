package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.DateUtils;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.BoileParam;
import com.javaweb.system.entity.CurrentData;
import com.javaweb.system.entity.Notice;
import com.javaweb.system.mapper.BoileParamMapper;
import com.javaweb.system.mapper.CurrentDataMapper;
import com.javaweb.system.query.CurrentDataQuery;
import com.javaweb.system.service.IBoileParamService;
import com.javaweb.system.service.ICurrentDataService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 *
 * @since 2022-12-12
 */

@Service
public class BoileParamServiceImpl extends ServiceImpl<BoileParamMapper, BoileParam> implements IBoileParamService
{

    /**
     * 获取所有数据列表
     *
     * @return
     */
    @Override
    public JsonResult edit(BoileParam entity) {

        entity.setDt(DateUtils.now());
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }


}





