package com.javaweb.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.service.IRecvDataService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 接收数据 服务实现类
 * </p>
 *
 *
 * @since 2023-1-10
 */

@Service
public class RecvDataServiceImpl implements IRecvDataService {

    @Autowired
    DataSource dataSoure;

    @Override
    public JsonResult getRecvdata(JSONObject data, HttpServletRequest request)
    {
        Connection connection = null;
        Statement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        int rNum=0;
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        LinkedHashMap<Object, Object> objectObjectHashMap1 = new LinkedHashMap<>();

        System.out.println("接收到的数据："+data);
        JSONObject recvdata = data.getJSONObject("jsondata");
        String hescode = recvdata.getString("stationNo");
        String key=recvdata.getString("md5Key");
        int    dataLen=Integer.parseInt(recvdata.getString("dataLen"));
        int    rate=recvdata.getInteger("rate");
        JSONArray data_arr = recvdata.getJSONArray("data");
        String  md5Key=getVerify(dataLen,rate,hescode);

         //字符串比对
         if(key.equals(md5Key))
         {
             try {
                 //获取本年份
                 Calendar calendar=Calendar.getInstance();
                 int year=calendar.get(Calendar.YEAR);
                 SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                 String collectdt=sdf.format(System.currentTimeMillis());
                 String tablename="t_hes_data_"+hescode;
                 connection = dataSoure.getConnection();
                 String sqlfield="insert into "+tablename+" (CollectDt," ;
                 String sqlvalue=" values('"+collectdt+"'," ;
                 String sql="";
                 for(int i = 0 ; i<dataLen ; i++)
                 {
                     JSONObject dataIndex = data_arr.getJSONObject(i);
                     if(i<dataLen-1)
                     {
                         sqlfield=sqlfield +"`"+dataIndex.getString("id")+"`,";
                         sqlvalue=sqlvalue +"'"+ dataIndex.getString("value")+"',";
                     }else
                     {
                         sqlfield=sqlfield +"`"+ dataIndex.getString("id")+"`)";
                         sqlvalue=sqlvalue +"'"+dataIndex.getString("value")+"')";
                     }
                 }
                 sql=sqlfield+sqlvalue;
                 rSet=connection.getMetaData().getTables(null,null,tablename,null);
                 if(rSet.next())
                 {
                     pStatement = connection.createStatement();
                     rNum = pStatement.executeUpdate(sql);
                     Rst="插入成功";
                 }else
                 {
                     Rst="数据表不存在";
                 }
                 connection.close();

             }catch (SQLException ex) {

             }
         }

        return JsonResult.success(Rst);
    }


    /*
     *
     * 接收换热站数据
     * */
    @Async("threadPoolTaskExecutor")
    @Override
    public JsonResult getRecvHesdata(String data, HttpServletRequest request)
    {
        Connection connection = null;
        Statement pStatement = null;
        ResultSet rSet = null;
        PreparedStatement preparedStatement = null;
        String Rst="";
        int rNum=0;
        //System.out.println(data);
        JSONObject recvdata=JSONObject.parseObject(data);

        String hescode = recvdata.getString("hescode");
        JSONArray data_arr = recvdata.getJSONArray("data");
        int    dataLen=Integer.parseInt(recvdata.getString("datalen"));
        String collectdt="";
        try {
            //获取本年份
            Calendar calendar=Calendar.getInstance();
            int year=calendar.get(Calendar.YEAR);
            SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            collectdt=sdf.format(System.currentTimeMillis());
            //查询
            ResultSet resultSet =null;
            String tablename="t_hes_data_"+hescode;
            connection = dataSoure.getConnection();
            String sqlfield="insert into "+tablename+" (CollectDt," ;
            String sqlvalue=" values('"+collectdt+"'," ;
            String sql="";
            for(int i = 0 ; i<dataLen ; i++)
            {
                JSONObject dataIndex = data_arr.getJSONObject(i);
                if(i<dataLen-1)
                {
                    sqlfield=sqlfield +"`"+dataIndex.getString("field")+"`,";
                    sqlvalue=sqlvalue +"'"+ dataIndex.getString("value")+"',";
                }else
                {
                    sqlfield=sqlfield +"`"+ dataIndex.getString("field")+"`)";
                    sqlvalue=sqlvalue +"'"+dataIndex.getString("value")+"')";
                }
            }
            sql=sqlfield+sqlvalue;
            rSet=connection.getMetaData().getTables(null,null,tablename,null);
            if(rSet.next())
            {
                //分区表名。查看分区是否存在
                String partitiontable="t_hes_data_"+hescode+"_"+String.valueOf(year);
                String partsql="SELECT * FROM INFORMATION_SCHEMA.PARTITIONS WHERE TABLE_NAME = '"+ tablename+
                        "' AND PARTITION_NAME ='"+partitiontable+"'";

                //预查询
                preparedStatement= connection.prepareStatement(partsql);
                resultSet=preparedStatement.executeQuery();
                //若集合为空,则创建分区
                if(!resultSet.next())
                {
                    year=year+1;
                    partsql="ALTER table "+tablename+" PARTITION BY RANGE(YEAR(collectDt))" +
                            "( PARTITION "+partitiontable+" VALUES LESS THAN ("+year+"))";
                    //预查询
                    pStatement = connection.createStatement();
                    pStatement.executeUpdate(partsql);
                }
                pStatement = connection.createStatement();
                rNum = pStatement.executeUpdate(sql);
                Rst="OK";

            }else
            {
                Rst="Database not exist";
                System.out.println(collectdt+":换热站"+hescode+"数据表不存在XXXXXXXX！");
                return JsonResult.success(Rst);
            }
            connection.close();

        }catch (SQLException ex) {
            ex.printStackTrace();
            System.out.println(collectdt+":换热站"+hescode+"数据异常报错XXXXXXXX！");
            return JsonResult.error("ERROR");
        }
        System.out.println(collectdt+":换热站"+hescode+"数据插入成功！！！！");
        return JsonResult.success(Rst);
    }


    /*
    *
    * 接收气象站数据
    * */
    @Override
    public JsonResult getRecvWsdata(String data, HttpServletRequest request)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        String temperature="0";
        String humidity="0";
        String speed="0";
        String solar="0";
        ObjectMapper objectMapper=new ObjectMapper();
        try{
             JsonNode jsonNode=objectMapper.readTree(data);
             temperature=jsonNode.get("T").asText();
             humidity=jsonNode.get("H").asText();
             speed=jsonNode.get("S").asText();
             solar=jsonNode.get("R").asText();
        }catch (IOException e)
        {
            e.printStackTrace();
            return JsonResult.error("解析数据报错");
        }

        try {

            SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String collectdt=sdf.format(System.currentTimeMillis());

            String tablename="T_WeatherStationData";
            connection = dataSoure.getConnection();
            String sql="insert into "+tablename+" (ws,CollectDt,T,H,S,R) values('华能沣东供热站内气象站','"+collectdt+"','"
                    +temperature+"','"+humidity+"','"+speed+"','"+solar+"')";
            rSet=connection.getMetaData().getTables(null,null,tablename,null);
            if(rSet.next())
            {
                pStatement = connection.prepareStatement(sql);
                System.out.println(sql);
                int num=pStatement.executeUpdate();
                Rst="OK";

            }else
            {
                Rst="Database not exist";
            }
            connection.close();

        }catch (SQLException ex) {
            ex.printStackTrace();
            return JsonResult.error("ERROR");
        }

        return JsonResult.success(Rst);
    }



    /*
    *
    * 接收室内采集温度入库
    *
    * */

    @Override
    public JsonResult getRecvIndoorTdata(String data, HttpServletRequest request)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        String Rst="";
        JSONObject recvdata=JSONObject.parseObject(data);
        //小区编号
        String heatunitno = recvdata.getString("heatunitno");
        //热表编号
        String heatMeterNo = recvdata.getString("heatMeterNo");
        //房号
        String romNo = recvdata.getString("romNo");
        String F_S_T1 = recvdata.getString("F_S_T1");
        String F_B_T1 = recvdata.getString("F_B_T1");
        String F_B_V = recvdata.getString("F_B_V");
        String F_S_H = recvdata.getString("F_S_H");
        String F_S_F = recvdata.getString("F_S_F");
//        JSONObject recvdata = data.getJSONObject("jsondata");
        String key=recvdata.getString("md5Key");
        int    dataLen=Integer.parseInt(recvdata.getString("dataLen"));
        int    rate=recvdata.getInteger("rate");
        String  md5Key=getVerify(dataLen,rate,heatunitno);
        //字符串比对
        if(key.equals(md5Key))
        {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String collectdt = sdf.format(System.currentTimeMillis());

                String tablename = "t_indoort_data_"+heatunitno;
                connection = dataSoure.getConnection();
                String sql = "insert into " + tablename + " (collectDt,heatMeterNo,romNo,F_S_T1,F_B_T1,F_B_V,F_S_H,F_S_F) values(" + collectdt + "','"
                        + heatMeterNo + "','" + romNo + "','" + F_S_T1 + "','" + F_B_T1 + "','" + F_B_V + "','" + F_S_H + "','" + F_S_F + "')";
                rSet = connection.getMetaData().getTables(null, null, tablename, null);
                if (rSet.next()) {
                    pStatement = connection.prepareStatement(sql);
                    int num = pStatement.executeUpdate();
                    Rst = "OK";
                } else {
                    Rst = "Database not exist";
                }
                connection.close();

            } catch (SQLException ex) {
                ex.printStackTrace();
                return JsonResult.error("ERROR");
            }
        }
        return JsonResult.success(Rst);
    }



    /*
     *
     * 按照规则获取秘钥 tbkj2015+ 时*分*长度*因子 +插入站号到因子指定的位置
     * param  nCount长度   nV 因子
     *
     */
    public String getVerify(Integer nCount,Integer nV,String nStationNo)
    {
        String  md5Key=null;
        Calendar calendar=Calendar.getInstance();
        int nhour=calendar.get(Calendar.HOUR_OF_DAY);
        int nMinutes=calendar.get(Calendar.MINUTE);
        int nCalc=nhour*nMinutes*nV*nCount;
        String strCalc="tbkj2015"+String.valueOf(nCalc);
        //MD5加密  站号插入到因子指定的位置
        String strEnd=StringUtils.substring(strCalc,nV);
        String strStart=StringUtils.substring(strCalc,0,nV);
        String key=strStart+nStationNo+strEnd;
        //MD5加密
        md5Key = DigestUtils.md5Hex(key);
        return md5Key;
    }
}
