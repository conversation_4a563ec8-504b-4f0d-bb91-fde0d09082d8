package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.AlarmConfig;
import com.javaweb.system.query.AlarmConfigQuery;
import com.javaweb.system.service.IAlarmConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 * 运行配置 前端控制器
 * </p>
 *
 *
 * @since 2022-12-28
 */
@RestController
@RequestMapping("/alarmconfig")
public class AlarmConfigController extends BaseController {

    @Autowired
    private IAlarmConfigService alarmConfigService;


    /**
     * 获取报警配置列表a
     *
     * @param rulesQuery 查询条件
     * @return`
     */
    @GetMapping("/index")
    public JsonResult index(AlarmConfigQuery rulesQuery) {
        return alarmConfigService.getList(rulesQuery);
    }

    /**
     * 添加报警配置
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "报警配置", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody AlarmConfig entity) {
        return alarmConfigService.edit(entity);
    }


    /**
     * 编辑报警配置
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "报警配置", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody AlarmConfig entity) {
        return alarmConfigService.edit(entity);
    }


    /**
     * 删除配置
     *
     * @param alarmConfigIds
     * @return
     */
    @Log(title = "报警配置", logType = LogType.DELETE)
    @DeleteMapping("/delete/{alarmConfigIds}")
    public JsonResult delete(@PathVariable("alarmConfigIds") Integer[] alarmConfigIds) {
        return alarmConfigService.deleteByIds(alarmConfigIds);
    }


}
