<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.RoleMapper">

    <!-- 获取用户角色 -->
    <select id="getRolesByUserId" resultType="com.javaweb.system.entity.Role">
        SELECT r.* FROM tb_role AS r
        INNER JOIN tb_user_role AS ur ON r.id=ur.role_id
        WHERE ur.user_id=#{userId} AND r.mark=1;
    </select>

    <!-- 获取用户角色 -->
    <select id="getRoleByUserId" resultType="com.javaweb.system.entity.Role">
        SELECT * FROM tb_role  WHERE id=#{userId}
    </select>

</mapper>
