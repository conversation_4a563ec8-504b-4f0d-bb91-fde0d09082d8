
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技

// | 

// | 

// | 
// | 
// | 
// | 
// | 
//+--------------------------------------------------------------------------------------


package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.BoileParam;
import com.javaweb.system.entity.MapInfo;
import com.javaweb.system.mapper.HesRuleMapper;
import com.javaweb.system.mapper.MapInfoMapper;
import com.javaweb.system.service.IBoileParamService;
import com.javaweb.system.service.IMapInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * <p>
 *  地图信息 前端控制器
 * </p>
 *
 * 
 * @since 2020-11-03
 */
@RestController
@RequestMapping("/mapInfo")
public class MapInfoController extends BaseController {

    @Autowired
    private IMapInfoService mapInfoService;

    @Autowired
    private MapInfoMapper mapInfoMapper;


    /**
     * 添加地图信息
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "添加地图信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody MapInfo entity) {
        return mapInfoService.edit(entity);
    }


    /**
     * 编辑地图信息
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "编辑地图信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody MapInfo entity) {
        return mapInfoService.edit(entity);
    }



    /**
     * 删除职级
     *
     * @param mapIds 职级ID
     * @return
     */
    @Log(title = "删除地图信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{mapIds}")
    public JsonResult delete(@PathVariable("mapIds") Integer[] mapIds) {
        return mapInfoService.deleteByIds(mapIds);
    }

    /**
     * 获取最新运行数据
     *
     * @return
     */
    @GetMapping("/getMapInfoLst")
    public JsonResult getMapInfoLst() {
        return JsonResult.success(mapInfoMapper.getMapInfoLst());
    }


    /**
     * 获取最新运行数据
     *
     * @return
     */
    @GetMapping("/getMapInfo/{id}")
    public JsonResult getMapInfo(@PathVariable("id") Integer id) {
        return JsonResult.success(mapInfoMapper.getMapInfo(id));
    }


    /**
     * 获取最新运行数据
     *
     * @return
     */
    @GetMapping("/getMapInfoByName/{name}")
    public JsonResult getMapInfoByName(@PathVariable("name") String name) {
        return JsonResult.success(mapInfoMapper.getMapInfoByName(name));
    }

    /**
     * 获取最新运行数据
     *
     * @return
     */
    @GetMapping("/getMapInfoLstByIds/{ids}")
    public JsonResult getMapInfoLstByIds(@PathVariable("ids") Integer[] ids) {
//        System.out.println(Arrays.toString(ids));
//        String[] stringArr = Arrays.stream(ids).map(Object::toString).toArray(String[]::new);
//        System.out.println(Arrays.toString(stringArr));
        return JsonResult.success(mapInfoMapper.getMapInfoLstByIds(ids));
    }



    /**
     * 导入Excel
     *
     * @param request 网络请求
     * @return
     */
    @Log(title = "地图管理", logType = LogType.IMPORT)
    @PostMapping("/importExcel/{name}")
    public JsonResult importExcel(HttpServletRequest request, @PathVariable("name") String name) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        return mapInfoService.importExcel(request, name);
    }

}
