<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HouseValvesMapper">

    <!-- 获取最新一条数据-->
    <select id="getHouseValves" resultType="com.javaweb.system.entity.HouseValves">
        SELECT id, heatmeterno,romno FROM t_houseinfo where useheatunit_id=#{useheatunitId}
          and useheatunitfloor_id=#{useheatunitfloorId}
          and useheatunitfloorunit_id=#{useheatunitfloorunitId}  order by id asc;
    </select>

</mapper>
