package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.RepairList;
import com.javaweb.system.entity.Rules;
import com.javaweb.system.mapper.RulesMapper;
import com.javaweb.system.query.RulesQuery;
import com.javaweb.system.service.IRulesService;
import com.javaweb.system.workthread.ConfigLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class RulesServiceImpl  extends ServiceImpl<RulesMapper, Rules> implements IRulesService {

        @Autowired
        private RulesMapper rulesMapper;

        @Autowired
        ConfigLoader configLoader;

        @Override
        public JsonResult getList(RulesQuery rulesQuery) {

        // 查询条件
        QueryWrapper<Rules> queryWrapper = new QueryWrapper<>();

        // 规则名称
        if (!StringUtils.isEmpty(rulesQuery.getName())) {
            queryWrapper.like("name", rulesQuery.getName());
        }
        queryWrapper.orderByDesc("id");
        int nPage = 1;
        int nLimit = 10;

        if(null!=rulesQuery.getPage()) {
            nPage = rulesQuery.getPage();
        }
        if(null!=rulesQuery.getLimit()) {
            nLimit = rulesQuery.getLimit();
        }
        // 查询分页数据
        IPage<Rules> page = new Page<>(nPage,nLimit);
        IPage<Rules> pageData = rulesMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }



    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(Rules entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }
        Rules rules=null;
        if(StringUtils.isNotNull(entity.getId()))
        {
            rules=rulesMapper.getRulesBynameId(entity.getName(),entity.getId());
        }else
        {
            rules=rulesMapper.getRulesByname(entity.getName());
        }


        if(StringUtils.isNotNull(rules))
        {
            return JsonResult.error("规则名称已存在！");
        }
        boolean result = this.saveOrUpdate(entity);
        configLoader.loadConfigsAndRules();
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }
        /**
         * 根据用户ID删除用户
         *
         * @param ids 记录ID
         * @return
         */
        @Override
        public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0

        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }


    }
