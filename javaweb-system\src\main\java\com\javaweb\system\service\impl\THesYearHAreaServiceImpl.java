package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HesYearHArea;
import com.javaweb.system.mapper.THesYearHAreaMapper;
import com.javaweb.system.query.HesYearHAreaQuery;
import com.javaweb.system.service.THesYearHAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 15:27
 */
@Service
public class  THesYearHAreaServiceImpl  extends ServiceImpl<THesYearHAreaMapper, HesYearHArea> implements THesYearHAreaService {

    @Autowired
    THesYearHAreaMapper tHesYearHAreaMapper;


    @Override
    public JsonResult getList(BaseQuery query) {
        HesYearHAreaQuery hesYearHAreaQuery = (HesYearHAreaQuery ) query;

        QueryWrapper<HesYearHArea> queryWrapper = new QueryWrapper<>();

        if (!StringUtils.isEmpty(hesYearHAreaQuery.getHesname())) {
            queryWrapper.like("hesname", hesYearHAreaQuery.getHesname());
        }

        queryWrapper.orderByDesc("id");
        Integer THpage = 1;
        Integer THlimit = 10;
        if (hesYearHAreaQuery.getPage()!=null){
            THpage = hesYearHAreaQuery.getPage();
        }
        if (hesYearHAreaQuery.getLimit()!=null){
            THlimit =hesYearHAreaQuery.getLimit();
        }

        IPage<HesYearHArea> page = new Page<>(THpage, THlimit);
        IPage<HesYearHArea> page1 = tHesYearHAreaMapper.selectPage(page, queryWrapper);
        return JsonResult.success(page1);
    }

    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HesYearHArea entity) {
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getHareaList() {
        QueryWrapper<HesYearHArea> queryWrapper =new QueryWrapper<>();
        List<HesYearHArea> hareaList = list(queryWrapper);

        return JsonResult.success(hareaList);
    }

}

