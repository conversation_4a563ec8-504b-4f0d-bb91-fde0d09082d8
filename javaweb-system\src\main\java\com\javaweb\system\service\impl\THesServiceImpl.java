package com.javaweb.system.service.impl;


import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.HesDto;
import com.javaweb.system.dto.HesEnergyDto;
import com.javaweb.system.dto.HesEnergyHourDto;
import com.javaweb.system.entity.HesCalc;
import com.javaweb.system.entity.HesStatisticsDay;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.HesCalcMapper;
import com.javaweb.system.mapper.HesStatisticsDayMapper;
import com.javaweb.system.mapper.THesMapper;
import com.javaweb.system.mapper.THesYearHAreaMapper;
import com.javaweb.system.query.HesQuery;
import com.javaweb.system.service.THesService;
import com.javaweb.system.workthread.RedisDataOptServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 *
 *
 * @Description :
 * <AUTHOR> 监测表实现
 * @Date: 2022/12/6 9:37
 */
@Service
public class THesServiceImpl  extends ServiceImpl<THesMapper,THes> implements THesService {

    @Autowired
    THesMapper tHesMapper;

    @Autowired
    DataSource dataSource;

    @Autowired
    THesYearHAreaMapper tHesYearHAreaMapper;

    @Autowired
    HesCalcMapper hescalcMapper;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    RedisDataOptServer redisDataOptServer;
    @Autowired
    HesStatisticsDayMapper tHesStatisticsDayMapper ;

    /**
     * 获取监测站列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {

        HesQuery HesQuery = (HesQuery) query;
        // 查询条件
        QueryWrapper<THes> queryWrapper = new QueryWrapper<>();
        // 监测站名称
        if (!StringUtils.isEmpty(HesQuery.getName())) {
            queryWrapper.like("name", HesQuery.getName());
        }
        //运行模式
        if (!StringUtils.isNull(HesQuery.getRunmode())) {
            queryWrapper.eq("runmode", HesQuery.getRunmode());
        }

        if(!StringUtils.isEmpty(HesQuery.getOrderField()) && !StringUtils.isEmpty(HesQuery.getOrder()))
        {
            String  field="id";
            if(HesQuery.getOrderField().equals("hescode"))
            {
                field="hescode";
            }else  if(HesQuery.getOrderField().equals("runmode"))
            {
                field="runmode";
            }else  if(HesQuery.getOrderField().equals("isused"))
            {
                field="isused";
            }else  if(HesQuery.getOrderField().equals("isrun"))
            {
                field="isrun";
            }else  if(HesQuery.getOrderField().equals("equipmentnum"))
            {
                field="equipmentnum";
            }else  if(HesQuery.getOrderField().equals("heatingtype"))
            {
                field="heatingtype";
            }
            if(HesQuery.getOrder().equals("asc"))
            {
                queryWrapper.orderByAsc(field);

            }else if(HesQuery.getOrder().equals("desc"))
            {
                queryWrapper.orderByDesc(field);
            }else
            {
                queryWrapper.orderByDesc(field);
            }
        }


        // 查询分页数据
        IPage<THes> page = new Page<>(HesQuery.getPage(), HesQuery.getLimit());
        IPage<THes> pageData = tHesMapper.selectPage(page, queryWrapper);
        pageData.convert(x -> {
            THes heslist = Convert.convert(THes.class, x);

            // 控制算法
            if (StringUtils.isNotNull(x.getCalcMode())) {
                HesCalc hescalcInfo = hescalcMapper.selectById(x.getCalcMode());
                if (StringUtils.isNotNull(hescalcInfo)) {
                    heslist.setCalcName(hescalcInfo.getCalcDesc());
                }
            }


            return heslist;
        });
        return JsonResult.success(pageData);
    }


    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(THes entity) {
        //获得换热站最大的编号
        if (StringUtils.isNull(entity.getId()))
        {
            //获得换热站最大的编号
            Integer hescode=0;
            if(tHesMapper.getHesMaxCode() !=null)
            {
                hescode= tHesMapper.getHesMaxCode()+1;
            }else {
                hescode=1;
            }
            entity.setHescode(hescode);
            entity.setRunmode(0);
        }
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        //更新redis 中对应的数据
        redisDataOptServer.updateRedisHesById(entity);

//        redisUtils.del("HeslistDto");
//        //获取所有换热站列表
//        List<HesDto> heslist=tHesMapper.getHesList();
//        redisUtils.lSet("HeslistDto",heslist);
        return JsonResult.success();
    }



    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        //删除redis 中对应的数据
        redisDataOptServer.delRedisHesById(ids);
        return JsonResult.success("删除成功");
    }


    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult setStatus(THes entity) {
        boolean result = this.updateById(entity);
        if (!result) {
            return JsonResult.error();
        }
        //更新redis 中对应的数据
        redisDataOptServer.updateRedisHesById(entity);
        return JsonResult.success();
    }


    /**
     * 获取监测站启用站的列表
     *
     * @return
     */
    @Override
    public JsonResult getHesList()
    {
        //判断redis缓存数据是否存在
        List<THes> heslst=new ArrayList<>();
        heslst=redisDataOptServer.getRedisSaveHesList();
        if (heslst.size() <=0)
        {
            QueryWrapper<THes> queryWrapper = new QueryWrapper<>();
             heslst = list(queryWrapper);
        }

        return JsonResult.success(heslst);
    }

    //换热站信息
    public JsonResult getHesinfoList(HesQuery hesQuery){

        QueryWrapper<THes> queryWrapper = new QueryWrapper<>();
        // 换热站运行模式
        if (StringUtils.isNotNull(hesQuery.getRunmode())) {
            queryWrapper.eq("runmode", hesQuery.getRunmode());
        }
        queryWrapper.eq("isused", 1);
        int nPage = 1;
        int nLimit = 10;
        if(null!=hesQuery.getPage()) {
            nPage = hesQuery.getPage();
        }
        if(null!=hesQuery.getLimit()) {
            nLimit = hesQuery.getLimit();
        }
        // 查询分页数据
        IPage<THes> page = new Page<>(nPage,nLimit);
        IPage<THes> pageData  = tHesMapper.selectPage(page, queryWrapper);
        return JsonResult.success(pageData);
    }


    //换热站信息
    @Override
    public  JsonResult getHesStation()
    {

        Connection        connection = null;
        PreparedStatement pStatement = null;
        ResultSet         rSet = null;

        //构造返回数据，注意这里需要用LinkedHashMap
        Map<String,Object> resultMap = new LinkedHashMap<String,Object>();
        try {

            connection = dataSource.getConnection();
            String sql="select hescode,name from T_Hes where isused=1";

            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                String hescode =rSet.getString("hescode");
                Object object=rSet.getObject(2);
                resultMap.put(hescode,object);
            }
            System.out.println(resultMap);
            redisUtils.hmset("heslist",resultMap);
            connection.close();

        }catch (SQLException ex) {

        }
        return JsonResult.success("成功");
    }
    /*
     * 换热站运行算法算法
     * 参数：
     * list       字段
     * tablename  表名
     * 2023/1/31 xqt
     * */
    public  JsonResult  getHesCalc() {
        Connection connection =null;
        Statement  statement=null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet =null;
        List<String> list = new ArrayList<>();
        try {
            //连接
            connection = dataSource.getConnection();
            statement = connection.createStatement();
            //sql语句
            String sql = "select MPDesc,MPFieldDefine  from t_hecalc";
            //输出sql语句
            preparedStatement= connection.prepareStatement(sql);
            resultSet=preparedStatement.executeQuery();
            while (resultSet.next())
            {
                //添加字段
                list.add(resultSet.getString("MPFieldDefine"));
            }

            list.add(0,"collectDt");
            list.add(0,"id");
            connection.close();

        } catch (SQLException e) {
            e.printStackTrace();
        }finally {

            if (connection!=null) try {connection.close();}catch (Exception ignore) {}
        }
        return JsonResult.success("成功");
    }

    //获取换热站个数
    @Override
    public JsonResult getHesNumData()
    {
        String hashname="FD:HesTypeCount";
        Map<Object, Object> resultList=redisUtils.hmget(hashname);
        if(resultList !=null)
        {
            return JsonResult.success(resultList);
        }
        return JsonResult.error();
    }


    //获取换热站能耗
    @Override
    public JsonResult getHesEnergyQuxianData(String start_dt, String end_dt,int hescode)
    {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        List<THes>  hesList= tHesMapper.getHesByNo(hescode);
        Double heatrate=hesList.get(0).getHeatRate();
        String hesname=hesList.get(0).getName();
        Double heatingindex=hesList.get(0).getHeatingindex();
        try {
            connection = dataSource.getConnection();
            String sql = "select dt, F_S_C_H,avgT,area,energy from t_hes_statistics_day  where hescode="+hescode+" and dt >='"+start_dt+"' and  dt<='"+end_dt+"'";
            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                Double heat = Double.valueOf(rSet.getString("F_S_C_H"));
                String dt= rSet.getString("dt");
                Double area = rSet.getDouble("area");
                //能耗
                Double enery = rSet.getDouble("energy");
                Double jieneng=heatingindex-enery;
                if(enery !=0)
                {
                    //电量 kWh/m²
                    Double dian=jieneng*area*24/1000;
                    //二氧化碳
                    String CO2=String.format("%.2f",dian*0.45);
                   // String CO2=String.format("%.2f",dian*0.977);
                    //碳排放
                    String tan=String.format("%.2f",dian*2.857/8.14);
                    String energys=String.format("%.2f",enery);
                    resultMap.put("value", energys);
                    resultMap.put("dian", String.format("%.2f", dian));
                    resultMap.put("CO2", CO2);
                    resultMap.put("tan", tan);
                }else
                {
                    resultMap.put("value", 0);
                    resultMap.put("dian", 0);
                    resultMap.put("CO2", 0);
                    resultMap.put("tan", 0);
                }
                resultMap.put("heatingindex",  heatingindex);
                resultMap.put("heat",  heat);
                resultMap.put("name",  hesname);
                resultMap.put("dt", dt);
                lst.add(resultMap);
            }
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            if (connection!=null) try {connection.close();}catch (Exception ignore) {}
        }
        return JsonResult.success(lst);
    }


    //获取换热站能耗
    @Override
    public JsonResult getHesDayEnergyData(String dt) {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        List<THes> hesList = tHesMapper.getHesUsed();

        try {
            // 获取数据库连接
            connection = dataSource.getConnection();

            for (int i = 0; i < hesList.size(); i++) {
                int hescode = hesList.get(i).getHescode();
                String hesname = hesList.get(i).getName();
                Double heatingindex = hesList.get(i).getHeatingindex(); // 修正获取加热指数

                // 构建SQL查询
                String sql_a = "SELECT F_S_C_H, avgT, area, energy FROM t_hes_statistics_day WHERE hescode = ? AND dt = ? ORDER BY id DESC LIMIT 1";
                pStatement = connection.prepareStatement(sql_a);
                pStatement.setInt(1, hescode);
                pStatement.setString(2, dt);

                // 执行查询
                rSet = pStatement.executeQuery();

                if (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    Double heat = rSet.getDouble("F_S_C_H");
                    Double area = rSet.getDouble("area");
                    Double energy = rSet.getDouble("energy");

                    if (energy != 0) {
                        Double jieneng = heatingindex - energy;
                        // 电量
                        //电量 kWh/m²
                        Double dian=jieneng*area*24/1000;
                        //二氧化碳
                        String CO2=String.format("%.2f",dian*0.45);
                        // String CO2=String.format("%.2f",dian*0.977);
                        //碳排放
                        String tan=String.format("%.2f",dian*2.857/8.14);
                        //Double dian = jieneng * area / 1000 / 24;
                        // 二氧化碳
                        //String CO2 = String.format("%.2f", dian * 0.977);
                        // 碳排放
                        //String tan = String.format("%.2f", dian * 0.272);
                        String energys = String.format("%.2f", energy);

                        resultMap.put("value", CO2);
                        resultMap.put("enery", energys);
                        resultMap.put("dian", String.format("%.2f", dian));
                        resultMap.put("tan", tan);
                    } else {
                        resultMap.put("value", 0);
                        resultMap.put("enery", 0);
                        resultMap.put("dian", 0);
                        resultMap.put("tan", 0);
                    }
                    resultMap.put("heatingindex", heatingindex);
                    resultMap.put("name", hesname);
                    resultMap.put("heat", heat);
                    resultMap.put("dt", dt);
                    lst.add(resultMap);
                } else {
                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    resultMap.put("name", hesname);
                    resultMap.put("value", 0);
                    resultMap.put("enery", 0);
                    resultMap.put("heat", 0);
                    resultMap.put("dian", 0);
                    resultMap.put("tan", 0);
                    resultMap.put("dt", dt);
                    lst.add(resultMap);
                }

                // 关闭当前的ResultSet和PreparedStatement
                if (rSet != null) {
                    rSet.close();
                }
                if (pStatement != null) {
                    pStatement.close();
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e);
        } finally {
            // 关闭数据库连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }

        return JsonResult.success(lst);
    }


    /*
     * 换热站能耗统计（最新的）按照日期
     * 1.获取换热站列表
     * 2.根据时间以及编号
     *
     *
     * */
    @Override
    public JsonResult getHesEnergytjData(String startdt,String enddt)
    {
        // 获取换热站列表
        List<THes> hesList = tHesMapper.getHesUsed();
        // 取出时间段内所有日期
        List<String> dtlst = getDays(startdt, enddt);

        // 构造返回数据
        List<LinkedHashMap<Object, Object>> lst = new ArrayList<>();

        for (THes hesdata : hesList) {
            LinkedHashMap<Object, Object> energylst = new LinkedHashMap<>();
            List<HesEnergyDto> hesEnergyDtoList = tHesMapper.getHesEnergy(hesdata.getHescode(), startdt, enddt);

            List<LinkedHashMap<Object, Object>> lst2 = hesEnergyDtoList.stream()
                    .map(hesdto -> {
                        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                        resultMap.put("dt", hesdto.getDt());
                        resultMap.put("area", hesdto.getArea());
                        resultMap.put("avgT", hesdto.getAvgt());
                        resultMap.put("FSCH", hesdto.getFSCH());
                        resultMap.put("energy", hesdto.getEnergy());
                        resultMap.put("theoryenergy", hesdto.getTheoryEnergy());

                        return resultMap;
                    })
                    .collect(Collectors.toList());

            energylst.put("hescode", hesdata.getHescode());
            energylst.put("data", lst2);
            lst.add(energylst);
        }

        // 组装为前端界面显示的格式
        List<LinkedHashMap<Object, Object>> rstlst = new ArrayList<>();
        for (String dt1 : dtlst) {
            LinkedHashMap<Object, Object> resultMap1 = new LinkedHashMap<>();
            resultMap1.put("dt", dt1);
            resultMap1.put("avgt", 0);
            double FSCH = 0.0;

            for (LinkedHashMap<Object, Object> hesMap : lst) {
                String hescode =  hesMap.get("hescode").toString();
                List<LinkedHashMap<Object, Object>> heslst = (List<LinkedHashMap<Object, Object>>) hesMap.get("data");

                Optional<LinkedHashMap<Object, Object>> matchingData = heslst.stream()
                        .filter(hesdto -> compareDates(dt1, hesdto.get("dt").toString()))
                        .findFirst();

                if (matchingData.isPresent())
                {
                    LinkedHashMap<Object, Object> hesdto = matchingData.get();
                    resultMap1.put("avgt", hesdto.get("avgT"));
                    FSCH += Double.valueOf(hesdto.get("FSCH").toString());
                    resultMap1.put("harea" + hescode, hesdto.get("area"));
                    resultMap1.put("hesrl" + hescode, hesdto.get("FSCH"));
                    resultMap1.put("hesnenghao" + hescode, hesdto.get("energy"));
                    resultMap1.put("theorynenghao" + hescode, hesdto.get("theoryenergy"));
                }
            }

            String totalerl = String.format("%.2f", FSCH);
            resultMap1.put("totalerl", totalerl);
            rstlst.add(resultMap1);
        }
        return JsonResult.success(rstlst);
    }
    /*
     * 换热站能耗统计（最新的）按照小时
     * 1.获取换热站列表
     * 2.根据时间以及编号
     * */
    @Override
    public JsonResult getHesEnergytjHourData(String startdt)
    {

        List<THes> hesList = tHesMapper.getHesUsed();
        // 使用流式处理API将hescode存放到int[]中
        int[] hescodes = hesList.stream()
                .mapToInt(THes::getHescode)
                .toArray();
        List<HesEnergyHourDto> hesEnergyHourDtoList = tHesMapper.getHesHourEnergy(hescodes,startdt);
        // 组装为前端界面显示的格式
        List<LinkedHashMap<Object, Object>> rstlst = new ArrayList<>();
        int j=0;
        for (int i=0;i<24;i++)
        {
            double FSCH = 0.0;
            LinkedHashMap<Object, Object> resultMap1 = new LinkedHashMap<>();
            resultMap1.put("dt", i+"时");
            resultMap1.put("avgt", 0.0);
            for (HesEnergyHourDto hesEnergydto:hesEnergyHourDtoList)
            {
                // 假设 HesEnergyHourDto 有 getHi(int i) 方法
                String hiValue = hesEnergydto.getHi(i);
                String[] energy = hiValue.split("~");
                FSCH += Double.valueOf(energy[1]);
                resultMap1.put("h" + i + hesEnergydto.getHescode(), hiValue);
                resultMap1.put("harea"+hesEnergydto.getHescode(),+hesEnergydto.getArea());
                resultMap1.put("hesrl"+hesEnergydto.getHescode(), energy[1]);
                resultMap1.put("hesnenghao"+hesEnergydto.getHescode(), energy[2]);
                resultMap1.put("theorynenghao"+hesEnergydto.getHescode(), energy[3]);
                if(!energy[0].equals("0.0"))
                {
                    resultMap1.put("avgt", energy[0]);
                }

            }
            String totalerl = String.format("%.2f", FSCH);

            resultMap1.put("totalerl", totalerl);
            rstlst.add(resultMap1);

        }
        return JsonResult.success(rstlst);
    }

    /*
    * 换热站能耗统计
    * */
    public JsonResult getHesEnergytjDataEx(String startdt,String enddt)
    {
        //获取换热站列表
        List<THes> hesList=tHesMapper.getHesUsed();
        //构造返回数据，注意这里需要用LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        //取出时间段内所有日期
        List<String> dtlst=getDays(startdt,enddt);

        for (THes hesdata:hesList)
        {
            LinkedHashMap<Object,Object> energylst = new LinkedHashMap<>();
            List<HesEnergyDto> hesEnergyDtoList = tHesMapper.getHesEnergy(hesdata.getHescode(), startdt, enddt);
            ArrayList<LinkedHashMap<Object, Object>> lst2 = new ArrayList<>();
            Double heatrate=hesdata.getHeatRate();
            //循环单个换热站的时间段内累积流量，并计算出热量，能耗
            for (HesEnergyDto hesdto:hesEnergyDtoList)
            {
                LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                resultMap.put("dt", hesdto.getDt());
                Date dt= hesdto.getDt();
                LocalDate localDate=dt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // LocalDate date=LocalDate.parse(hesdto.getDt(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                int year=localDate.getYear();
                int mouth=localDate.getMonthValue();

                String area=tHesYearHAreaMapper.getHareaByDate(hesdata.getHescode(),year,mouth);
                Double harea=1.0;
                if(StringUtils.isNotEmpty(area))
                {
                    harea=Double.valueOf(area);
                }
                resultMap.put("area",harea);
                resultMap.put("avgT", hesdto.getAvgt());
                resultMap.put("FSCH", hesdto.getFSCH());
                Double heat = Double.valueOf(hesdto.getFSCH());
                Double energy= (heat * 1000000000) * heatrate / harea / 86400 / 10000;
                resultMap.put("energy", energy);
                lst2.add(resultMap);
            }
            energylst.put("hescode",hesdata.getHescode());
            energylst.put("data",lst2);
            lst.add(energylst);
        }

        ArrayList<LinkedHashMap<Object, Object>> rstlst = new ArrayList<>();
        //组装为前端界面显示的格式
        for (int i=0;i<dtlst.size();i++)
        {
           String dt1=dtlst.get(i).toString();
           LinkedHashMap<Object, Object> resultMap1 = new LinkedHashMap<>();
           resultMap1.put("dt",dt1);
           resultMap1.put("avgt",0);
           double FSCH=0.0;
           for (int j=0;j<lst.size();j++)
           {

               String hescode=lst.get(j).get("hescode").toString();
               List<LinkedHashMap<Object,Object>> heslst=(List<LinkedHashMap<Object,Object>>)lst.get(j).get("data");
               //单个换热站时间段内的数据
               for (int k=0;k<heslst.size();k++)
               {
                   String dt2=heslst.get(k).get("dt").toString();
                   //得到相同日期的数据
                   if(compareDates(dt1,dt2))
                   {
                       resultMap1.put("avgt",heslst.get(k).get("avgT"));
                       double harea=Double.valueOf(heslst.get(k).get("area").toString())*10000;
                       FSCH=FSCH+Double.valueOf(heslst.get(k).get("FSCH").toString());
                       resultMap1.put("harea"+hescode,String.format("%.3f",harea));
                       resultMap1.put("hesrl"+hescode,heslst.get(k).get("FSCH"));
                       resultMap1.put("hesnenghao"+hescode,String.format("%.2f",heslst.get(k).get("energy")));
                       continue;
                   }
               }
           }
            String totalerl=String.format("%.2f",FSCH);
            resultMap1.put("totalerl",totalerl);
            rstlst.add(resultMap1);
    }
        return JsonResult.success(rstlst);

    }

    /*
     *
     * 比较两个日期是否相等
     *
     * */
    public static boolean compareDates(String dateString1, String dateString2) {
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        DateFormat format2 = new SimpleDateFormat("E MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

        try {
            // 将日期字符串解析为 Date 对象
            Date date1 = format1.parse(dateString1);
            Date date2 = format2.parse(dateString2);

            // 将 Date 对象格式化为字符串，使用相同的格式进行比较
            String formattedDate1 = format1.format(date1);
            String formattedDate2 = format1.format(date2);

            // 比较两个格式化后的日期字符串是否相等
            return formattedDate1.equals(formattedDate2);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return false;
    }

    /*
    *
    * 获取两个日期之间的所有日期
    *
    * */
    public static List<String> getDays(String startTime, String endTime) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }
    @Override
    public JsonResult getCarbonAllowanceData(String startDate, String endDate, List<Integer> hescodes) {
        // 假设碳排放因子为 94.5 kgCO2/GJ
        double carbonFactor = 25;
        // 1. 查询所有日期
        List<String> dateList = getDays(startDate, endDate);
        // 2. 查询所有站点信息
        List<THes> hesList = tHesMapper.selectList(
            new QueryWrapper<THes>().in("hescode", hescodes)
        );
        Map<Integer, String> codeNameMap = hesList.stream().collect(Collectors.toMap(THes::getHescode, THes::getName));
        // 3. 查询统计表
        QueryWrapper<HesStatisticsDay> qw = new QueryWrapper<>();
        qw.between("dt", startDate, endDate);
        qw.in("hescode", hescodes);
        List<HesStatisticsDay> statList = tHesStatisticsDayMapper.selectList(qw);

        // 4. 组装数据
        List<Map<String, Object>> series = new ArrayList<>();
        List<String> legend = new ArrayList<>();
        for (Integer hescode : hescodes) {
            String name = codeNameMap.getOrDefault(hescode, String.valueOf(hescode));
            legend.add(name);
            List<Double> totalList = new ArrayList<>();
            List<Double> perAreaList = new ArrayList<>();
            List<Double> intensityList = new ArrayList<>();
            for (String dt : dateList) {
                // 找到该站该天的数据
                Optional<HesStatisticsDay> statOpt = statList.stream()
                    .filter(s -> {
                        // 统一日期格式
                        String sdt;
                        Object rawDt = s.getDt();
                        if (rawDt instanceof Date) {
                            sdt = new SimpleDateFormat("yyyy-MM-dd").format((Date) rawDt);
                        } else {
                            sdt = rawDt.toString().substring(0, 10); // 防止有时分秒
                        }
                        return s.getHescode().equals(hescode) && dt.equals(sdt);
                    })
                    .findFirst();
                double fsch = 0, area = 0;
                if (statOpt.isPresent()) {
                    fsch = Optional.ofNullable(statOpt.get().getFSCH()).orElse(0d);
                    area = Optional.ofNullable(statOpt.get().getArea()).orElse(0d);
                }
                double total = fsch * carbonFactor; // 总碳排放
                double perArea = area > 0 ? total  / area : 0; // 单位面积
                double intensity = fsch > 0 ? total  / fsch : 0; // 碳强度
                totalList.add(round2(total));
                perAreaList.add(round4(perArea));
                intensityList.add(round2(intensity));
            }
            Map<String, Object> s = new HashMap<>();
            s.put("name", name);
            s.put("total", totalList);
            s.put("perArea", perAreaList);
            s.put("intensity", intensityList);
            series.add(s);
        }

        // 统计summary和tableData
        double totalSum = 0, areaSum = 0, fschSum = 0;
        List<Map<String, Object>> tableData = new ArrayList<>();
        for (Integer hescode : hescodes) {
            String name = codeNameMap.getOrDefault(hescode, String.valueOf(hescode));
            double total = 0, area = 0, fsch = 0;
            for (HesStatisticsDay stat : statList) {
                // 统一hescode类型
                if (stat.getHescode().equals(hescode)) {
                    double f = Optional.ofNullable(stat.getFSCH()).orElse(0d);
                    double a = Optional.ofNullable(stat.getArea()).orElse(0d);
                    total += f * carbonFactor;
                    fsch += f;
                    // 取最大面积（或最后一天的面积）
                    if (a > area) area = a;
                }
            }
            double perArea = area > 0 ? total / area : 0;
            double intensity = fsch > 0 ? total / fsch : 0;
            totalSum += total;
            areaSum += area;
            fschSum += fsch;
            Map<String, Object> row = new HashMap<>();
            row.put("name", name);
            row.put("period", startDate + "~" + endDate);
            row.put("total", round2(total));
            row.put("perArea", round4(perArea));
            row.put("intensity", round2(intensity));
            row.put("area", round2(area));
            tableData.add(row);
        }
        double summaryPerArea = areaSum > 0 ? totalSum / areaSum : 0;
        double summaryIntensity = fschSum > 0 ? totalSum / fschSum : 0;
        Map<String, Object> summary = new HashMap<>();
        summary.put("total", round2(totalSum));
        summary.put("perArea", round4(summaryPerArea));
        summary.put("intensity", round2(summaryIntensity));

        // 返回结构
        Map<String, Object> result = new HashMap<>();
        result.put("xAxis", dateList);
        result.put("legend", legend);
        result.put("series", series);
        result.put("summary", summary);
        result.put("tableData", tableData);
        return JsonResult.success(result);
    }

    private double round2(double v) { return Math.round(v * 100.0) / 100.0; }
    private double round4(double v) { return Math.round(v * 10000.0) / 10000.0; }
}

