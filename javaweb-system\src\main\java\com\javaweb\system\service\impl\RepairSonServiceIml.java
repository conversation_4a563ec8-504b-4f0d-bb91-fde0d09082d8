package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.RepairSon;
import com.javaweb.system.mapper.RepairSonMapper;
import com.javaweb.system.query.RepairSonQuery;
import com.javaweb.system.service.IRepairSonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 维修列表 服务实现类
 * </p>
 *
 *
 * @since 2022-12-28
 */
@Service
public class RepairSonServiceIml extends BaseServiceImpl<RepairSonMapper, RepairSon> implements IRepairSonService {

    @Autowired
    private RepairSonMapper  repairsonMapper;


    /**
     * 获取维修列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {
        RepairSonQuery repairsonQuery = (RepairSonQuery) query;
        // 查询条件
        QueryWrapper<RepairSon> queryWrapper = new QueryWrapper<>();

        // 维修名称
        if (!StringUtils.isEmpty(repairsonQuery.getName())) {
            queryWrapper.like("name", repairsonQuery.getName());
        }

        int nPage = 1;
        int nLimit = 10;

        if(null!=repairsonQuery.getPage()) {
            nPage = repairsonQuery.getPage();
        }
        if(null!=repairsonQuery.getLimit()) {
            nLimit = repairsonQuery.getLimit();
        }
        // 查询分页数据
        IPage<RepairSon> page = new Page<>(nPage,nLimit);
        IPage<RepairSon> pageData = repairsonMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }

    /**
     * 获取维修人员列表
     *
     * @return
     */
    @Override
    public JsonResult getRepairSonList() {
        QueryWrapper<RepairSon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("state", 1);
        return JsonResult.success(list(queryWrapper));
    }
}
