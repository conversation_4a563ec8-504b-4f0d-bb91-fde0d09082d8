<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.HeatUnitFloorUnitMapper">

    <!-- 通过关联id获得小区、楼宇信息-->
    <select id="getUnitFloorUnitByLinkId" resultType="com.javaweb.system.dto.HouseInfoDto">
        SELECT
            mt.id AS id,
            c.name AS name,
            b.floorname AS floorname
        FROM
            t_useheatunitfloorunit mt
                LEFT JOIN t_useheatunit c ON mt.useheatunitid = c.id
                LEFT JOIN t_useheatunitfloor b ON mt.useheatunitfloorid = b.id

    </select>

    <!-- 通过单元信息获取个数-->
    <select id="getCountByNo" resultType="Integer">
        SELECT count(id) as num  FROM t_useheatunitfloorunit  where useheatunitid=#{useheatunitid}
          and useheatunitfloorid=#{useheatunitfloorid} and floorunitname = #{floorunitname}
        <if test="id !=null">
            and id !=#{id}
        </if>
    </select>


    <!-- 基础信息-->
    <select id="getHeatUnitFloorUnitBaseinfo" resultType="com.javaweb.system.entity.HeatUnitFloorUnit">
        SELECT id,floorunitname  FROM t_useheatunitfloorunit where useheatunitid=#{useheatunitid}
        and useheatunitfloorid=#{useheatunitfloorid} order by id asc;
    </select>


</mapper>
