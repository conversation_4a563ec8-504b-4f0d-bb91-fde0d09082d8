package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Recode;
import com.javaweb.system.query.RecodeQuery;

public interface IRecodeService extends IService<Recode> {

    /**
     * 根据查询条件获取所有数据列表
     *
     * @param recodeQuery 查询条件
     * @return
     */
    JsonResult getList(RecodeQuery recodeQuery);


    /**
     * 根据查询条件获取实时报警数据列表
     *
     * @param recodeQuery 查询条件
     * @return
     */
    JsonResult getAlarmList(RecodeQuery recodeQuery);

    /**
     * 根据ID删除记录
     *
     * @param ids 记录ID
     * @return
     */
    JsonResult deleteByIds(Integer[] ids);


    /**
     * 根据最新实时报警数据列表
     *
     * @param
     * @return
     */

    JsonResult getAlarmRecodedata();
}
