package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.HesDto;
import com.javaweb.system.dto.HesEnergyDto;
import com.javaweb.system.dto.HesEnergyHourDto;
import com.javaweb.system.entity.THes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 增删改查
 *
 * @Description :
 * <AUTHOR> mc
 * @Date: 2022/12/6 9:05
 */
public interface THesMapper extends BaseMapper<THes> {

    /**
     * 获取换热站启用站的数据
     *
     * @param
     * @return
     */
    List<THes> getHesUsed();

    /**
     * 获取换热站启用站的数据
     *
     * @param
     * @return
     */
    List<THes> getHesByNo(int hescode);

    /**
     * 获取换热站启用站的数据
     *
     * @param
     * @return
     */
    List<HesDto> getHesDtoUsed();



    /**
     * 获取换热站全部站的数据
     *
     * @param
     * @return
     */
    List<HesDto> getHesList();


    /**
     * 获取换热站全部站的数据
     *
     * @param
     * @return
     */
    List<THes> getHesAllList();

    List<HesEnergyDto> getHesEnergy(int hescode, String startdt, String enddt);



    /**
     * 获取换热站当天小时能耗
     *
     * @param
     * @return
     */
    List<HesEnergyHourDto> getHesHourEnergy(@Param("hescodes") int[] hescodes, @Param("startdt")  String startdt);

    /**
     * 获取换热站最大的编号
     *
     */
    Integer getHesMaxCode();

    Integer  getHesCode(@Param("name") String name);

    String getHesName(int hescode);

    THes selectByCode(int hescode);

}

