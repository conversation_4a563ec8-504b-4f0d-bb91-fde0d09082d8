package com.javaweb.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.Weather;
import com.javaweb.system.query.WeatherQuery;

import java.util.List;


public interface IWeatherService  extends IService<Weather> {

    /**
     * 根据查询条件获取数据列表
     *
     * @param weatherQuery 查询条件
     * @return
     */
    JsonResult getList(WeatherQuery weatherQuery);


    /**
     * 获取所有气象站数据
     *
     * @return
     */
    JsonResult getWeatherAll();


    /**
     * 获取选择日期气象站数据
     * @param dt  日期
     * @return
     */

    JsonResult getWeatherDate(String dt);

    /**
     * 获取选择日期气象站数据  平均每小时
     * @param dt  日期
     * @return
     */
    JsonResult getWeatherHourAvgData(String dt);


    /**
     * 获取选择日期气象站数据最高最低气温，以及平均气温
     * @param dt  日期
     * @return
     */
    JsonResult getWeatherDayMinMax(String dt);

    /**
     * 获取选择日期气象站  平均每天
     * @param
     * @return
     */
    JsonResult getWeatherDayAvgData(String startdt,String enddt);

}
