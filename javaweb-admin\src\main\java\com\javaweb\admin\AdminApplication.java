

package com.javaweb.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = {"com.javaweb.*"})
@MapperScan("com.javaweb.**.mapper")
@EnableTransactionManagement
// 开启定时任务支持
@EnableScheduling
//该注解会扫描相应的包
@ServletComponentScan
@EnableAsync
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  智慧综合能源平台启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}


