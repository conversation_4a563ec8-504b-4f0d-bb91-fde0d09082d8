package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.HeatUnit;
import com.javaweb.system.mapper.HeatUnitMapper;
import com.javaweb.system.query.UseHeatUnitQuery;
import com.javaweb.system.service.IHeatUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/12 15:40
 */
@RestController
@RequestMapping("/heatunit")
public class HeatUnitController extends BaseController {

    @Autowired
    private IHeatUnitService IHeatUnitService;

    @Autowired
    private HeatUnitMapper heatUnitMapper;
    /**
     * 获取查询列表
     * @param useHeatUnitQuery
     * @return
     */
    //@RequiresPermissions("sys:heatunit:index")
    @GetMapping("/index")
    public JsonResult index(UseHeatUnitQuery useHeatUnitQuery) {
        return IHeatUnitService.getList(useHeatUnitQuery);
    }


    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "小区信息", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody HeatUnit entity){

        return IHeatUnitService.edit(entity);
    }

    @Log(title = "小区信息", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody HeatUnit entity){

        return IHeatUnitService.edit(entity);
    }

    @Log(title = "小区信息", logType = LogType.DELETE)
    @DeleteMapping("/delete/{useHeatUnitIds}")
    public JsonResult delete(@PathVariable("useHeatUnitIds") Integer[]useHeatUnitIds){

        return IHeatUnitService.deleteByIds(useHeatUnitIds);
    }

    @GetMapping("/getHeatunitList")
    public JsonResult getHeatunitList(){
        return IHeatUnitService.getHeatunitList();
    }


    @GetMapping("creatTable/{unitNo}")
    public JsonResult creatTable(@PathVariable String unitNo)
    {
        return IHeatUnitService.creatDataBaseTable(unitNo);
    }

    //获得基本信息  id 名称
    @GetMapping("/getHeatUnitBaseinfo")
    public JsonResult getHeatUnitBaseinfo()
    {
        return JsonResult.success(heatUnitMapper.getHeatUnitBaseinfo());
    }


}

