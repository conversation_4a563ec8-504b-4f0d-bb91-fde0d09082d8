package com.javaweb.system.service.impl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.HesMpDictionary;
import com.javaweb.system.entity.THes;
import com.javaweb.system.mapper.HesMpDictionaryMapper;
import com.javaweb.system.query.HesMpDictionaryQuery;
import com.javaweb.system.service.THesMpDictionaryService;
import com.javaweb.system.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/16 17:28
 */
@Service
public class THesMpDictionaryServiceImpl extends ServiceImpl<HesMpDictionaryMapper, HesMpDictionary> implements THesMpDictionaryService {

    @Autowired
    private HesMpDictionaryMapper hesMpDictionaryMapper;

    @Autowired
    RedisUtils redisUtils;


    @Override
    public JsonResult getList(BaseQuery query){

        HesMpDictionaryQuery hesMpDictionaryQuery = (HesMpDictionaryQuery) query;

        //查询条件
        QueryWrapper<HesMpDictionary> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(hesMpDictionaryQuery.getMpfield())){
            queryWrapper.like("MPField",hesMpDictionaryQuery.getMpfield());
        }
        queryWrapper.orderByDesc("id");
        Integer npage = 1;
        Integer nlimit = 10;
        if (hesMpDictionaryQuery.getPage()!=null){
            npage = hesMpDictionaryQuery.getPage();
        }
        if (hesMpDictionaryQuery.getLimit()!=null){
            nlimit = hesMpDictionaryQuery.getLimit();
        }
        IPage<HesMpDictionary> page = new Page<>(npage,nlimit);
        IPage<HesMpDictionary> page1 = hesMpDictionaryMapper.selectPage(page,queryWrapper);
        for(HesMpDictionary hp:page1.getRecords()){
            if(StringUtils.equals(hp.getMpvaluetype(),"float")){
                hp.setMpvaluetype("浮点型");
            } else if(StringUtils.equals(hp.getMpvaluetype(),"int")){
                hp.setMpvaluetype("整数型");
            }
        }

        return JsonResult.success(page1);


    }


    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(HesMpDictionary entity) {
        boolean result = this.saveOrUpdate(entity);
        if (!result) {
            return JsonResult.error();
        }
        //获取测点库字段列表
        List<HesMpDictionary> hesfieldlist=hesMpDictionaryMapper.getDictionaryList();
        redisUtils.lSet("HesFieldList",hesfieldlist);
        return JsonResult.success();
    }


    /**
     * 删除
     *
     * @param  ids
     * @return
     */
    @Override
    public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        //获取测点库字段列表
        List<HesMpDictionary> hesfieldlist=hesMpDictionaryMapper.getDictionaryList();
        redisUtils.lSet("HesFieldList",hesfieldlist);
        return JsonResult.success("删除成功");
    }



    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult setStatus(HesMpDictionary entity) {
        boolean result = this.updateById(entity);
        if (!result) {
            return JsonResult.error();
        }

        //获取测点库字段列表
        List<HesMpDictionary> hesfieldlist=hesMpDictionaryMapper.getDictionaryList();
        redisUtils.lSet("HesFieldList",hesfieldlist);
        return JsonResult.success();
    }


    @Override
    public JsonResult getHesMpDictionaryList() {

        //取出redis缓存数据
        List<Object> HesFieldList =redisUtils.lGet("HesFieldList",0,-1);
        //判断redis缓存数据是否存在
        //若存在，则返回redis
        if (StringUtils.isNotNull(HesFieldList))
        {
            List<HesMpDictionary> rstLst = new ArrayList<>();
            for(int i=0;i<HesFieldList.size();i++)
            {
                Object ob = HesFieldList.get(i);
                rstLst =  JSON.parseArray(JSON.toJSONString(ob),HesMpDictionary.class);
            }
            return JsonResult.success(rstLst);
        }else
        {
            QueryWrapper<HesMpDictionary> queryWrapper = new QueryWrapper<>();
            List<HesMpDictionary> list = list(queryWrapper);
            return JsonResult.success(list);
        }

    }
}

