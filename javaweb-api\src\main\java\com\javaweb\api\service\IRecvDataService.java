package com.javaweb.api.service;


import com.alibaba.fastjson.JSONObject;
import com.javaweb.common.utils.JsonResult;

import javax.servlet.http.HttpServletRequest;

public interface IRecvDataService {

    /**
     * 获取验证码
     *
     * @param request 请求响应
     * @return
     */
//    JsonResult getRecvdata(HttpServletResponse response);

    JsonResult getRecvdata(JSONObject data, HttpServletRequest request);


    /**
     * 接收换热站数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvHesdata(String data, HttpServletRequest request);


    /**
     * 接收气象站数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvWsdata(String data, HttpServletRequest request);

    /**
     * 接收室内采集数据
     *
     * @param request 请求响应
     * @return
     */
    JsonResult getRecvIndoorTdata(String data, HttpServletRequest request);


    /**
     * 获取前端程序版本号
     *
     */
    String  getHesVersion();

    /**
     * 获取换热站参数
     *
     */
    String  getHesParam(Integer hescode);


    /**
     * 获取换热站状态
     *
     */
    String getHesState(Integer hescode);

}
