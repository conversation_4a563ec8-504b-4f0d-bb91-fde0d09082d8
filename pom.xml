<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.javaweb</groupId>
    <artifactId>javaweb</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <!--父模块打包类型必须为pom-->
    <packaging>pom</packaging>
    <name>javaweb</name>
    <description>前后端分离框架</description>

    <!-- 子模块依赖 -->
    <modules>
        <module>javaweb-common</module>
        <module>javaweb-system</module>
        <module>javaweb-generator</module>
        <module>javaweb-admin</module>
        <module>javaweb-api</module>
        <module>javaweb-calculate</module>
        <module>javaweb-control</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <!-- 表示打包时跳过mvn test -->
        <maven.test.skip>true</maven.test.skip>
        <!--全局配置项目版本号-->
        <version>0.0.1-SNAPSHOT</version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- 子模块依赖 -->
            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-common</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-generator</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-system</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-admin</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-api</artifactId>
                <version>${version}</version>
            </dependency>

            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-calculate</artifactId>
                <version>${version}</version>
            </dependency>

            <dependency>
                <groupId>com.javaweb</groupId>
                <artifactId>javaweb-control</artifactId>
                <version>${version}</version>
            </dependency>

            <!-- 第三方依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.10</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.76</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

</project>
