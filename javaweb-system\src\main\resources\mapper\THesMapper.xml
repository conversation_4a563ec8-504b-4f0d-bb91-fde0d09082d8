<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.THesMapper">

    <!-- 获取换热站信息 -->
    <select id="getHesUsed" resultType="com.javaweb.system.entity.THes">
        SELECT hescode,Name,UseHeatUnit_name,heat_rate,heatingindex FROM t_hes where isused=1;
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesName" resultType="String">
        SELECT Name FROM t_hes where hescode=#{hescode};
    </select>
    <!-- 获取换热站信息 -->
    <select id="getHesCode" resultType="Integer">
        SELECT hescode FROM t_hes where name=#{name};
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesByNo" resultType="com.javaweb.system.entity.THes">
        SELECT hescode,Name,UseHeatUnit_name,heat_rate,heatingindex FROM t_hes where hescode=#{hescode};
    </select>


    <!-- 获取换热站信息 -->
    <select id="selectByCode" resultType="com.javaweb.system.entity.THes">
        SELECT hescode,Name FROM t_hes where hescode=#{hescode};
    </select>


    <!-- 获取换热站信息 -->
    <select id="getHesDtoUsed" resultType="com.javaweb.system.dto.HesDto">
        SELECT  id,hescode,Name FROM t_hes where isused=1;
    </select>

    <select id="getHesAllList" resultType="com.javaweb.system.entity.THes">
        SELECT * FROM t_hes ;
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesList" resultType="com.javaweb.system.dto.HesDto">
        SELECT  id,hescode,Name,isused,isrun FROM t_hes;
    </select>

    <!-- 获取换热站信息 -->
    <select id="getHesEnergy" resultType="com.javaweb.system.dto.HesEnergyDto">
        SELECT  dt, F_S_C_H,avgT,area,energy,theory_energy from t_hes_statistics_day where hescode=#{hescode} and dt &lt;= #{enddt}  and  dt>= #{startdt}
       order by id desc;
    </select>

    <select id="getHesHourEnergy" resultType="com.javaweb.system.dto.HesEnergyHourDto">
        SELECT  * from t_hes_statistics_hour where dt = #{startdt}  and hescode in
        <foreach collection="hescodes" item="item" index="index" open="(" separator="," close=")" >
            (#{item})
        </foreach>
    </select>



    <!-- 获取换热站最大的编号 -->
    <select id="getHesMaxCode" resultType="Integer">
        SELECT  max(hescode) as hescode FROM t_hes;
    </select>

</mapper>
