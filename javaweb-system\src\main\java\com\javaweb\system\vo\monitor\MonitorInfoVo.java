package com.javaweb.system.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.javaweb.common.annotation.Excel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * @Date:
 */
@Data
public class MonitorInfoVo {

    /**
     * 测点ID
     */
    @Excel(name = "测点ID", cellType = Excel.ColumnType.NUMERIC)
    private Integer id;

    /**
     * 站编号
     */
    @Excel(name = "站编号")
    private Integer hescode;

    /**
     * 测点名称
     */
    @Excel(name = "测点名称")
    private String mpdesc;

    /**
     * 测点定义
     */
    @Excel(name = "测点定义")
    private String mpfielddefine;

    /**
     * 控制状态：1不控制 2控制
     */
    @Excel(name = "控制状态", readConverterExp = "0=否,1=是")
    private Integer iscontrol;

    /**
     * 报警状态：0正常 2报警
     */
    @Excel(name = "报警状态", readConverterExp = "0=正常,1=报警")
    private Integer isbaojing;

    /**
     * 启用状态：0未启用 1启用
     */
    @Excel(name = "启用状态", readConverterExp = "0=否,1=是")
    private Integer isused;


}

