package com.javaweb.system.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.javaweb.common.common.BaseQuery;
import com.javaweb.common.common.BaseServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.RedisUtils;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.dto.MpdictDto;
import com.javaweb.system.entity.HistoryData;
import com.javaweb.system.entity.HousePayInfo;
import com.javaweb.system.mapper.HistoryMapper;
import com.javaweb.system.query.HistoryQuery;
import com.javaweb.system.service.IHistoryService;
import org.apache.shiro.authc.Account;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.sql.DataSource;
import java.sql.*;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * <p>
 * 历史数据 服务实现类
 * </p>
 *
 *
 * @since 2020-11-07
 */

@Service
public class  HisoryServiceImpl  extends BaseServiceImpl<HistoryMapper, HistoryData>  implements IHistoryService {

    @Autowired
    private HistoryMapper historyMapper;


    @Autowired
    DataSource dataSoure;

    @Autowired
    RedisUtils redisUtils;

    /**
     * 获取配置测点列表
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public JsonResult getList(BaseQuery query) {

        HistoryQuery historyQuery = (HistoryQuery) query;
        // 查询条件
        QueryWrapper<HistoryData> queryWrapper = new QueryWrapper<>();

        // 换热站编号
        int hescode = 7;
        if (StringUtils.isNotNull(historyQuery.getHescode())) {
            hescode = historyQuery.getHescode();
        }
        //数据表名
        String tableName = "t_hes_data_" + String.valueOf(hescode);
        List<HistoryData> historyList = historyMapper.getHistoryStation(tableName);
        return JsonResult.success(historyList);
    }

    /*
     * 得到换热站的历史数据 当天
     *
     * */
    @Override
    public JsonResult getHistoryDt(String hescode, String dt) {
        // 动态生成表名
        String tableName = "t_hes_data_" + hescode;
        // 用于存储结果的列表
        ArrayList<LinkedHashMap<String, String>> lst = new ArrayList<>();
        //System.out.println("=========接收到的hescode="+hescode+",dt="+dt+"=======");
        try (Connection connection = dataSoure.getConnection()) {
            // 构建查询语句
            String sql = "SELECT * FROM " + tableName + " WHERE CollectDT >= ? AND CollectDT <= ? ORDER BY id DESC";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                String startOfDay = dt + " 00:00:00";
                String endOfDay = dt + " 23:59:59";
                pStatement.setString(1, startOfDay);
                pStatement.setString(2, endOfDay);

                try (ResultSet rSet = pStatement.executeQuery()) {
                    ResultSetMetaData metaData = rSet.getMetaData();
                    int columnCount = metaData.getColumnCount();

                    // 获取所有列名
                    List<String> columnNames = new ArrayList<>();
                    for (int i = 1; i <= columnCount; i++) {
                        columnNames.add(metaData.getColumnLabel(i));
                    }
                    // 处理每一行数据
                    while (rSet.next()) {
                        LinkedHashMap<String, String> rowMap = new LinkedHashMap<>();
                        for (String columnName : columnNames) {
                            String value = rSet.getString(columnName);
                            if (!columnName.equals("CollectDT") && !columnName.equals("id")) {
                                if (value != null) {
                                    value = String.format("%.2f", Double.parseDouble(value));
                                }
                            }
                            rowMap.put(columnName, value);
                        }
                        lst.add(rowMap);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        } catch (DateTimeParseException e) {
            e.printStackTrace();
            return JsonResult.error("日期格式不正确");
        }

        return JsonResult.success(lst);
    }


    /*
     * 得到换热站的历史数据 当天
     *
     * */
    @Override
    public JsonResult getHistoryquxianDt(String hescode, String dt, String field) {
        String[] mpfield = field.split(",");
        // 动态生成表名
        String tableName = "t_hes_data_" + hescode;
        ResultSet rSet = null;
        // 用于存储结果的列表
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        try (Connection connection = dataSoure.getConnection()) {
            // 构建查询语句
            String sql = "SELECT CollectDT," + field + " FROM " + tableName + " WHERE CollectDT >= ? AND CollectDT <= ? ORDER BY id asc";
            try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                String startOfDay = dt + " 00:00:00";
                String endOfDay = dt + " 23:59:59";
                pStatement.setString(1, startOfDay);
                pStatement.setString(2, endOfDay);
                rSet = pStatement.executeQuery();
                while (rSet.next()) {

                    LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                    String collectdt = rSet.getString("collectdt");
                    resultMap.put("collectdt", collectdt);
                    for (String mp : mpfield) {
                        String val = rSet.getString(mp);
                        resultMap.put(mp, val);
                    }
                    lst.add(resultMap);
                }

            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        }

        return JsonResult.success(lst);
    }


    /*
     * 得到多个换热站的历史数据曲线 当天
     *
     * */
    @Override
    public JsonResult getMultHesHistoryQuxianDt(String hescode, String startDt,String endDt, Integer interval,String field) {
        // 创建统一时间序列
        List<String> timeSeries = createTimeSeries(startDt,endDt,interval);
        String[] mpfield = field.split(",");
        String[] hescodes = hescode.split(",");

        // 存储最终结果，使用LinkedHashMap来保持插入顺序
        LinkedHashMap<String, ArrayList<LinkedHashMap<Object, Object>>> finalResultMap = new LinkedHashMap<>();

        ResultSet rSet = null;
        try (Connection connection = dataSoure.getConnection()) {
            for (String code : hescodes) {
                // 动态生成表名
                String tableName = "t_hes_data_" + code;
                ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
                // 构建查询语句
                //String sql = "SELECT CollectDT," + field + " FROM " + tableName + " WHERE CollectDT >= ? AND CollectDT <= ? ORDER BY id asc";
                String sql = "SELECT " +
                        "CONCAT(DATE_FORMAT(CollectDT, '%Y-%m-%d %H:')," +
                        "LPAD(FLOOR(MINUTE(CollectDT) / "+interval+") * "+interval+", 2, '0'),':00') AS interval_start,  " +
                        "MIN(id) AS id, " +
                        field + " " +
                        "FROM " + tableName + " " +
                        "WHERE CollectDT >= ? AND CollectDT <= ? " +
                        "GROUP BY interval_start " +
                        "ORDER BY interval_start ASC";
                try (PreparedStatement pStatement = connection.prepareStatement(sql)) {
                    String startOfDay = startDt;
                    String endOfDay = endDt;
                    pStatement.setString(1, startOfDay);
                    pStatement.setString(2, endOfDay);
                    rSet = pStatement.executeQuery();

                    while (rSet.next()) {
                        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
                        String collectdt = rSet.getString("interval_start");
                        resultMap.put("collectdt", collectdt);
                        for (String mp : mpfield) {
                            String val = rSet.getString(mp);
                            resultMap.put(mp, val);
                        }
                        lst.add(resultMap);
                    }
                }

                // 使用哈希表来加速查找
                Map<String, LinkedHashMap<Object, Object>> stationDataMap = new HashMap<>();
                for (LinkedHashMap<Object, Object> dataPoint : lst) {
                    stationDataMap.put((String) dataPoint.get("collectdt"), dataPoint);
                }

                // 将站点数据与时间序列进行左连接
                ArrayList<LinkedHashMap<Object, Object>> finalResultForCode = new ArrayList<>();
                for (String ts : timeSeries) {
                    LinkedHashMap<Object, Object> dataPoint = stationDataMap.get(ts);
                    if (dataPoint == null) {
                        dataPoint = new LinkedHashMap<>();
                        dataPoint.put("collectdt", ts);
//                        dataPoint.put("hescode", code);
                        for (String mp : mpfield) {
                            dataPoint.put(mp, ""); // 空值或特定标志
                        }
                    }
                    finalResultForCode.add(dataPoint);
                }

                // 添加到最终结果映射中
                finalResultMap.put(code, finalResultForCode);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return JsonResult.error(e.getMessage());
        }
        System.out.println(finalResultMap);
        // 返回最终结果
        return JsonResult.success(finalResultMap);
    }

    // 创建统一时间序列
    private List<String> createTimeSeries(String startDt,String endDt,Integer interval) {
        List<String> timeSeries = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将输入的日期字符串转换为 LocalDateTime
        LocalDateTime startOfDay = LocalDateTime.parse(startDt, formatter);
        LocalDateTime endOfDay = LocalDateTime.parse(endDt, formatter);

        // 每5分钟一个时间点
        Duration step = Duration.ofMinutes(interval);

        LocalDateTime currentTime = startOfDay;
        while (!currentTime.isAfter(endOfDay)) {
            timeSeries.add(currentTime.format(formatter));
            currentTime = currentTime.plus(step);
        }

        return timeSeries;
    }

    /*
    *
    * 获取换热站的配置参数
    * */
    @Override
    public JsonResult getHesFieldParam(int hescode) {
        // 构造返回数据，注意这里需要用 LinkedHashMap
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        String hesfield = "hesfield:" + String.valueOf(hescode);

        List<Object> hesFieldData = redisUtils.lGet(hesfield, 0, -1);

        if (StringUtils.isNotNull(hesFieldData)) {
            // 从 Redis 中获取数据
            for (Object ob : hesFieldData) {
                List<MpdictDto> rstLst = JSON.parseArray(JSON.toJSONString(ob), MpdictDto.class);
                for (MpdictDto mpdictDto : rstLst) {
                    LinkedHashMap<Object, Object> resultMap = createResultMap(mpdictDto);
                    lst.add(resultMap);
                }
            }
        } else {
            // 从数据库中获取数据
            lst = fetchFromDatabase(hescode);
        }
        return JsonResult.success(lst);
    }

    // 从数据库中获取数据
    private ArrayList<LinkedHashMap<Object, Object>> fetchFromDatabase(int hescode) {
        ArrayList<LinkedHashMap<Object, Object>> lst = new ArrayList<>();
        String sql="SELECT MPDesc, MPFieldDefine FROM T_HesMonitorPointDefine WHERE HesCode = ? ";
        try (Connection connection = dataSoure.getConnection();
             PreparedStatement pStatement = connection.prepareStatement(sql)) {

            pStatement.setInt(1, hescode);
            try (ResultSet rSet = pStatement.executeQuery()) {
                while (rSet.next()) {
                    LinkedHashMap<Object, Object> resultMap = createResultMap(rSet.getString("MPDesc"), rSet.getString("MPFieldDefine"));
                    lst.add(resultMap);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            // 可以在这里记录日志或抛出自定义异常
        }

        return lst;
    }

    // 创建结果映射
    private LinkedHashMap<Object, Object> createResultMap(MpdictDto mpdictDto) {
        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("title", mpdictDto.getMpdesc());
        resultMap.put("dataIndex", mpdictDto.getMpfielddefine());
        return resultMap;
    }

    // 创建结果映射
    private LinkedHashMap<Object, Object> createResultMap(String mpDesc, String mpFieldDefine) {
        LinkedHashMap<Object, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("title", mpDesc);
        resultMap.put("dataIndex", mpFieldDefine);
        return resultMap;
    }

    /*
    *
    * 获取换热站历史数据数据
    * */
    @Override
    public JsonResult getheslist(int hescode) {
        // 构造返回数据，注意这里需要用 LinkedHashMap
        Map<String, String> resultMap = new LinkedHashMap<>();
        ArrayList<String> fieldList = new ArrayList<>();
        ArrayList<LinkedHashMap<Object, Object>> resultList = new ArrayList<>();

        try (Connection connection = dataSoure.getConnection()) {
            // 获取监控点定义
            String mpDefineSql = "SELECT MPDesc, MPFieldDefine FROM T_HesMonitorPointDefine WHERE HesCode = ?";
            try (PreparedStatement mpDefineStatement = connection.prepareStatement(mpDefineSql)) {
                mpDefineStatement.setInt(1, hescode);
                try (ResultSet mpDefineResult = mpDefineStatement.executeQuery()) {
                    while (mpDefineResult.next()) {
                        String mpDesc = mpDefineResult.getString("MPDesc");
                        String mpFieldDefine = mpDefineResult.getString("MPFieldDefine");
                        resultMap.put(mpDesc, mpFieldDefine);
                        fieldList.add(mpFieldDefine);
                    }
                }
            }

            // 检查表是否存在
            String tableName = "t_hes_data_" + String.valueOf(hescode);
                // 构建查询字段
                StringBuilder fieldsBuilder = new StringBuilder();
                for (String field : fieldList) {
                    if (fieldsBuilder.length() > 0) {
                        fieldsBuilder.append(", ");
                    }
                    fieldsBuilder.append(field);
                }

                // 查询数据
                String dataSql = "SELECT id, CollectDt, " + fieldsBuilder.toString() + " FROM " + tableName + " ORDER BY id DESC";
                try (PreparedStatement dataStatement = connection.prepareStatement(dataSql)) {
                    try (ResultSet dataResult = dataStatement.executeQuery()) {
                        ResultSetMetaData metaData = dataResult.getMetaData();
                        int columnCount = metaData.getColumnCount();

                        // 获取所有列名
                        List<String> columnNames = new ArrayList<>();
                        for (int i = 1; i <= columnCount; i++) {
                            columnNames.add(metaData.getColumnLabel(i));
                        }

                        // 处理每一行数据
                        while (dataResult.next()) {
                            LinkedHashMap<Object, Object> rowMap = new LinkedHashMap<>();
                            for (String columnName : columnNames) {
                                Object value = dataResult.getObject(columnName);
                                rowMap.put(columnName, value);
                            }
                            resultList.add(rowMap);
                        }
                    }
            }

        } catch (SQLException e) {
            e.printStackTrace();
            // 可以在这里记录日志或抛出自定义异常
        }

        return JsonResult.success(resultList);
    }


}
