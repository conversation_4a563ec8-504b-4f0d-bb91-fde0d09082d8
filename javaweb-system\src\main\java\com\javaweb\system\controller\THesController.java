package com.javaweb.system.controller;

import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.THes;
import com.javaweb.system.service.THesService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.javaweb.system.query.HesQuery;
import com.javaweb.common.enums.LogType;

import java.util.List;
import java.util.Map;

/**
 *
 *
 * @Description :
 * <AUTHOR>
 * @Date: 2022/12/6 14:46
 */
@RestController
@RequestMapping("/hes")
public class THesController extends BaseController {
    @Autowired
    private  THesService tHesService;

    /**
     * 获取查询列表
     * @return
     */
    @GetMapping("/index")
    public JsonResult index(HesQ<PERSON><PERSON>) {
        return tHesService.getList(HesQuery);
    }

    /**
     * 增加
     * @param entity
     * @return
     */
    @Log(title = "监测站管理", logType = LogType.INSERT)
    @PostMapping("/add")
    public JsonResult add(@RequestBody THes entity) {
        return tHesService.edit(entity);
    }

    /**
     * 编辑
     * @param entity
     * @return
     */
    @Log(title = "监测站管理", logType = LogType.UPDATE)
    @PutMapping("/edit")
    public JsonResult edit(@RequestBody THes entity) {
        return tHesService.edit(entity);
    }

    /**
     * 删除
     * @param hesIds
     * @return
     */
    @Log(title = "监测站管理", logType = LogType.DELETE)
    @DeleteMapping("/delete/{hesIds}")
    public JsonResult delete(@PathVariable("hesIds") Integer[] hesIds) {
        return tHesService.deleteByIds(hesIds);
    }


    /**
     * 设置运行状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "测点配置", logType = LogType.STATUS)
    @PutMapping("/runstatus")
    public JsonResult bjstate(@RequestBody THes entity) {
        return tHesService.setStatus(entity);
    }

    /**
     * 设置状态
     *
     * @param entity 实体对象
     * @return
     */
    @Log(title = "测点配置", logType = LogType.STATUS)
    @PutMapping("/usedstatus")
    public JsonResult showstatus(@RequestBody THes entity) {
        return tHesService.setStatus(entity);
    }


    /**
     * 获取监测站列表
     * @return
     */
    @GetMapping("/getHesList")
    public JsonResult getHesList(){
        return tHesService.getHesList();
    }


    /**
     * 获取监测站列表
     * @return
     */

    @GetMapping("/getHesInfoList")
    public JsonResult getHesInfoList(HesQuery HesQuery){
        return tHesService.getHesinfoList(HesQuery);
    }

    /**
     * 获取监测站类型分布数量
     * @return
     */

    @GetMapping("/getHesNum")
    public JsonResult getHesNum(){
        return tHesService.getHesNumData();
    }

    /**
     * 获取监测站能耗曲线
     * @return
     */

    @GetMapping("/getHesEnergyQuxian/{startdt}/{enddt}/{hescode}")
    public JsonResult getHesEnergyQuxian(@PathVariable("startdt") String startdt,
                                         @PathVariable("enddt")   String enddt,
                                         @PathVariable("hescode") int    hescode)
    {
        return tHesService.getHesEnergyQuxianData(startdt,enddt,hescode);
    }

    /**
     * 获取监测站日能耗曲线
     * @return
     */
    @GetMapping("/getHesDayEnergyQuxian/{dt}")
    public JsonResult getHesDayEnergyQuxian(@PathVariable("dt") String dt)
    {
        return tHesService.getHesDayEnergyData(dt);
    }
    
 /**
     * 获取碳指标分析数据
     * @param params 包含 startDate, endDate, hescodes
     * @return
     */
    @PostMapping("/getCarbonAllowanceData")
    public JsonResult getCarbonAllowanceData(@RequestBody Map<String, Object> params) {
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        List<Integer> hescodes = (List<Integer>) params.get("hescodes");
        return tHesService.getCarbonAllowanceData(startDate, endDate, hescodes);
    }


    @GetMapping("/ceshi")
    public JsonResult ceshi() {
        return tHesService.getHesStation();
    }
}