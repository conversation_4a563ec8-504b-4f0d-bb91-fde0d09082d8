package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.dto.MonitorDto;
import com.javaweb.system.dto.MonitorPointDefineDto;
import com.javaweb.system.entity.HesMonitorPointDeFine;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * @Date: 2022/12/16 16:03
 */
public interface HesMonitorPointDeFineMapper  extends BaseMapper<HesMonitorPointDeFine> {


    List<HesMonitorPointDeFine> selectDictDataByHescode(Integer hescode);

    List<MonitorDto> getDictDataByHescode(Integer hescode);

    List<MonitorPointDefineDto> getMonitorPointDeFineByHescode(@Param("Ids") Integer[] Ids, @Param("hescode") Integer hescode);


    List<MonitorDto>  getDictDataAllList();

    void delFieldDefine(Integer hescode);


    void insertFieldDefineByhescode(@Param("hescode") Integer hescode,@Param("MPFieldDefine") String MPFieldDefine);

}

