package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.MapInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MapInfoMapper extends BaseMapper<MapInfo> {

    //获得全部地图标记信息
    List<MapInfo> getMapInfoLst();

    //根据id获得标记信息
    MapInfo getMapInfo(Integer id);

    //根据名字获得标记信息
    MapInfo getMapInfoByName(String name);


    /*
    * 根据ids 获得标记信息
    * @Param  这个注解必须带有
    * */
    List<MapInfo> getMapInfoLstByIds(@Param("Ids")  Integer[] Ids);
}



