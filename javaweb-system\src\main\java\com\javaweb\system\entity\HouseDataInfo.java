package com.javaweb.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class HouseDataInfo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectdt;

    /**
     * 热表编号
     */
    private String heatmeterno;

    /**
     * 阀门编号
     */
    private String valvesno;

    /**
     * 房号
     */
    private Integer romno;

    /**
     * 供水温度
     */
    private String fST1;

    /**
     * 回水温度
     */
    private String fBT1;



    /**
     * 热量
     */
    private String fSH;


    /**
     * 流量
     */
    private String fSF;


    /**
     * 累积热量
     */
    private String fSCH;

    /**
     * 累积流量
     */
    private String fSCF;

}
