
// | 前后端分离旗舰版框架

// | 版权所有 2022 旲博科技
//+--------------------------------------------------------------------------------------


package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.Role;
import com.javaweb.system.entity.UserRole;

import java.util.List;

/**
 * <p>
 * 人员角色表 Mapper 接口
 * </p>
 *
 */
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID获取角色
     *
     * @param userId 用户ID
     * @return
     */
    List<Role> getRolesByUserId(Integer userId);

}
