<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.RunRulesMapper">

    <select id="getRunRulesLst" resultType="com.javaweb.system.entity.RunRules">
        SELECT  * FROM t_runrules  order by id asc;
    </select>

    <select id="getRunRulesByname" resultType="com.javaweb.system.entity.RunRules">
        SELECT  * FROM t_runrules where name =#{name}  limit 1;
    </select>


    <select id="getRunRulesBynameId" resultType="com.javaweb.system.entity.Rules">
        SELECT  * FROM t_runrules where name =#{name}  and  id !=#{id} limit 1;
    </select>
</mapper>
