package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.common.utils.StringUtils;
import com.javaweb.system.entity.AlarmConfig;
import com.javaweb.system.mapper.AlarmConfigMapper;
import com.javaweb.system.query.AlarmConfigQuery;
import com.javaweb.system.service.IAlarmConfigService;
import com.javaweb.system.workthread.ConfigLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class AlarmConfigServiceImpl extends ServiceImpl<AlarmConfigMapper, AlarmConfig> implements IAlarmConfigService {

        @Autowired
        private AlarmConfigMapper alarmConfigMapper;

        @Autowired
        ConfigLoader configLoader;

        @Override
        public JsonResult getList(AlarmConfigQuery alarmConfigQuery) {

        // 查询条件
        QueryWrapper<AlarmConfig> queryWrapper = new QueryWrapper<>();

        // 监测站
        if (StringUtils.isNotNull(alarmConfigQuery.getHescode())) {
            queryWrapper.eq("hescode", alarmConfigQuery.getHescode());
        }

            // 监测站
            if (StringUtils.isNotEmpty(alarmConfigQuery.getHesname())) {
                queryWrapper.like("hesname", alarmConfigQuery.getHesname());
            }
        queryWrapper.orderByDesc("id");
        int nPage = 1;
        int nLimit = 10;

        if(null!=alarmConfigQuery.getPage()) {
            nPage = alarmConfigQuery.getPage();
        }
        if(null!=alarmConfigQuery.getLimit()) {
            nLimit = alarmConfigQuery.getLimit();
        }
        // 查询分页数据
        IPage<AlarmConfig> page = new Page<>(nPage,nLimit);
        IPage<AlarmConfig> pageData = alarmConfigMapper.selectPage(page, queryWrapper);

        return JsonResult.success(pageData);
    }



    /**
     * 添加或编辑
     *
     * @param entity 实体对象
     * @return
     */
    @Override
    public JsonResult edit(AlarmConfig entity) {
        if (CommonConfig.appDebug) {
            return JsonResult.error("演示环境禁止操作");
        }

        boolean result = this.saveOrUpdate(entity);
        configLoader.loadConfigsAndRules();
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success();
    }
        /**
         * 根据用户ID删除用户
         *
         * @param ids 记录ID
         * @return
         */
        @Override
        public JsonResult deleteByIds(Integer[] ids) {
        if (StringUtils.isNull(ids)) {
            return JsonResult.error("记录ID不能为空");
        }
        // 设置Mark=0

        QueryWrapper   qryWrapper  = new QueryWrapper();
        qryWrapper.in("id",ids);
        boolean result = remove(qryWrapper);
        if (!result) {
            return JsonResult.error();
        }
        return JsonResult.success("删除成功");
    }


    }
