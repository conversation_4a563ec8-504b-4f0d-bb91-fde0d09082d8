package com.javaweb.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.system.entity.Recode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecodeMapper extends BaseMapper<Recode> {

    /**
     * 获取最新5条报警数据
     *
     * @param
     * @return
     */
    List<Recode> getAlarmData();

    Recode getAlarmBycode(@Param("hescode") Integer hescode,
                          @Param("ruleId") Integer ruleId);


    void updateAlarmStatusById(@Param("id") Integer id);
}



