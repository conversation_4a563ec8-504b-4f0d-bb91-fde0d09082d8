package com.javaweb.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.entity.CurrentData;
import com.javaweb.system.mapper.CurrentDataMapper;
import com.javaweb.system.query.CurrentDataQuery;
import com.javaweb.system.service.ICurrentDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 实时监测 服务实现类
 * </p>
 *
 *
 * @since 2022-12-12
 */

@Service
public class CurrentDataServiceImpl extends ServiceImpl<CurrentDataMapper, CurrentData> implements ICurrentDataService
{
    @Autowired
    private CurrentDataMapper currentdataMapper;

    /**
     * 获取所有数据列表
     *
     * @return
     */
    @Override
    public JsonResult getCurrentDataAll(CurrentDataQuery currentdataQuery) {
        QueryWrapper<CurrentData>  queryWrapper = new QueryWrapper<>();
        IPage<CurrentData> page = new Page<>(currentdataQuery.getPage(), currentdataQuery.getLimit());
        IPage<CurrentData> pageData = currentdataMapper.selectPage(page, queryWrapper);
        return JsonResult.success(pageData);
    }



}





