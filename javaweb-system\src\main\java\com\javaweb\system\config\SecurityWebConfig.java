package com.javaweb.system.config;

import com.javaweb.system.security.LoginSecurityInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 安全Web配置类
 * 用于注册安全相关的拦截器
 */
@Configuration
public class SecurityWebConfig implements WebMvcConfigurer {

    @Autowired
    private LoginSecurityInterceptor loginSecurityInterceptor;

    /**
     * 添加拦截器
     *
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册登录安全拦截器
        registry.addInterceptor(loginSecurityInterceptor)
                .addPathPatterns("/login/**", "/api/login/**")  // 拦截登录相关路径
                .excludePathPatterns(
                    "/login/captcha",           // 排除验证码接口
                    "/login/logout",            // 排除退出登录接口
                    "/login/un_auth",           // 排除未授权接口
                    "/login/unauthorized"       // 排除未授权接口
                );
    }
}
