package com.javaweb.system.controller;


import com.javaweb.common.annotation.Log;
import com.javaweb.common.common.BaseController;
import com.javaweb.common.config.CommonConfig;
import com.javaweb.common.enums.LogType;
import com.javaweb.common.utils.JsonResult;
import com.javaweb.system.query.HistoryQuery;
import com.javaweb.system.service.IHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * <p>
 * 历史数据 前端控制器
 * </p>
 *
 *
 * @since 2022-12-27
 */
@RestController
@RequestMapping("/history")
public class HistoryController extends BaseController {

    @Autowired
    private IHistoryService historyService;

    @GetMapping("/index")
    public JsonResult index(HistoryQuery historyQuery){
        return historyService.getList(historyQuery);
    }

    /**
     * 历史数据
     *
     * @param hescode 编号
     * @return
     */
    @GetMapping("/getdata/{hescode}/{dt}")
    public JsonResult getdata(@PathVariable("hescode") String hescode,@PathVariable("dt") String dt)
    {
        return historyService.getHistoryDt(hescode,dt);
    }

    /**
     * 历史数据曲线
     *
     * @param hescode 编号
     * @return
     */
    @GetMapping("/getHistoryquxianDt/{hescode}/{dt}/{field}")
    public JsonResult getHistoryquxianDt(@PathVariable("hescode") String hescode,
                                         @PathVariable("dt") String dt,
                                         @PathVariable("field") String field)
    {
        return historyService.getHistoryquxianDt(hescode,dt,field);
    }


    /**
     * 历史数据曲线
     *
     * @param hescode 编号
     * @return
     */
    @GetMapping("/getMultHesHistoryQuxianDt/{hescode}/{startDt}/{endDt}/{interval}/{field}")
    public JsonResult getMultHesHistoryQuxianDt(@PathVariable("hescode") String hescode,
                                                @PathVariable("startDt") String startDt,
                                                @PathVariable("endDt") String endDt,
                                                @PathVariable("interval") Integer interval,
                                                @PathVariable("field") String field)
    {
        return historyService.getMultHesHistoryQuxianDt(hescode,startDt,endDt,interval,field);
    }
    /**
     * 获取配置表字段
     *
     * @param hescode 编号
     * @return
     */
    @GetMapping("/getHesFieldParam/{hescode}")
    public JsonResult getHesFieldParam(@PathVariable("hescode") int hescode)
    {
        return historyService.getHesFieldParam(hescode);
    }


    @GetMapping("/gethesdatalist/{hescode}")
    public JsonResult gethesdatalist(@PathVariable("hescode") int hescode)
    {
        return historyService.getheslist(hescode);
    }

}
