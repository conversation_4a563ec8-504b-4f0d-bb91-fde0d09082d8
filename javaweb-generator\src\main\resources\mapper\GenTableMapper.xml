<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.generator.mapper.GenTableMapper">

    <resultMap type="com.javaweb.generator.entity.GenTable" id="GenTableResult">
        <id property="id" column="id"/>
        <result property="tableName" column="table_name"/>
        <result property="tableComment" column="table_comment"/>
        <result property="className" column="class_name"/>
        <result property="tplCategory" column="tpl_category"/>
        <result property="packageName" column="package_name"/>
        <result property="moduleName" column="module_name"/>
        <result property="businessName" column="business_name"/>
        <result property="functionName" column="function_name"/>
        <result property="functionAuthor" column="function_author"/>
        <result property="options" column="options"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="note" column="note"/>
        <collection property="columns" javaType="java.util.List" resultMap="GenTableColumnResult"/>
    </resultMap>


    <resultMap type="com.javaweb.generator.entity.GenTableColumn" id="GenTableColumnResult">
        <id property="id" column="id"/>
        <result property="tableId" column="table_id"/>
        <result property="columnName" column="column_name"/>
        <result property="columnComment" column="column_comment"/>
        <result property="columnType" column="column_type"/>
        <result property="javaType" column="java_type"/>
        <result property="javaField" column="java_field"/>
        <result property="isPk" column="is_pk"/>
        <result property="isIncrement" column="is_increment"/>
        <result property="isRequired" column="is_required"/>
        <result property="isInsert" column="is_insert"/>
        <result property="isEdit" column="is_edit"/>
        <result property="isList" column="is_list"/>
        <result property="isQuery" column="is_query"/>
        <result property="queryType" column="query_type"/>
        <result property="htmlType" column="html_type"/>
        <result property="dictType" column="dict_type"/>
        <result property="sort" column="sort"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="selectGenTableVo">
        select id, table_name, table_comment, class_name, tpl_category, package_name, module_name, business_name, function_name, function_author, options, note, create_user, create_time, update_user, update_time, mark from gen_table
    </sql>

    <!-- 获取数据表列表 -->
    <select id="selectGenTableList" parameterType="com.javaweb.generator.entity.GenTable" resultMap="GenTableResult">
        <include refid="selectGenTableVo"/>
        <where>
            <if test="param.tableName != null and param.tableName != ''">
                AND lower(table_name) like lower(concat('%', #{param.tableName}, '%'))
            </if>
            <if test="param.tableComment != null and param.tableComment != ''">
                AND lower(table_comment) like lower(concat('%', #{param.tableComment}, '%'))
            </if>
                AND mark = 1
        </where>
    </select>

    <!-- 获取数据库表 -->
    <select id="selectDbTableList" parameterType="com.javaweb.generator.entity.GenTable" resultMap="GenTableResult">
        select table_name, table_comment, create_time, update_time from information_schema.tables
        where table_schema = (select database())
        AND table_name NOT LIKE 'gen_%'
        AND table_name NOT IN (select table_name from gen_table)
        <if test="param.tableName != null and param.tableName != ''">
            AND lower(table_name) like lower(concat('%', #{param.tableName}, '%'))
        </if>
    </select>

    <!-- 根据表名获取数据表 -->
    <select id="selectDbTableListByNames" resultMap="GenTableResult">
        select table_name, table_comment, create_time, update_time from information_schema.tables
        where table_name NOT LIKE 'gen_%' and table_schema = (select database())
        and table_name in
        <foreach collection="array" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <!-- 插入表结构 -->
    <insert id="insertGenTable" parameterType="com.javaweb.generator.entity.GenTable" useGeneratedKeys="true"
            keyProperty="id">
        insert into gen_table (
        <if test="tableName != null">table_name,</if>
        <if test="tableComment != null and tableComment != ''">table_comment,</if>
        <if test="className != null and className != ''">class_name,</if>
        <if test="tplCategory != null and tplCategory != ''">tpl_category,</if>
        <if test="packageName != null and packageName != ''">package_name,</if>
        <if test="moduleName != null and moduleName != ''">module_name,</if>
        <if test="businessName != null and businessName != ''">business_name,</if>
        <if test="functionName != null and functionName != ''">function_name,</if>
        <if test="functionAuthor != null and functionAuthor != ''">function_author,</if>
        <if test="note != null and note != ''">note,</if>
        <if test="createUser != null and createUser != ''">create_user,</if>
        create_time
        )values(
        <if test="tableName != null">#{tableName},</if>
        <if test="tableComment != null and tableComment != ''">#{tableComment},</if>
        <if test="className != null and className != ''">#{className},</if>
        <if test="tplCategory != null and tplCategory != ''">#{tplCategory},</if>
        <if test="packageName != null and packageName != ''">#{packageName},</if>
        <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
        <if test="businessName != null and businessName != ''">#{businessName},</if>
        <if test="functionName != null and functionName != ''">#{functionName},</if>
        <if test="functionAuthor != null and functionAuthor != ''">#{functionAuthor},</if>
        <if test="note != null and note != ''">#{note},</if>
        <if test="createUser != null and createUser != ''">#{createUser},</if>
        sysdate()
        )
    </insert>

    <!-- 获取表详情信息 -->
    <select id="selectGenTableById" parameterType="Integer" resultMap="GenTableResult">
	    SELECT t.id, t.table_name, t.table_comment, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.options, t.note,
			   c.id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort
		FROM gen_table t
			 LEFT JOIN gen_table_column c ON t.id = c.table_id
		where c.table_id = #{tableId}
	</select>

    <!-- 更新业务表 -->
    <update id="updateGenTable" parameterType="com.javaweb.generator.entity.GenTable">
        update gen_table
        <set>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="tableComment != null and tableComment != ''">table_comment = #{tableComment},</if>
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="functionAuthor != null and functionAuthor != ''">function_author = #{functionAuthor},</if>
            <if test="tplCategory != null and tplCategory != ''">tpl_category = #{tplCategory},</if>
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="businessName != null and businessName != ''">business_name = #{businessName},</if>
            <if test="functionName != null and functionName != ''">function_name = #{functionName},</if>
            <if test="options != null and options != ''">options = #{options},</if>
            <if test="updateUser != null and updateUser != ''">update_user = #{updateUser},</if>
            <if test="note != null">note = #{note},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <!-- 根据表名获取业务表 -->
    <select id="selectGenTableByName" parameterType="String" resultMap="GenTableResult">
	    SELECT t.id, t.table_name, t.table_comment, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.options, t.note,
			   c.id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort
		FROM gen_table t
			 LEFT JOIN gen_table_column c ON t.id = c.table_id
		where t.table_name = #{tableName}
	</select>

</mapper>
