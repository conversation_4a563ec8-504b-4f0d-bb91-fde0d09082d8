package com.javaweb.calculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javaweb.calculate.entity.AllHesData;
import com.javaweb.calculate.entity.StatisticsDay;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StatisticsDayMapper extends BaseMapper<StatisticsDay> {

    int getNumByDt(@Param("hescode") String hescode,@Param("dt") String dt);
}
