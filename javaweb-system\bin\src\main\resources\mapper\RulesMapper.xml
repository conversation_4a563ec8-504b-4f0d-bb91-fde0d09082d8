<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.system.mapper.RulesMapper">
    <select id="getRulesLst" resultType="com.javaweb.system.entity.Rules">
        SELECT  * FROM t_alarmrules  order by id asc;
    </select>


    <select id="getRulesByname" resultType="com.javaweb.system.entity.Rules">
        SELECT  * FROM t_alarmrules where name =#{name}  limit 1;
    </select>

    <select id="getRulesBynameId" resultType="com.javaweb.system.entity.Rules">
        SELECT  * FROM t_alarmrules where name =#{name}  and  id !=#{id} limit 1;
    </select>

</mapper>
