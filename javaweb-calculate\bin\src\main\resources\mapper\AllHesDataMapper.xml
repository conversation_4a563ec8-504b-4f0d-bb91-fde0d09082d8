<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javaweb.calculate.mapper.AllHesDataMapper">
        <!-- 获取实时数据 -->
        <select id="getAllHesData" resultType="com.javaweb.calculate.entity.AllHesData">
                SELECT * FROM t_allhesdata;
        </select>

        <!-- 获取个数 -->
        <select id="getNumByHescode" resultType="Integer">
                SELECT count(id) FROM t_allhesdata where hescode=#{hescode};
        </select>


</mapper>
