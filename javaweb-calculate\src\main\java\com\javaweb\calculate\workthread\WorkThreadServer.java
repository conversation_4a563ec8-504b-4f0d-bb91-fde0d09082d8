package com.javaweb.calculate.workthread;

import com.javaweb.calculate.entity.StatisticsDay;
import com.javaweb.calculate.entity.StatisticsHour;
import com.javaweb.calculate.mapper.AllHesDataMapper;
import com.javaweb.calculate.mapper.StatisticsDayMapper;
import com.javaweb.calculate.mapper.StatisticsHourMapper;
import com.javaweb.common.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.sql.DataSource;
import java.sql.*;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableScheduling
public class WorkThreadServer {
    @Autowired
    DataSource dataSource;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private AllHesDataMapper allHesDataMapper;

    @Autowired
    private StatisticsDayMapper statisticsDayMapper;

    @Autowired
    private StatisticsHourMapper statisticsHourMapper;

    /*
     *   主界面数据采集
     *   1.获取换热站信息列表
     *   2.循环根据站的分区 查询对应的字段，最后一条数据
     *   3.查看采集表是否存在当前的换热站的数据，若存在则则更新，否则插入
     *   4.根据分区所对应的字段，组合成插入或者更新语句，并执行
     */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay=3000,fixedDelay=60000)
    public void AllHesDataCalc() {
        try {
            PreparedStatement pStatement = null;
            ResultSet rSet = null;

            try (Connection connection = dataSource.getConnection()){
                String sql="select Name,hescode,runMode,heatingType,equipmentNum from t_hes where isused=1";
                pStatement = connection.prepareStatement(sql);
                rSet = pStatement.executeQuery();
                while(rSet.next())
                {
                    String hesname =rSet.getString("Name");
                    String hescode =rSet.getString("hescode");
                    //System.out.println("============执行====hescode="+hescode+"=======");
                    Integer equipmentNum =Integer.valueOf(rSet.getString("equipmentNum"));
                    Integer runMode =Integer.valueOf(rSet.getString("runMode"));
                    Integer heatingType =Integer.valueOf(rSet.getString("heatingType"));
                    String strtable="T_Hes_data_"+ hescode;
                    String strsqlfields ="collectdt  as collect_dt," +
                            "F_S_F  as fsf," +
                            "F_B_WP_FREQUENCY1 as fre1," +
                            "F_B_WP_FREQUENCY2 as fre2," +
                            "F_S_P1 as fsp," +
                            "F_B_P1 as fbp," +
                            "F_S_T1 as fst," +
                            "F_B_T1 as fbt," +
                            "S_S_T1 as h_sst," +
                            "S_B_T1 as h_sbt";

                    if ((equipmentNum == 2) || (equipmentNum == 21))
                    {
                        strsqlfields = strsqlfields+"," +
                                "LOW_S_S_T1 as l_sst," +
                                "LOW_S_B_T1 as l_sbt";
                    }
                    else if (equipmentNum == 3)
                    {
                        strsqlfields = strsqlfields+"," +
                        "LOW_S_S_T1 as l_sst," +
                        "LOW_S_B_T1 as l_sbt,"+
                        "MID_S_S_T1 as mid_sst,"+
                        "MID_S_B_T1 as mid_sbt";
                    }else if (equipmentNum == 4)
                    {
                        strsqlfields = strsqlfields+"," +
                        "LOW_S_S_T1 as l_sst," +
                        "LOW_S_B_T1 as l_sbt,"+
                        "MID_S_S_T1 as mid_sst,"+
                        "MID_S_B_T1 as mid_sbt,"+
                        "FOUR_S_S_T1 as four_sst,"+
                        "FOUR_S_B_T1 as four_sbt";
                    }
                    String strsql = "select " + strsqlfields +" from " + strtable + " order by id desc limit 0,1";
                    pStatement = connection.prepareStatement(strsql);
                    ResultSet rSet1 = pStatement.executeQuery();
                    if(rSet1 != null)
                    {
                        while(rSet1.next())
                        {
                            int num=allHesDataMapper.getNumByHescode(hescode);
                            // 调用方法获取对应的字符串
                            String aht = getHeatingTypeString(heatingType);
                            String arm = getRunModeString(runMode);
                            if(num>0)
                            {
                                UpdateSQLToDB(connection,rSet1,hescode, hesname, equipmentNum, arm, aht);
                            }else
                            {
                                WriteSQLToDB(connection,rSet1,hescode, hesname, equipmentNum, arm, aht);
                            }
                        }
                    }
                }
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String getHeatingTypeString(int heatingType) {
        switch (heatingType) {
            case 0:
                return "未知";
            case 1:
                return "暖气片";
            case 2:
                return "地暖";
            case 3:
                return "暖气片+地暖";
            case 4:
                return "风机盘管";
            default:
                return "无效类型";
        }
    }

    private static String getRunModeString(int runMode) {
        switch (runMode) {
            case 0:
                return "手动";
            case 1:
                return "自动";
            default:
                return "无效模式";
        }
    }

    public void UpdateSQLToDB(Connection connection ,ResultSet rSet,String hescode,String hesname,Integer equipmentNum,String runMode,String heatingType)
    {
        List<String> commonFields = Arrays.asList(
                "collect_dt", "fsf", "fre1", "fre2", "fsp", "fbp", "fst", "fbt", "h_sst", "h_sbt"
        );

        List<String> additionalFields = new ArrayList<>(commonFields);
        if ((equipmentNum.equals(2)) || (equipmentNum.equals(21))) {
            additionalFields.add("l_sst");
            additionalFields.add("l_sbt");
        }else if(equipmentNum.equals(3))
        {
            additionalFields.add("l_sst");
            additionalFields.add("l_sbt");
            additionalFields.add("mid_sst");
            additionalFields.add("mid_sbt");

        }else if(equipmentNum.equals(4)){
            additionalFields.add("l_sst");
            additionalFields.add("l_sbt");
            additionalFields.add("mid_sst");
            additionalFields.add("mid_sbt");
            additionalFields.add("four_sst");
            additionalFields.add("four_sbt");
        }
        String sqlStr = buildUpdateSQL("t_allHesdata", additionalFields, "hescode");
            try (PreparedStatement updateStatement = connection.prepareStatement(sqlStr)) {
                int parameterIndex = 1;
                // Set parameters for fields that are in rSet
                for (String fieldName : additionalFields)
                {
                    String val=rSet.getString(fieldName);
                    updateStatement.setString(parameterIndex++, rSet.getString(fieldName));
                }
                // Set parameters for fields that are not in rSet
                updateStatement.setString(parameterIndex++, hescode);
                updateStatement.setString(parameterIndex++, hesname);
                updateStatement.setString(parameterIndex++, heatingType);
                updateStatement.setString(parameterIndex++, runMode);
                updateStatement.setInt(parameterIndex++, equipmentNum);
                // Set the WHERE clause parameter
                updateStatement.setString(parameterIndex, hescode);
                updateStatement.executeUpdate();
            }catch (SQLException e) {
                // 更详细的错误处理
            }
    }
    //拼接字符串
    private String buildUpdateSQL(String tableName, List<String> fields, String primaryKey) {
        StringBuilder sql = new StringBuilder("UPDATE " + tableName + " SET ");
        for (int i = 0; i < fields.size(); i++) {
            sql.append(fields.get(i)).append("=?");
            if (i < fields.size() - 1) {
                sql.append(",");
            }
        }
        sql.append(", hescode=?, hesname=?, heat_type=?, control_mode=?,num=? ");
        sql.append(" WHERE ").append(primaryKey).append("=?");
        return sql.toString();
    }

    //执行采集表的插入语句
    public void WriteSQLToDB(Connection connection,ResultSet rSet,String hescode,String hesname,Integer equipmentNum,String runMode,String heatingType)
    {
        List<String> commonFields = Arrays.asList(
                "collect_dt", "fsf", "fre1", "fre2", "fsp", "fbp",
                "fst", "fbt", "h_sst", "h_sbt"
        );
        List<String> additionalFields = new ArrayList<>(commonFields);
        if ((equipmentNum.equals("2")) || (equipmentNum.equals("21"))) {
            additionalFields.add("l_sst");
            additionalFields.add("l_sbt");
        }else if(equipmentNum.equals("3"))
        {
            additionalFields.add("mid_sst");
            additionalFields.add("mid_sbt");
        }else if(equipmentNum.equals("4")){
            additionalFields.add("four_sst");
            additionalFields.add("four_sbt");
        }
        String sqlStr = InsertSQLToDB("t_allHesdata", additionalFields);
        try (PreparedStatement insertStatement = connection.prepareStatement(sqlStr)) {
            int parameterIndex = 1;
            // Set parameters for fields that are in rSet
            insertStatement.setString(parameterIndex++, hescode);
            insertStatement.setString(parameterIndex++, hesname);
            insertStatement.setString(parameterIndex++, heatingType);
            insertStatement.setString(parameterIndex++, runMode);
            insertStatement.setInt(parameterIndex++, equipmentNum);
            for (String fieldName : additionalFields)
            {
                insertStatement.setString(parameterIndex++, rSet.getString(fieldName));
            }
            insertStatement.executeUpdate();
        }catch (SQLException e) {
            e.printStackTrace();
        }

    }
    //组合采集表的插入语句
    private String InsertSQLToDB(String tableName, List<String> fields)
    {
        StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
        sql.append("hescode,hesname, heat_type,control_mode,num,");

        for (int i = 0; i < fields.size(); i++) {
            sql.append(fields.get(i));
            if (i < fields.size() - 1) {
                sql.append(",");
            }
        }
        sql.append(") VALUES (");
        for (int i = 0; i < fields.size()+5; i++) {
            sql.append("?");
            if (i < (fields.size()+5) - 1) {
                sql.append(",");
            }
        }
        sql.append(")");
        return sql.toString();
    }
    /*
    *
    *  Q=1000*Q0/A0*(A1+(A0-A1)*0.3)/A1*(Tn-Tw)/21.4
        式中：
        Q：理论天能耗，W/m2
        Q0：换热站设计负荷，kW
        A0：换热站设计面积，m2
        A1：换热站实际供热面积，m2
        Tn：室内实际日平均温度，℃
        Tw：室外实际日平均温度，℃
    * */
    public void updateTheoryEnergy() {
        String sqlSelect = "SELECT hescode, dt, avgT, area, energy FROM t_hes_statistics";
        String sqlDesign = "SELECT hescode, design_load, design_area FROM t_hes GROUP BY hescode";
        String sqlUpdate = "UPDATE t_hes_statistics SET theory_energy = ? WHERE hescode = ? AND dt = ?";

        Map<Integer, Double[]> designValuesCache = new HashMap<>();

        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false); // 开始事务

            // 查询设计负荷和设计面积并存入缓存
            try (PreparedStatement stmtDesign = connection.prepareStatement(sqlDesign);
                 ResultSet rsDesign = stmtDesign.executeQuery()) {
                while (rsDesign.next()) {
                    int hescode = rsDesign.getInt("hescode");
                    double designLoad = rsDesign.getDouble("design_load");
                    double designArea = rsDesign.getDouble("design_area");
                    designValuesCache.put(hescode, new Double[]{designLoad, designArea});
                }
            }

            // 查询统计数据并进行理论能耗计算和更新
            try (PreparedStatement stmtSelect = connection.prepareStatement(sqlSelect);
                 ResultSet rs = stmtSelect.executeQuery();
                 PreparedStatement stmtUpdate = connection.prepareStatement(sqlUpdate)) {

                while (rs.next()) {
                    Integer hescode = rs.getInt("hescode");
                    String dt = rs.getString("dt");
                    double Tw = rs.getDouble("avgT");
                    double A1 = rs.getDouble("area");
                    double Tn = calcAvgIndoorT(connection, hescode, dt);

                    Double[] designValues = designValuesCache.get(hescode);
                    double Q0 = designValues[0];
                    double A0 = designValues[1];

                    double Q = 1000 * Q0 / A0 * (A1 + (A0 - A1) * 0.3) / A1 * (Tn - Tw) / 21.4;
                    if (!Double.isNaN(Q) && !Double.isInfinite(Q)) {
                        stmtUpdate.setDouble(1, Q);
                        stmtUpdate.setInt(2, hescode);
                        stmtUpdate.setString(3, dt);
                        stmtUpdate.executeUpdate();
                    } else {
                        // 处理无效的 Q 值，例如记录日志、跳过更新或设置为默认值
                       // System.err.println("Invalid Q value for hescode: " + hescode + " and dt: " + dt);
                    }
                }
            }

            connection.commit(); // 提交事务
        } catch (SQLException e) {
            // 处理异常，例如回滚事务或记录日志
            e.printStackTrace();
        }
    }


    public double calcAvgIndoorT(Connection connection, Integer hescode, String dt) {
        String sql = "SELECT AVG(install_t) as avg_temp FROM t_hes_indoort WHERE hescode = ? AND install_dt >= ? and  install_dt<?";
        double avgTemperature = 23; // 默认值

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, hescode);
            stmt.setString(2, dt+" 00:00:00");
            stmt.setString(3, dt+" 23:59:59");
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    avgTemperature = rs.getDouble("avg_temp");
                    if (rs.wasNull()) {
                        avgTemperature = 23; // 如果查询结果为空，则使用默认值
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace(); // 在实际应用中，你可能需要更合适的错误处理
        }

        return avgTemperature;
    }
    //计算每小时的平均室内温度
    public double calcAvgIndoorTForSpecificHour(Connection connection, Integer hescode, String dt, int hour) {
        String sql = "SELECT AVG(install_t) as avg_temp FROM t_hes_indoort " +
                "WHERE hescode = ? AND install_dt >= ? AND install_dt < ?";
        double avgTemperature = 23; // 默认值

        // 构建查询的起始和结束时间
        String startTime = String.format("%s %02d:00:00", dt, hour);
        String endTime = String.format("%s %02d:59:59", dt, hour);

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, hescode);
            stmt.setString(2, startTime);
            stmt.setString(3, endTime);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    avgTemperature = rs.getDouble("avg_temp");
                    if (rs.wasNull()) {
                        avgTemperature = 23; // 如果查询结果为空，则使用默认值
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace(); // 在实际应用中，你可能需要更合适的错误处理
        }

        return avgTemperature;
    }

    /*
     *
     * 能耗计算（天）
     * 1.查询换热站编号列表
     * 2.计算间隔时间，判断间隔时间
     * 3.根据站号和日期查询，判断指定日期是否已经计算
     * 4.若没有计算，则计算当前天的能耗；
     * 5.能耗公式   累计热量 *1000000000*rate/面积/86400
     *
     *
     * Q=1000*Q0/A0*(A1+(A0-A1)*0.3)/A1*(Tn-Tw)/21.4
        式中：
        Q：理论天能耗，W/m2
        Q0：换热站设计负荷，kW
        A0：换热站设计面积，m2
        A1：换热站实际供热面积，m2
        Tn：室内实际日平均温度，℃
        Tw：室外实际日平均温度，℃
     */
    @Async("threadPoolTaskExecutor")
    @Scheduled(cron = "0 0 1 * * ?")// 每天凌晨 1 点执行
    //@Scheduled(initialDelay=10000,fixedDelay = Long.MAX_VALUE)
    public void startCalcThread3()
    {
        try {
            ResultSet rSet = null;
            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
            Date startCalcDT = formatter1.parse("2024-11-15");
            // 创建一个 Calendar 实例，并设置为当前时间
            Calendar calendar = Calendar.getInstance();
            Date endCalcDT = calendar.getTime();
            // 将 endCalcDT 减去一天
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            endCalcDT = calendar.getTime();  // 调整后的当前时间减去一天
            int nDay=calculateDaysBetween(startCalcDT,endCalcDT);
            DecimalFormat df = new DecimalFormat("#.000");
            // 使用 Calendar 来处理日期
            // 定义日期格式
            //SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            try (Connection connection = dataSource.getConnection()){

                String sql="select a.hescode ,a.design_load,a.design_area,a.heat_rate,b.heara from t_hes a LEFT JOIN t_hes_Year_HArea  b ON a.hescode=b.hescode" +
                        " AND  b.id=(select max(id) from t_hes_Year_HArea where hescode=a.hescode)";
                PreparedStatement pStatement = connection.prepareStatement(sql);
                rSet = pStatement.executeQuery();
                while(rSet.next())
                {
                    double heatrate =Double.valueOf(rSet.getString("heat_rate"));
                    String hescode =rSet.getString("hescode");
                    String heara =rSet.getString("heara");
                    double A0 =rSet.getDouble("design_area");
                    double Q0 =rSet.getDouble("design_load");

                    if(heara==null || heara.isEmpty())
                    {
                        heara="1.0";
                    }

                    calendar.setTime(startCalcDT);
                    double area=Double.valueOf(heara)*10000;
                    for(int i=0;i<nDay;i++)
                    {
                        // 在每次迭代中增加一天
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                        // 获取新的日期
                        Date newDate = calendar.getTime();

                        String dt= formatter1.format(newDate);
                        // 打印新的日期
                        int num=statisticsDayMapper.getNumByDt(hescode,dt);
                        if(num==0)
                        {
                            double Tn = calcAvgIndoorT(connection, Integer.valueOf(hescode), dt);
                            double F_S_C_H =0.0;
                            double F_S_C_F =0.0;
                            double unit_h=0.0;
                            double energy=0.0;
                            double avgT=0.0;
                            String strdatatb="t_hes_data_"+hescode;
                            String strsql = "SELECT " +
                                    "   (MAX(CAST(F_S_C_H AS DECIMAL(18,2))) - MIN(CAST(F_S_C_H AS DECIMAL(18,2)))) AS FSCH, " +
                                    "   (MAX(CAST(F_S_C_F AS DECIMAL(18,2))) - MIN(CAST(F_S_C_F AS DECIMAL(18,2)))) AS FSCF, " +
                                    "    wsd.avgT " +
                                    " FROM " + strdatatb +" hes"+
                                    " JOIN " +
                                    " (SELECT AVG(T) as avgT FROM t_weatherstationdata WHERE CollectDT >= ? AND CollectDT <= ? ) wsd "+
                                    " ON 1 = 1"+
                                    " WHERE hes.CollectDT >= ? " +
                                    " AND hes.CollectDT <= ? " +
                                    " AND CAST(F_S_C_H AS DECIMAL(18,2)) > 0";
                            pStatement = connection.prepareStatement(strsql);
                            pStatement.setString(1, dt + " 00:00:00");
                            pStatement.setString(2, dt + " 23:59:59");
                            pStatement.setString(3, dt + " 00:00:00");
                            pStatement.setString(4, dt + " 23:59:59");
                            ResultSet rSet1 = pStatement.executeQuery();
                            if(rSet1 != null)
                            {
                                StatisticsDay statisticsDay=new StatisticsDay();
                                while(rSet1.next())
                                {
                                    String FSCH=rSet1.getString("FSCH");
                                    if (FSCH == null || FSCH.isEmpty()) {
                                        F_S_C_H = 0.0;
                                    } else {
                                        F_S_C_H = Double.valueOf(FSCH);
                                    }
                                    String FSCF=rSet1.getString("FSCF");
                                    if (FSCF == null || FSCF.isEmpty()) {
                                        F_S_C_F = 0.0;
                                    } else {
                                        F_S_C_F = Double.valueOf(FSCF);
                                    }
                                    unit_h=F_S_C_H/area/10000*1000;
                                    energy=F_S_C_H *1000000000*heatrate/area/86400;
                                    String t=rSet1.getString("avgT");
                                    if (t == null || t.isEmpty()) {
                                        avgT = 0.0;
                                    } else {
                                        avgT=Double.valueOf(t);
                                    }
                                    //理论日能耗
                                    double Q = 1000 * Q0 / A0 * (area + (A0 - area) * 0.3) / area * (Tn - avgT) / 21.4;
                                    if (!Double.isNaN(Q) && !Double.isInfinite(Q))
                                    {
                                        statisticsDay.setTheoryEnergy(Q);
                                    }else
                                    {
                                        statisticsDay.setTheoryEnergy(0.0);
                                    }
                                    statisticsDay.setHescode(hescode);
                                    statisticsDay.setDt(dt);
                                    statisticsDay.setIscalc(1);
                                    statisticsDay.setFSCH(F_S_C_H);
                                    statisticsDay.setFSCF(F_S_C_F);
                                    statisticsDay.setEnergy(energy);
                                    statisticsDay.setAvgt(avgT);
                                    statisticsDay.setArea(area);
                                    statisticsDay.setUnitH(unit_h);
                                 }
                                //计算理论能耗
                                statisticsDayMapper.insert(statisticsDay);
                            }
                        }

                    }
                }
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 计算两个日期之间的天数差。
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 两个日期之间的天数差
     */
    public static int calculateDaysBetween(Date date1, Date date2) {
        // 使用毫秒进行计算
        long diffInMillies = Math.abs(date2.getTime() - date1.getTime());
        // 转换为天数
        // 转换为天数
        long days = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

        // 将 long 转换为 int
        if (days > Integer.MAX_VALUE || days < Integer.MIN_VALUE) {
            throw new ArithmeticException("Days difference is out of the range of an int.");
        }

        return (int) days;
    }

    /**
     * 获取今年的第一天
     * @return 今年的第一天
     */
    public static Date getCurrentYearDate()
    {
        // 获取当前时间的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 设置月份为一年中的第一个月（注意：月份从0开始计数，所以1月是0）
        calendar.set(Calendar.MONTH, Calendar.JANUARY);

        // 设置日期为当月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 将小时、分钟、秒和毫秒设置为0，以确保得到的是当天的开始时刻
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取Date对象
        Date firstDayOfYear = calendar.getTime();
        return  firstDayOfYear;
    }

    /*
     *
     * 能耗计算--小时
     * 1.查询换热站编号列表
     * 2.计算间隔时间，判断间隔时间
     * 3.根据站号和日期查询，判断指定日期是否已经计算
     * 4.若没有计算，则计算当前天的能耗；
     * 5.能耗公式   累计热量 *1000000000*rate/面积/86400
     */
    @Async("threadPoolTaskExecutor")
    @Scheduled(cron = "0 0 2 * * ?")
    //@Scheduled(initialDelay=3000,fixedDelay = Long.MAX_VALUE)
    public void startCalcThread4() {
        Connection connection = null;
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        try{
            //Date startCalcDT = getCurrentYearDate();
            SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
            Date startCalcDT = formatter1.parse("2024-01-01");
            Calendar calendar = Calendar.getInstance();
            Date endCalcDT = calendar.getTime();
            // 将 endCalcDT 减去一天
            calendar.add(Calendar.DAY_OF_YEAR, -1);
            endCalcDT = calendar.getTime();  // 调整后的当前时间减去一天
            int nDay = calculateDaysBetween(startCalcDT, endCalcDT);
            // 用于格式化数值，保留两位小数
            DecimalFormat df = new DecimalFormat("#.000");
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat hourFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            try {
                connection = dataSource.getConnection();
                String sql = "SELECT a.hescode,a.heat_rate, a.design_load,a.design_area,b.heara FROM t_hes a LEFT JOIN (SELECT hescode, MAX(id) AS max_id FROM t_hes_Year_HArea GROUP BY hescode) sub ON a.hescode = sub.hescode LEFT JOIN t_hes_Year_HArea b ON b.id = sub.max_id AND b.hescode = a.hescode";
                pStatement = connection.prepareStatement(sql);
                rSet = pStatement.executeQuery();
                while (rSet.next())
                {
                    double heatRate = Double.valueOf(rSet.getString("heat_rate"));
                    String hesCode = rSet.getString("hescode");
                    double A0 =rSet.getDouble("design_area");
                    double Q0 =rSet.getDouble("design_load");
                    String heara = rSet.getString("heara");
                    if (heara==null || heara.isEmpty()) {
                        heara = "1.0";
                    }
                    double area = Double.valueOf(heara)*10000;
                    for (int i = 0; i <= nDay; i++)
                    {
                        calendar.setTime(startCalcDT);
                        calendar.add(Calendar.DAY_OF_MONTH, i);
                        Date currentDate = calendar.getTime();
                        String dt = formatter.format(currentDate);
                        // 打印新的日期
                        int num=statisticsHourMapper.getNumByDt(hesCode,dt);
                        if(num==0)
                        {
                            //查询每天每小时的累计 F_S_C_H
                            String hourlySql = "SELECT hes.hour_start,hes.FSCH,wsd.avgT FROM (" +
                                    "SELECT DATE_FORMAT(CollectDT, '%Y-%m-%d %H:00:00') AS hour_start, "
                                    + "   (MAX(CAST(F_S_C_H AS DECIMAL(18,2))) - MIN(CAST(F_S_C_H AS DECIMAL(18,2)))) AS FSCH "
                                    + "FROM " + "t_hes_data_" + hesCode + " hes "
                                    + "WHERE CollectDT >= ? AND CollectDT <= ? AND CAST(F_S_C_H AS DECIMAL(18,2)) > 0 "
                                    + "GROUP BY hour_start ) hes " +
                                    "  LEFT JOIN (SELECT " +
                                    "   DATE_FORMAT(CollectDT, '%Y-%m-%d %H:00:00') AS hour_start, AVG(T) AS avgT" +
                                    "   FROM t_weatherstationdata wsd" +
                                    "   WHERE CollectDT >= ? AND CollectDT <= ?" +
                                    "   GROUP BY hour_start) wsd" +
                                    "   ON hes.hour_start = wsd.hour_start;";
                            pStatement = connection.prepareStatement(hourlySql);
                            pStatement.setString(1, dt + " 00:00:00");
                            pStatement.setString(2, dt + " 23:59:59");
                            pStatement.setString(3, dt + " 00:00:00");
                            pStatement.setString(4, dt + " 23:59:59");
                            ResultSet rSet1 = pStatement.executeQuery();
                            // 初始化每小时的能耗数组
                            String[] hourlyEnergy = new String[24];
                            for (int hour = 0; hour < 24; hour++) {
                                hourlyEnergy[hour] = "0.0~0.0~0.0~0.0";
                            }
                            // 填充每小时的能耗
                            while (rSet1.next())
                            {
                                String hourStart = rSet1.getString("hour_start");

                                double fsch = rSet1.getDouble("FSCH");
                                String avgT =  rSet1.getString("avgT");

                                if(avgT==null || avgT.isEmpty())
                                {
                                    avgT="0.0";
                                }else
                                {
                                    avgT = df.format(rSet1.getDouble("avgT"));
                                }

                                int hour = hourFormatter.parse(hourStart).getHours();
                                double energy = fsch * 1000000000 * heatRate / area / 3600; // 每小时能耗
                                // 保留两位小数
                                energy = Double.parseDouble(df.format(energy));
                                String val=avgT+"~"+df.format(fsch) + "~" + String.valueOf(energy);


                                double Tn =calcAvgIndoorTForSpecificHour(connection, Integer.valueOf(hesCode), dt, hour);
                                double Tw=Double.valueOf(avgT);
                                double Q = 1000 * Q0 / A0 * (area + (A0 - area) * 0.3) / area * (Tn - Tw) / 21.4;

                                if (!Double.isNaN(Q) && !Double.isInfinite(Q)) {
                                    DecimalFormat df1 = new DecimalFormat("#.###");
                                    String formattedQ = df1.format(Q);
                                    hourlyEnergy[hour] =val+"~"+String.valueOf(formattedQ);
                                } else {
                                    hourlyEnergy[hour] =val+"~0.0";
                                }
                            }
                            // 插入统计数据
                            StatisticsHour statisticsHour = new StatisticsHour();
                            statisticsHour.setHescode(hesCode);
                            statisticsHour.setDt(dt);
                            statisticsHour.setArea(area);
                            statisticsHour.setIscalc(1);
                            //设置每小时的能耗
                            for (int hour=0; hour < 24; hour++)
                            {
                                statisticsHour.setHourlyEnergy(hour, hourlyEnergy[hour]);
                            }
                            // 执行插入操作
                            statisticsHourMapper.insert(statisticsHour);
                        }
                    }
                }
            } catch (SQLException | ParseException e) {
                e.printStackTrace();
            } finally {
                if (connection != null) {
                    try {
                        connection.close();
                    } catch (Exception ignore) {
                    }
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    /*
     *
     * 流量控制算法
     * 1.得到启用的hes列表  hescode,g_rate
     * 2.得到面积 A
     * 3.设置qi=40
     * 4.得到当天当时的小时设定的温度Tn
     * 5.得到最后一天温度
     * 6.得到换热站表的供回水温度
     * 7. 30分钟执行一次
     *
     * 若是t_hes_rule 为空，默认值为20
     */
    @Async("threadPoolTaskExecutor")
    @Scheduled(initialDelay=10000,fixedDelay=1800000)
    public void startCalcThread5()
    {
        PreparedStatement pStatement = null;
        ResultSet rSet = null;
        // 获取当前时间
        LocalTime now = LocalTime.now();
        // 获取当前小时
        int currentHour = now.getHour();
        double Tw=0.0;
        double Total_G=0.0;
        double Gmin=0.0;
        try (Connection connection = dataSource.getConnection()) {
            //查询参数
            String sqlstr="SELECT " +
                    "(SELECT T FROM T_WeatherStationData ORDER BY id DESC LIMIT 1) AS Tw," +
                    "(SELECT min_g FROM T_BoilerParam ORDER BY id DESC LIMIT 1)  AS min_g," +
                    "(SELECT SUM(G)  FROM T_Hes_CalcParam4) AS sumG";

            pStatement = connection.prepareStatement(sqlstr);
            ResultSet rSet2 = pStatement.executeQuery();
            while (rSet2.next())
            {
                Tw = rSet2.getDouble("Tw");
                Total_G = rSet2.getDouble("sumG");
                Gmin = rSet2.getDouble("min_g");
            }
            String sql = "select hescode,g_rate from t_hes where isUsed =1 order by hescode desc";
            pStatement = connection.prepareStatement(sql);
            rSet = pStatement.executeQuery();
            while (rSet.next())
            {
                double gRate = Double.valueOf(rSet.getString("g_rate"));
                int hesCode = rSet.getInt("hescode");
                if(hesCode==32)
                {
                    System.out.println(hesCode);
                }
                String strtable="T_Hes_data_"+ hesCode;
                String strsql = "SELECT COALESCE(r.control_t, 20) + COALESCE(r.offset_t, 0) AS tn, " +
                        "       hya.heara, " +
                        "       cp4.G, " +
                        "       d.F_S_T1, " +
                        "       d.F_B_T1 " +
                        "FROM (SELECT 0 AS control_t, 0 AS offset_t, ? AS hescode, ? AS run_h) r_default " +
                        "LEFT JOIN t_hes_rule r ON r.hescode = r_default.hescode AND r.run_h = r_default.run_h " +
                        "CROSS JOIN (SELECT heara FROM T_Hes_Year_HArea WHERE hescode = ? ORDER BY id DESC LIMIT 1) hya " +
                        "CROSS JOIN (SELECT G FROM T_Hes_CalcParam4 WHERE hescode = ? ORDER BY id DESC LIMIT 1) cp4 " +
                        "CROSS JOIN (SELECT F_S_T1, F_B_T1 FROM " + strtable + " ORDER BY id DESC LIMIT 1) d ";

                pStatement = connection.prepareStatement(strsql);
                pStatement.setInt(1, hesCode);
                pStatement.setInt(2, currentHour);
                pStatement.setInt(3, hesCode);
                pStatement.setInt(4, hesCode);
                ResultSet rSet1 = pStatement.executeQuery();
                while (rSet1.next())
                {
                    double qi=40;
                    double A = rSet1.getDouble("heara");
                    double Tn = rSet1.getDouble("tn");
                    double Tg = rSet1.getDouble("F_S_T1");
                    double Th = rSet1.getDouble("F_B_T1");
                    double Q=A*qi*(Tn-Tw)/2.14;
                    double G=0.0;
                    if (Tg-Th > 15)
                    {
                        G= 0.86*Q/(Tg - Th);
                    }else
                    {
                        G=0.05*Q;
                    }
                    saveG(connection,hesCode,G);
                    if(Total_G < Gmin)
                    {
                        double clacG = rSet1.getDouble("G");
                               G=clacG*Gmin/Total_G;
                               saveG(connection,hesCode,G);
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void saveG(Connection conn, int hescode, double G) throws SQLException {
        String updateSql = "UPDATE T_Hes_CalcParam4 SET G=?, dt=? WHERE hescode=?";
        String insertSql = "INSERT INTO T_Hes_CalcParam4 (dt, hescode, G) VALUES (?, ?, ?)";

        try (PreparedStatement updateStmt = conn.prepareStatement(updateSql);
             PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
            updateStmt.setDouble(1, G);
            updateStmt.setString(2, getCurrentDateTime());
            updateStmt.setInt(3, hescode);
            int affectedRows = updateStmt.executeUpdate();
            if (affectedRows == 0)
            { // 如果没有更新任何行，则插入新记录
                insertStmt.setString(1, getCurrentDateTime());
                insertStmt.setInt(2, hescode);
                insertStmt.setDouble(3, G);
                insertStmt.executeUpdate();
            }
        }
    }

    private String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        String formattedDateTime = sdf.format(System.currentTimeMillis());
        return formattedDateTime;
    }
}


